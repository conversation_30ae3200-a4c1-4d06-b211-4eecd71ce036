<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP命令执行函数启用指南</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        h1, h2 {
            color: #333;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
        
        .step {
            background: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin: 15px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="card">
        <h1>🔧 PHP命令执行函数启用指南</h1>
        <p>您的服务器当前禁用了所有PHP命令执行函数，这是出于安全考虑的常见设置。</p>
        <p><a href="index.php" class="btn">← 返回主界面</a> <a href="debug.php" class="btn">调试工具</a></p>
    </div>

    <div class="card">
        <h2>📋 当前状态检查</h2>
        <?php
        $functions = ['exec', 'shell_exec', 'system', 'passthru'];
        $disabled_functions = explode(',', ini_get('disable_functions'));
        $disabled_functions = array_map('trim', $disabled_functions);
        
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #f8f9fa;'><th style='padding: 10px; border: 1px solid #ddd;'>函数名</th><th style='padding: 10px; border: 1px solid #ddd;'>状态</th><th style='padding: 10px; border: 1px solid #ddd;'>说明</th></tr>";
        
        foreach ($functions as $func) {
            $exists = function_exists($func);
            $disabled = in_array($func, $disabled_functions);
            
            echo "<tr>";
            echo "<td style='padding: 10px; border: 1px solid #ddd;'><code>$func()</code></td>";
            
            if ($exists && !$disabled) {
                echo "<td style='padding: 10px; border: 1px solid #ddd; color: green;'>✅ 可用</td>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>函数正常可用</td>";
            } elseif ($disabled) {
                echo "<td style='padding: 10px; border: 1px solid #ddd; color: red;'>❌ 被禁用</td>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>在disable_functions中被禁用</td>";
            } else {
                echo "<td style='padding: 10px; border: 1px solid #ddd; color: orange;'>⚠️ 不存在</td>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>PHP版本不支持此函数</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div class='info'>";
        echo "<strong>disable_functions 设置:</strong><br>";
        echo "<code>" . (ini_get('disable_functions') ?: '(无禁用函数)') . "</code>";
        echo "</div>";
        ?>
    </div>

    <div class="card">
        <h2>🛠️ 启用方法</h2>
        
        <div class="warning">
            <strong>⚠️ 安全警告</strong><br>
            启用命令执行函数会增加安全风险。请确保您了解相关风险，并采取适当的安全措施。
        </div>

        <h3>方法1: 修改 php.ini 文件</h3>
        <div class="step">
            <strong>步骤1:</strong> 找到 php.ini 文件位置
            <pre><?php echo "当前 php.ini 位置: " . php_ini_loaded_file(); ?></pre>
        </div>

        <div class="step">
            <strong>步骤2:</strong> 编辑 php.ini 文件
            <pre>sudo nano <?php echo php_ini_loaded_file(); ?></pre>
        </div>

        <div class="step">
            <strong>步骤3:</strong> 找到 disable_functions 行，移除需要的函数
            <pre>; 原来的设置（示例）
disable_functions = exec,shell_exec,system,passthru,proc_open,popen

; 修改后（移除exec和shell_exec）
disable_functions = proc_open,popen</pre>
        </div>

        <div class="step">
            <strong>步骤4:</strong> 重启Web服务器
            <pre># Apache
sudo systemctl restart apache2
# 或
sudo systemctl restart httpd

# Nginx + PHP-FPM
sudo systemctl restart nginx
sudo systemctl restart php-fpm</pre>
        </div>

        <h3>方法2: 使用 .htaccess 文件（Apache）</h3>
        <div class="step">
            在网站根目录创建或编辑 .htaccess 文件：
            <pre>php_admin_value disable_functions ""</pre>
            <div class="warning">
                注意：这种方法可能不被所有主机提供商支持。
            </div>
        </div>

        <h3>方法3: 联系主机提供商</h3>
        <div class="step">
            如果您使用的是共享主机，请联系您的主机提供商：
            <ul>
                <li>说明您需要使用命令执行函数</li>
                <li>询问是否可以为您的账户启用这些函数</li>
                <li>了解是否有其他替代方案</li>
            </ul>
        </div>
    </div>

    <div class="card">
        <h2>🔒 安全建议</h2>
        
        <div class="warning">
            <strong>重要安全提示：</strong>
            <ul>
                <li>只启用您确实需要的函数</li>
                <li>确保Web应用有适当的输入验证</li>
                <li>不要执行用户直接输入的命令</li>
                <li>使用白名单限制可执行的命令</li>
                <li>定期检查和更新安全设置</li>
            </ul>
        </div>

        <h3>推荐的安全配置</h3>
        <div class="step">
            如果只需要基本的命令执行功能，建议只启用 <code>shell_exec</code>：
            <pre>disable_functions = exec,system,passthru,proc_open,popen</pre>
        </div>
    </div>

    <div class="card">
        <h2>🎯 替代方案</h2>
        
        <div class="info">
            <strong>如果无法启用命令执行函数，您可以考虑以下替代方案：</strong>
        </div>

        <h3>1. 使用纯PHP实现</h3>
        <ul>
            <li>文件操作：使用PHP内置文件函数</li>
            <li>系统信息：使用 <code>disk_free_space()</code>、<code>memory_get_usage()</code> 等</li>
            <li>时间任务：使用数据库记录和Web请求触发</li>
        </ul>

        <h3>2. 外部触发方式</h3>
        <ul>
            <li>使用外部Cron调用Web接口</li>
            <li>使用队列系统（如Redis、RabbitMQ）</li>
            <li>使用Webhook方式触发任务</li>
        </ul>

        <h3>3. 容器化部署</h3>
        <ul>
            <li>使用Docker容器，完全控制PHP配置</li>
            <li>使用专用的任务调度容器</li>
        </ul>
    </div>

    <div class="card">
        <h2>🧪 测试验证</h2>
        <p>修改配置后，请使用以下方式验证：</p>
        
        <div class="step">
            <strong>1. 重新访问调试页面</strong><br>
            <a href="debug.php" class="btn btn-success">打开调试工具</a>
        </div>

        <div class="step">
            <strong>2. 检查函数状态</strong><br>
            在调试页面查看"系统信息"部分的函数可用性
        </div>

        <div class="step">
            <strong>3. 测试命令执行</strong><br>
            使用调试页面的"命令测试工具"测试简单命令如 <code>date</code>
        </div>
    </div>

    <div class="card">
        <h2>❓ 常见问题</h2>
        
        <h3>Q: 为什么这些函数被禁用？</h3>
        <p>A: 这是出于安全考虑。这些函数可以执行系统命令，如果被恶意利用可能导致服务器被攻击。</p>

        <h3>Q: 启用这些函数安全吗？</h3>
        <p>A: 如果您的应用有适当的安全措施（输入验证、权限控制等），并且您了解相关风险，那么是相对安全的。</p>

        <h3>Q: 共享主机可以启用这些函数吗？</h3>
        <p>A: 大多数共享主机出于安全考虑不允许启用这些函数。您可能需要升级到VPS或专用服务器。</p>

        <h3>Q: 有没有不需要这些函数的替代方案？</h3>
        <p>A: 有的，可以使用纯PHP实现某些功能，或者使用外部API调用的方式来触发任务。</p>
    </div>

    <div class="card">
        <p><strong>需要帮助？</strong></p>
        <p>如果您在配置过程中遇到问题，建议：</p>
        <ul>
            <li>查看服务器错误日志</li>
            <li>联系系统管理员或主机提供商</li>
            <li>考虑使用替代的任务调度方案</li>
        </ul>
        
        <p><a href="index.php" class="btn">返回主界面</a></p>
    </div>
</body>
</html>
