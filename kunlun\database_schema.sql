-- 预警系统数据库表结构
-- 创建时间: 2025-08-12
-- 说明: 基于kafka.php数据结构设计的预警人员存储和短信通知系统

-- 1. 预警人员记录表
CREATE TABLE `Kunlun_alarm_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `alarm_id` varchar(100) NOT NULL COMMENT '报警ID（来自Kafka数据）',
  `alarm_no` varchar(100) DEFAULT NULL COMMENT '报警编号',
  `person_name` varchar(100) DEFAULT NULL COMMENT '预警人员姓名',
  `person_sfz` varchar(18) DEFAULT NULL COMMENT '预警人员身份证号',
  `score` decimal(5,2) DEFAULT NULL COMMENT '相似度分数',
  `location_name` varchar(200) DEFAULT NULL COMMENT '位置名称',
  `latitude` decimal(10,6) DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(10,6) DEFAULT NULL COMMENT '经度',
  `image_base64` longtext COMMENT '抓拍图片Base64数据',
  `image_url` varchar(500) DEFAULT NULL COMMENT '原始图片URL',
  `album_name` varchar(100) DEFAULT NULL COMMENT '图库名称',
  `severity` enum('low','medium','high') DEFAULT 'low' COMMENT '严重级别',
  `captured_time` datetime DEFAULT NULL COMMENT '抓拍时间',
  `created_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `is_processed` tinyint(1) DEFAULT 0 COMMENT '是否已处理（0=未处理，1=已处理）',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `process_user_id` int(11) DEFAULT NULL COMMENT '处理人员ID',
  `remarks` text COMMENT '备注信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_alarm_id` (`alarm_id`),
  KEY `idx_person_sfz` (`person_sfz`),
  KEY `idx_captured_time` (`captured_time`),
  KEY `idx_severity` (`severity`),
  KEY `idx_is_processed` (`is_processed`),
  KEY `idx_location` (`location_name`),
  KEY `idx_score` (`score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预警人员记录表';

-- 2. 短信预警配置表
CREATE TABLE `Kunlun_sms_alert_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `alarm_person_sfz` varchar(18) NOT NULL COMMENT '预警人员身份证号',
  `alarm_person_name` varchar(100) DEFAULT NULL COMMENT '预警人员姓名',
  `recipient_user_id` int(11) NOT NULL COMMENT '短信接收人员ID（关联3_user.id）',
  `alert_level` enum('low','medium','high','all') DEFAULT 'all' COMMENT '预警级别（只有达到此级别才发送短信）',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用（0=禁用，1=启用）',
  `valid_start_time` datetime DEFAULT NULL COMMENT '有效开始时间',
  `valid_end_time` datetime DEFAULT NULL COMMENT '有效结束时间',
  `created_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` int(11) DEFAULT NULL COMMENT '创建人ID',
  `updated_by` int(11) DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`),
  KEY `idx_alarm_person_sfz` (`alarm_person_sfz`),
  KEY `idx_recipient_user_id` (`recipient_user_id`),
  KEY `idx_alert_level` (`alert_level`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_valid_time` (`valid_start_time`,`valid_end_time`),
  CONSTRAINT `fk_sms_alert_recipient` FOREIGN KEY (`recipient_user_id`) REFERENCES `3_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信预警配置表';

-- 3. 短信发送记录表
CREATE TABLE `Kunlun_sms_send_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `alarm_record_id` int(11) NOT NULL COMMENT '关联预警记录ID',
  `config_id` int(11) NOT NULL COMMENT '关联短信配置ID',
  `recipient_user_id` int(11) NOT NULL COMMENT '接收人员ID',
  `recipient_name` varchar(100) DEFAULT NULL COMMENT '接收人员姓名',
  `recipient_phone` varchar(20) DEFAULT NULL COMMENT '接收人员电话',
  `recipient_unit_id` int(11) DEFAULT NULL COMMENT '接收人员单位ID',
  `recipient_unit_name` varchar(200) DEFAULT NULL COMMENT '接收人员单位名称',
  `sms_content` text NOT NULL COMMENT '短信内容',
  `send_status` enum('pending','success','failed','expired') DEFAULT 'pending' COMMENT '发送状态',
  `send_time` datetime DEFAULT NULL COMMENT '发送时间',
  `response_message` text COMMENT '发送响应信息',
  `retry_count` int(11) DEFAULT 0 COMMENT '重试次数',
  `created_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_alarm_record_id` (`alarm_record_id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_recipient_user_id` (`recipient_user_id`),
  KEY `idx_send_status` (`send_status`),
  KEY `idx_send_time` (`send_time`),
  KEY `idx_retry_count` (`retry_count`),
  CONSTRAINT `fk_sms_log_alarm` FOREIGN KEY (`alarm_record_id`) REFERENCES `Kunlun_alarm_records` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sms_log_config` FOREIGN KEY (`config_id`) REFERENCES `Kunlun_sms_alert_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信发送记录表';

-- 插入一些测试数据（可选）
-- 注意：需要确保3_user表中存在对应的用户ID

-- 示例短信配置数据
-- INSERT INTO `Kunlun_sms_alert_config` 
-- (`alarm_person_sfz`, `alarm_person_name`, `recipient_user_id`, `alert_level`, `is_active`, `created_by`) 
-- VALUES 
-- ('510000199001010001', '张三', 1, 'high', 1, 1),
-- ('510000199002020002', '李四', 2, 'medium', 1, 1),
-- ('510000199003030003', '王五', 3, 'all', 1, 1);

-- 创建索引优化查询性能
CREATE INDEX `idx_alarm_records_composite` ON `Kunlun_alarm_records` (`person_sfz`, `severity`, `captured_time`);
CREATE INDEX `idx_sms_config_composite` ON `Kunlun_sms_alert_config` (`alarm_person_sfz`, `alert_level`, `is_active`);
CREATE INDEX `idx_sms_log_composite` ON `Kunlun_sms_send_log` (`send_status`, `send_time`, `retry_count`);

-- 创建视图，方便查询
CREATE VIEW `v_alarm_with_sms_config` AS
SELECT 
    ar.id as alarm_id,
    ar.alarm_no,
    ar.person_name,
    ar.person_sfz,
    ar.score,
    ar.severity,
    ar.location_name,
    ar.captured_time,
    ar.is_processed,
    sac.id as config_id,
    sac.recipient_user_id,
    sac.alert_level,
    sac.is_active as config_active,
    u.name as recipient_name,
    u.phone as recipient_phone,
    u.organization_unit as recipient_unit_id,
    unit.unit_name as recipient_unit_name
FROM Kunlun_alarm_records ar
LEFT JOIN Kunlun_sms_alert_config sac ON ar.person_sfz = sac.alarm_person_sfz 
    AND sac.is_active = 1
    AND (sac.valid_start_time IS NULL OR sac.valid_start_time <= NOW())
    AND (sac.valid_end_time IS NULL OR sac.valid_end_time >= NOW())
    AND (sac.alert_level = 'all' OR sac.alert_level = ar.severity)
LEFT JOIN 3_user u ON sac.recipient_user_id = u.id
LEFT JOIN 2_unit unit ON u.organization_unit = unit.id;

-- 创建存储过程，用于清理过期数据
DELIMITER //
CREATE PROCEDURE `CleanupOldAlarmRecords`(IN days_to_keep INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 删除超过指定天数的预警记录（已处理的）
    DELETE FROM Kunlun_alarm_records 
    WHERE is_processed = 1 
    AND created_time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    -- 删除超过指定天数的短信发送记录
    DELETE FROM Kunlun_sms_send_log 
    WHERE created_time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    COMMIT;
END //
DELIMITER ;

-- 创建触发器，自动更新相关字段
DELIMITER //
CREATE TRIGGER `tr_alarm_records_before_update` 
BEFORE UPDATE ON `Kunlun_alarm_records`
FOR EACH ROW
BEGIN
    -- 如果处理状态从未处理变为已处理，自动设置处理时间
    IF OLD.is_processed = 0 AND NEW.is_processed = 1 AND NEW.process_time IS NULL THEN
        SET NEW.process_time = NOW();
    END IF;
END //
DELIMITER ;

-- 表结构创建完成
-- 使用说明：
-- 1. 执行此SQL文件创建表结构
-- 2. 确保3_user和2_unit表已存在
-- 3. 根据实际需要调整字段长度和索引
-- 4. 定期执行CleanupOldAlarmRecords存储过程清理数据
