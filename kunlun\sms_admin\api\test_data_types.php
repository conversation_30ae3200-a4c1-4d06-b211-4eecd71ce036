<?php
/**
 * 测试数据类型转换
 */

require_once '../../db_connection.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // 测试获取配置列表的数据类型
    $listSql = "SELECT
                    sac.id,
                    sac.alarm_type,
                    sac.alarm_person_sfz,
                    sac.alarm_person_name,
                    sac.album_name,
                    sac.recipient_user_id,
                    sac.is_active,
                    sac.valid_start_time,
                    sac.valid_end_time,
                    sac.created_time,
                    u.name as recipient_name,
                    u.phone as recipient_phone,
                    unit.unit_name as recipient_unit_name
                FROM Kunlun_sms_alert_config sac
                JOIN 3_user u ON sac.recipient_user_id = u.id
                LEFT JOIN 2_unit unit ON u.organization_unit = unit.id
                ORDER BY sac.created_time DESC
                LIMIT 5";
    
    $result = $conn->query($listSql);
    
    $originalData = [];
    $convertedData = [];
    
    while ($row = $result->fetch_assoc()) {
        // 原始数据
        $originalData[] = [
            'id' => $row['id'],
            'id_type' => gettype($row['id']),
            'is_active' => $row['is_active'],
            'is_active_type' => gettype($row['is_active']),
            'recipient_user_id' => $row['recipient_user_id'],
            'recipient_user_id_type' => gettype($row['recipient_user_id'])
        ];
        
        // 转换后的数据
        $converted = $row;
        $converted['id'] = intval($converted['id']);
        $converted['recipient_user_id'] = intval($converted['recipient_user_id']);
        $converted['is_active'] = intval($converted['is_active']);
        
        $convertedData[] = [
            'id' => $converted['id'],
            'id_type' => gettype($converted['id']),
            'is_active' => $converted['is_active'],
            'is_active_type' => gettype($converted['is_active']),
            'recipient_user_id' => $converted['recipient_user_id'],
            'recipient_user_id_type' => gettype($converted['recipient_user_id']),
            'recipient_name' => $converted['recipient_name']
        ];
    }
    
    // JavaScript类型判断测试
    $jsTests = [
        'string_0' => [
            'value' => '0',
            'type' => 'string',
            'truthy' => '0' ? 'true' : 'false',
            'description' => '字符串"0"在JavaScript中是truthy'
        ],
        'number_0' => [
            'value' => 0,
            'type' => 'number',
            'truthy' => 0 ? 'true' : 'false',
            'description' => '数字0在JavaScript中是falsy'
        ],
        'string_1' => [
            'value' => '1',
            'type' => 'string',
            'truthy' => '1' ? 'true' : 'false',
            'description' => '字符串"1"在JavaScript中是truthy'
        ],
        'number_1' => [
            'value' => 1,
            'type' => 'number',
            'truthy' => 1 ? 'true' : 'false',
            'description' => '数字1在JavaScript中是truthy'
        ]
    ];
    
    echo json_encode([
        'status' => 1,
        'message' => '数据类型测试完成',
        'data' => [
            'original_data' => $originalData,
            'converted_data' => $convertedData,
            'js_type_tests' => $jsTests,
            'fix_explanation' => [
                'problem' => 'MySQL返回的数值字段是字符串类型，导致JavaScript判断错误',
                'solution' => '在PHP中使用intval()将字符串转换为整数',
                'example' => [
                    'before' => 'is_active: "0" (string) -> truthy in JavaScript',
                    'after' => 'is_active: 0 (number) -> falsy in JavaScript'
                ]
            ]
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 0,
        'message' => '测试失败: ' . $e->getMessage(),
        'error' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
