<?php
require_once '../../conn_waf.php';

// 设置应用ID
$APP_ID = 8;



// 获取控制参数
$controlCode = isset($_POST['controlCode']) ? $_POST['controlCode'] : '';

// 检查必要参数
if (empty($controlCode)) {
    echo json_encode(['status' => 0, 'message' => '缺少控制参数', 'data' => []]);
    exit;
}

/**
 * 添加重点人员
 */
function addKeyPerson() {
    global $conn,$APP_ID;
    
    if (empty($_POST['name']) || empty($_POST['id_number']) || empty($_POST['gender']) || 
        empty($_POST['phone']) || empty($_POST['type_id'])  || empty($_POST['unit_id'])) {
        throw new Exception('缺少必要参数');
    }

    $sql = "INSERT INTO 8_key_person (name, id_number, gender, phone, type_id, unit_id, is_display, is_top) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $is_display = $_POST['is_display'] ?? 0;
    $is_top = $_POST['is_top'] ?? 0;
    $stmt->bind_param("ssssiiii", 
        $_POST['name'],
        $_POST['id_number'],
        $_POST['gender'],
        $_POST['phone'],
        $_POST['type_id'],
        $_POST['unit_id'],
        $is_display,
        $is_top
    );
    
    if (!$stmt->execute()) {
        throw new Exception('添加失败：' . $stmt->error);
    }
    $insert_id=$stmt->insert_id;
    logOperation($conn, $_SESSION['user_id'], $APP_ID, "新增重点人员id：{$insert_id}");
    echo json_encode(['status' => 1, 'message' => '添加成功', 'data' => [
        'id' => $insert_id,
    ]]);
    $stmt->close();
}

/**
 * 删除重点人员
 */
function deleteKeyPerson() {
    global $conn,$APP_ID;
    
    if (empty($_POST['id'])) {
        throw new Exception('缺少ID参数');
    }

    // 处理参数
    $ids = $_POST['id'];
    $idList = is_numeric($ids) ? [intval($ids)] : array_filter(array_map('intval', explode(',', $ids)));
    
    if (empty($idList)) {
        throw new Exception('无效的ID参数');
    }

    // 先检查是否存在关联的动向记录
    $placeholders = implode(',', array_fill(0, count($idList), '?'));
    $sql = "SELECT COUNT(*) as count FROM 8_person_movement WHERE person_id IN ($placeholders)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param(str_repeat('i', count($idList)), ...$idList);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    if ($row['count'] > 0) {
        throw new Exception('存在关联的动向记录，无法删除');
    }

    // 执行删除操作
    $sql = "DELETE FROM 8_key_person WHERE id IN ($placeholders)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param(str_repeat('i', count($idList)), ...$idList);
    
    if (!$stmt->execute()) {
        throw new Exception('删除失败：' . $stmt->error);
    }

    logOperation($conn, $_SESSION['user_id'], $APP_ID, "删除重点人员ID：" . implode(',', $idList));
    echo json_encode(['status' => 1, 'message' => '删除成功', 'data' => [
        'id' => $_POST['id'],
    ]]);
}

/**
 * 修改重点人员信息
 */
function modifyKeyPerson() {
    global $conn,$APP_ID;
    
    if (empty($_POST['id'])) {
        throw new Exception('缺少ID参数');
    }

    $updateFields = [];
    $types = "";
    $params = [];

    // 动态构建更新字段
    $allowedFields = [
        'name' => 's',
        'id_number' => 's',
        'gender' => 'i',
        'phone' => 's',
        'type_id' => 'i',
        'unit_id' => 'i',
        'is_display' => 'i',
        'is_top' => 'i'
    ];

    foreach ($allowedFields as $field => $type) {
        if (isset($_POST[$field]) && $_POST[$field] !== '') {
            $updateFields[] = "`$field` = ?";
            $types .= $type;
            $params[] = $_POST[$field];
        }
    }

    if (empty($updateFields)) {
        echo json_encode(['status' => 1, 'message' => '没有字段需要更新', 'data' => []]);
        exit;
    }

    $types .= "i"; // 为WHERE id = ?添加类型
    $params[] = $_POST['id'];

    $sql = "UPDATE 8_key_person SET " . implode(", ", $updateFields) . " WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    
    if (!$stmt->execute()) {
        throw new Exception('更新失败：' . $stmt->error);
    }

    // 构建变更数据
    $changedData = [];
    foreach ($allowedFields as $field => $type) {
        if (isset($_POST[$field]) && $_POST[$field] !== '') {
            $changedData[$field] = $_POST[$field];
        }
    }

    $logMessage = "修改重点人员ID：{$_POST['id']}，变更数据：" . json_encode($changedData);
    logOperation($conn, $_SESSION['user_id'], $APP_ID, $logMessage);
    echo json_encode(['status' => 1, 'message' => '更新成功', 'data' => $changedData]);
}

/**
 * 查询重点人员信息
 */
function queryKeyPerson() {
    global $conn;
    
    // 当page和pagesize都为空时，设置默认值
    if (empty($_POST['page']) && empty($_POST['pagesize'])) {
        $page = 1;
        $pagesize = 10;
    } else {
        $page = isset($_POST['page']) ? max(1, intval($_POST['page'])) : 1;
        $pagesize = isset($_POST['pagesize']) ? max(1, intval($_POST['pagesize'])) : 10;
    }
    $offset = ($page - 1) * $pagesize;

    // 构建查询条件
    $where = "1=1";
    $params = [];
    $types = "";

    if (!empty($_POST['name'])) {
        $where .= " AND name LIKE ?";
        $params[] = "%{$_POST['name']}%";
        $types .= "s";
    }

    if (!empty($_POST['id_number'])) {
        $where .= " AND id_number LIKE ?";
        $params[] = "%{$_POST['id_number']}%";
        $types .= "s";
    }

    if (!empty($_POST['type_id'])) {
        $where .= " AND type_id = ?";
        $params[] = $_POST['type_id'];
        $types .= "i";
    }

    if (!empty($_POST['unit_id'])) {
        $where .= " AND unit_id = ?";
        $params[] = $_POST['unit_id'];
        $types .= "i";
    }
    if (!empty($_POST['unit_name'])) {
        $where .= " AND LOWER(u.unit_name) LIKE LOWER(?)";
        $params[] = "%{$_POST['unit_name']}%";
        $types .= "s";
    }
    if (isset($_POST['is_display'])) {
        $where .= " AND is_display = ?";
        $params[] = $_POST['is_display'];
        $types .= "i";
    }
    if (isset($_POST['is_top'])) {
        $where .= " AND is_top = ?";
        $params[] = $_POST['is_top'];
        $types .= "i";
    }
    // 获取总记录数
    $countSql = "SELECT COUNT(DISTINCT k.id) as total FROM 8_key_person k LEFT JOIN 2_unit u ON k.unit_id = u.id WHERE $where";
    $stmt = $conn->prepare($countSql);
    if (!empty($types)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    $total = $result->fetch_assoc()['total'];

    // 获取分页数据
    $sql = "SELECT k.*, t.type_name, u.unit_name
           FROM 8_key_person k
           LEFT JOIN 8_person_type t ON k.type_id = t.id
           LEFT JOIN 2_unit u ON k.unit_id = u.id
           WHERE $where
           ORDER BY k.is_top DESC, k.id DESC
           LIMIT ?, ?";

    $stmt = $conn->prepare($sql);
    $types .= "ii";
    $params[] = $offset;
    $params[] = $pagesize;
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $row['gender_name'] = $row['gender'] == 1 ? '男' : '女';
        $data[] = $row;
    }

    echo json_encode([
        'status' => 1,
        'message' => '查询成功',
        'data' => $data,
        'total' => $total,
        'page' => $page,
        'pagesize' => $pagesize,

    ]);
}
function isHasPerm(){
    if (!isset($_SESSION['user_id'])) {
        echo json_encode([
            'status' => 0,
            'message' => '用户未登录',
            'data' => []
        ]);
        exit;
    }
    global $APP_ID;
    if (!isAdmin() && !isAppAdmin($APP_ID)) {
    echo json_encode([
        'status' => 0,
        'message' => '当前用户无权限操作',
        'data' => []
    ]);
    exit;
    }
}
try {
    switch ($controlCode) {
        case 'add':
            isHasPerm();
            addKeyPerson();
            break;

        case 'del':
            isHasPerm();
            deleteKeyPerson();
            break;

        case 'modify':
            isHasPerm();
            modifyKeyPerson();
            break;

        case 'query':
            queryKeyPerson();
            break;

        default:
            throw new Exception('无效的控制参数');
    }
} catch (Exception $e) {
    echo json_encode(['status' => 0, 'message' => $e->getMessage(), 'data' => []]);
}