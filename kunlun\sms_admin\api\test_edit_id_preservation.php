<?php
/**
 * 测试编辑配置时ID保持不变
 */

require_once '../../db_connection.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // 模拟用户ID
    $user_id = 1;
    
    $tests = [];
    
    // 1. 创建测试配置
    $createData = [
        'alarm_type' => 'sfz',
        'alarm_person_sfz' => '110101199001011234',
        'alarm_person_name' => '测试人员',
        'recipients' => json_encode([1]),
        'valid_start_time' => '2025-08-12 00:00:00',
        'valid_end_time' => '2025-08-12 23:59:59',
        'is_active' => 1
    ];
    
    // 创建配置
    $insertSql = "INSERT INTO Kunlun_sms_alert_config (
                    alarm_type, alarm_person_sfz, alarm_person_name, album_name,
                    recipient_user_id, is_active, valid_start_time, valid_end_time,
                    created_by, created_time
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    
    $insertStmt = $conn->prepare($insertSql);
    $insertStmt->bind_param("ssssisssi",
        $createData['alarm_type'],
        $createData['alarm_person_sfz'],
        $createData['alarm_person_name'],
        null, // album_name
        1, // recipient_user_id
        $createData['is_active'],
        $createData['valid_start_time'],
        $createData['valid_end_time'],
        $user_id
    );
    
    $insertStmt->execute();
    $originalId = $conn->insert_id;
    
    $tests['create_config'] = [
        'success' => true,
        'original_id' => $originalId,
        'note' => '创建测试配置成功'
    ];
    
    // 2. 测试简单编辑（相同接收人员）
    $_POST = [
        'action' => 'edit',
        'id' => $originalId,
        'alarm_type' => 'sfz',
        'alarm_person_sfz' => '110101199001011234',
        'alarm_person_name' => '测试人员（已修改）',
        'recipients' => json_encode([1]), // 相同的接收人员
        'valid_start_time' => '2025-08-12 00:00:00',
        'valid_end_time' => '2025-08-12 23:59:59',
        'is_active' => 0 // 修改状态
    ];
    
    // 模拟调用updateConfig函数
    ob_start();
    include 'config_manage.php';
    $response1 = ob_get_clean();
    
    // 检查ID是否保持不变
    $checkSql = "SELECT id, alarm_person_name, is_active FROM Kunlun_sms_alert_config WHERE id = ?";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bind_param("i", $originalId);
    $checkStmt->execute();
    $result = $checkStmt->get_result();
    
    if ($result->num_rows > 0) {
        $updatedConfig = $result->fetch_assoc();
        $tests['simple_edit'] = [
            'success' => true,
            'id_preserved' => intval($updatedConfig['id']) === $originalId,
            'name_updated' => $updatedConfig['alarm_person_name'] === '测试人员（已修改）',
            'status_updated' => intval($updatedConfig['is_active']) === 0,
            'original_id' => $originalId,
            'current_id' => intval($updatedConfig['id']),
            'note' => 'ID应该保持不变，内容应该更新'
        ];
    } else {
        $tests['simple_edit'] = [
            'success' => false,
            'error' => '配置被删除了',
            'note' => '这是错误的行为'
        ];
    }
    
    // 3. 测试多接收人员编辑
    $_POST = [
        'action' => 'edit',
        'id' => $originalId,
        'alarm_type' => 'sfz',
        'alarm_person_sfz' => '110101199001011234',
        'alarm_person_name' => '测试人员（多接收人员）',
        'recipients' => json_encode([1, 2]), // 增加接收人员
        'valid_start_time' => '2025-08-12 00:00:00',
        'valid_end_time' => '2025-08-12 23:59:59',
        'is_active' => 1
    ];
    
    // 记录编辑前的配置数量
    $countBeforeSql = "SELECT COUNT(*) as count FROM Kunlun_sms_alert_config";
    $countBefore = $conn->query($countBeforeSql)->fetch_assoc()['count'];
    
    // 模拟调用updateConfig函数
    ob_start();
    include 'config_manage.php';
    $response2 = ob_get_clean();
    
    // 记录编辑后的配置数量
    $countAfterSql = "SELECT COUNT(*) as count FROM Kunlun_sms_alert_config";
    $countAfter = $conn->query($countAfterSql)->fetch_assoc()['count'];
    
    // 检查原始ID是否还存在
    $checkOriginalSql = "SELECT id, alarm_person_name, recipient_user_id FROM Kunlun_sms_alert_config WHERE id = ?";
    $checkOriginalStmt = $conn->prepare($checkOriginalSql);
    $checkOriginalStmt->bind_param("i", $originalId);
    $checkOriginalStmt->execute();
    $originalResult = $checkOriginalStmt->get_result();
    
    $tests['multi_recipient_edit'] = [
        'count_before' => intval($countBefore),
        'count_after' => intval($countAfter),
        'original_id_exists' => $originalResult->num_rows > 0,
        'original_id' => $originalId,
        'note' => '多接收人员编辑时，原始ID应该保留'
    ];
    
    if ($originalResult->num_rows > 0) {
        $originalData = $originalResult->fetch_assoc();
        $tests['multi_recipient_edit']['original_data'] = $originalData;
    }
    
    // 4. 清理测试数据
    $cleanupSql = "DELETE FROM Kunlun_sms_alert_config WHERE alarm_person_sfz = '110101199001011234'";
    $conn->query($cleanupSql);
    
    $tests['cleanup'] = [
        'success' => true,
        'note' => '清理测试数据完成'
    ];
    
    echo json_encode([
        'status' => 1,
        'message' => 'ID保持测试完成',
        'data' => [
            'tests' => $tests,
            'summary' => [
                'simple_edit_preserves_id' => $tests['simple_edit']['id_preserved'] ?? false,
                'multi_recipient_preserves_original' => $tests['multi_recipient_edit']['original_id_exists'] ?? false,
                'explanation' => [
                    'simple_edit' => '当接收人员不变时，使用UPDATE语句，ID保持不变',
                    'multi_recipient_edit' => '当接收人员变化时，保留原始记录并更新，必要时创建新记录',
                    'benefit' => '避免了删除重建导致的ID变化问题'
                ]
            ]
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 0,
        'message' => '测试失败: ' . $e->getMessage(),
        'error' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
}
?>
