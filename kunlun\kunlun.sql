-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.0.4
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-08-12 15:30:22
-- 服务器版本： 5.7.44-log
-- PHP 版本： 8.0.26

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `application`
--

-- --------------------------------------------------------

--
-- 表的结构 `Kunlun_alarm_records`
--

CREATE TABLE `Kunlun_alarm_records` (
  `id` int(11) NOT NULL COMMENT '主键ID',
  `alarm_id` varchar(100) NOT NULL COMMENT '报警ID（来自Kafka数据）',
  `alarm_no` varchar(100) DEFAULT NULL COMMENT '报警编号',
  `person_name` varchar(100) DEFAULT NULL COMMENT '预警人员姓名',
  `person_sfz` varchar(18) DEFAULT NULL COMMENT '预警人员身份证号',
  `score` decimal(5,2) DEFAULT NULL COMMENT '相似度分数',
  `location_name` varchar(200) DEFAULT NULL COMMENT '位置名称',
  `latitude` decimal(10,6) DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(10,6) DEFAULT NULL COMMENT '经度',
  `image_url` varchar(500) DEFAULT NULL COMMENT '图片URL',
  `album_name` varchar(100) DEFAULT NULL COMMENT '图库名称',
  `severity` enum('low','medium','high') DEFAULT 'low' COMMENT '严重级别',
  `captured_time` datetime DEFAULT NULL COMMENT '抓拍时间',
  `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `is_processed` tinyint(1) DEFAULT '0' COMMENT '是否已处理（0=未处理，1=已处理）',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `process_user_id` int(11) DEFAULT NULL COMMENT '处理人员ID',
  `remarks` text COMMENT '备注信息'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预警人员记录表';

-- --------------------------------------------------------

--
-- 表的结构 `Kunlun_sms_alert_config`
--

CREATE TABLE `Kunlun_sms_alert_config` (
  `id` int(11) NOT NULL COMMENT '主键ID',
  `alarm_type` enum('sfz','album') NOT NULL DEFAULT 'sfz' COMMENT '预警类型（sfz=单身份证预警，album=图库预警）',
  `alarm_person_sfz` varchar(18) DEFAULT NULL COMMENT '预警人员身份证号（单身份证预警时必填）',
  `alarm_person_name` varchar(100) DEFAULT NULL COMMENT '预警人员姓名',
  `album_name` varchar(100) DEFAULT NULL COMMENT '图库名称（图库预警时必填）',
  `recipient_user_id` int(11) NOT NULL COMMENT '短信接收人员ID（关联3_user.id）',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用（0=禁用，1=启用）',
  `valid_start_time` datetime DEFAULT NULL COMMENT '有效开始时间',
  `valid_end_time` datetime DEFAULT NULL COMMENT '有效结束时间',
  `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` int(11) DEFAULT NULL COMMENT '创建人ID',
  `updated_by` int(11) DEFAULT NULL COMMENT '更新人ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信预警配置表（支持单身份证和图库预警）';

-- --------------------------------------------------------

--
-- 表的结构 `Kunlun_sms_send_log`
--

CREATE TABLE `Kunlun_sms_send_log` (
  `id` int(11) NOT NULL COMMENT '主键ID',
  `alarm_record_id` int(11) NOT NULL COMMENT '关联预警记录ID',
  `config_id` int(11) NOT NULL COMMENT '关联短信配置ID',
  `recipient_user_id` int(11) NOT NULL COMMENT '接收人员ID',
  `recipient_name` varchar(100) DEFAULT NULL COMMENT '接收人员姓名',
  `recipient_phone` varchar(20) DEFAULT NULL COMMENT '接收人员电话',
  `recipient_unit_id` int(11) DEFAULT NULL COMMENT '接收人员单位ID',
  `recipient_unit_name` varchar(200) DEFAULT NULL COMMENT '接收人员单位名称',
  `sms_content` text NOT NULL COMMENT '短信内容',
  `send_status` enum('pending','success','failed','expired') DEFAULT 'pending' COMMENT '发送状态',
  `send_time` datetime DEFAULT NULL COMMENT '发送时间',
  `response_message` text COMMENT '发送响应信息',
  `retry_count` int(11) DEFAULT '0' COMMENT '重试次数',
  `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信发送记录表';

--
-- 转储表的索引
--

--
-- 表的索引 `Kunlun_alarm_records`
--
ALTER TABLE `Kunlun_alarm_records`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_alarm_id` (`alarm_id`),
  ADD KEY `idx_person_sfz` (`person_sfz`),
  ADD KEY `idx_captured_time` (`captured_time`),
  ADD KEY `idx_severity` (`severity`),
  ADD KEY `idx_is_processed` (`is_processed`),
  ADD KEY `idx_location` (`location_name`),
  ADD KEY `idx_score` (`score`),
  ADD KEY `idx_alarm_records_composite` (`person_sfz`,`severity`,`captured_time`);

--
-- 表的索引 `Kunlun_sms_alert_config`
--
ALTER TABLE `Kunlun_sms_alert_config`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_alarm_person_sfz` (`alarm_person_sfz`),
  ADD KEY `idx_recipient_user_id` (`recipient_user_id`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_valid_time` (`valid_start_time`,`valid_end_time`),
  ADD KEY `idx_alarm_type` (`alarm_type`),
  ADD KEY `idx_album_name` (`album_name`);

--
-- 表的索引 `Kunlun_sms_send_log`
--
ALTER TABLE `Kunlun_sms_send_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_alarm_record_id` (`alarm_record_id`),
  ADD KEY `idx_config_id` (`config_id`),
  ADD KEY `idx_recipient_user_id` (`recipient_user_id`),
  ADD KEY `idx_send_status` (`send_status`),
  ADD KEY `idx_send_time` (`send_time`),
  ADD KEY `idx_retry_count` (`retry_count`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `Kunlun_alarm_records`
--
ALTER TABLE `Kunlun_alarm_records`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID';

--
-- 使用表AUTO_INCREMENT `Kunlun_sms_alert_config`
--
ALTER TABLE `Kunlun_sms_alert_config`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID';

--
-- 使用表AUTO_INCREMENT `Kunlun_sms_send_log`
--
ALTER TABLE `Kunlun_sms_send_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID';

--
-- 限制导出的表
--

--
-- 限制表 `Kunlun_sms_alert_config`
--
ALTER TABLE `Kunlun_sms_alert_config`
  ADD CONSTRAINT `fk_sms_alert_recipient` FOREIGN KEY (`recipient_user_id`) REFERENCES `3_user` (`id`) ON DELETE CASCADE;

--
-- 限制表 `Kunlun_sms_send_log`
--
ALTER TABLE `Kunlun_sms_send_log`
  ADD CONSTRAINT `fk_sms_log_alarm` FOREIGN KEY (`alarm_record_id`) REFERENCES `Kunlun_alarm_records` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_sms_log_config` FOREIGN KEY (`config_id`) REFERENCES `Kunlun_sms_alert_config` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
