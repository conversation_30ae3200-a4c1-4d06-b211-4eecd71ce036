<?php
/**
 * 预警系统完整测试页面
 * 创建时间: 2025-08-12
 * 功能: 测试整个预警系统的各个组件
 */

require_once '../conn_waf.php';

$APP_ID = 35; // 预警系统应用ID

// 检查用户权限
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.html');
    exit;
}

// 检查应用权限
function isHasPerm($APP_ID) {
    global $conn;
    
    if (!isset($_SESSION['user_id'])) {
        return false;
    }
    
    // 系统管理员拥有所有权限
    if (isAdmin()) {
        return true;
    }
    
    // 检查应用管理员权限
    if (isAppAdmin($APP_ID)) {
        return true;
    }
    
    return false;
}

if (!isHasPerm($APP_ID)) {
    $errorMessage = urlencode('您没有访问预警系统测试的权限。');
    header("Location: ../permission_error.html?type=permission&message=" . $errorMessage);
    exit;
}

$user_id = $_SESSION['user_id'];

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'testDatabase':
            testDatabase();
            break;
        case 'testSSE':
            testSSEConnection();
            break;
        case 'testSms':
            testSmsService();
            break;
        case 'simulateAlarm':
            simulateAlarmData();
            break;
        case 'testFullFlow':
            testFullFlow();
            break;
        case 'generateTestData':
            generateTestData();
            break;
        default:
            echo json_encode(['status' => 0, 'message' => '无效的操作']);
    }
    exit;
}

/**
 * 测试数据库连接和表结构
 */
function testDatabase() {
    global $conn;
    
    $results = [];
    
    // 测试数据库连接
    try {
        $result = $conn->query("SELECT VERSION() as version");
        if ($result) {
            $version = $result->fetch_assoc()['version'];
            $results['connection'] = ['status' => 'success', 'message' => "数据库连接成功，版本: $version"];
        } else {
            $results['connection'] = ['status' => 'error', 'message' => '数据库查询失败'];
        }
    } catch (Exception $e) {
        $results['connection'] = ['status' => 'error', 'message' => '数据库连接失败: ' . $e->getMessage()];
    }
    
    // 测试表结构
    $tables = ['Kunlun_alarm_records', 'Kunlun_sms_alert_config', 'Kunlun_sms_send_log'];
    
    foreach ($tables as $table) {
        try {
            $sql = "SHOW TABLES LIKE '$table'";
            $result = $conn->query($sql);
            
            if ($result && $result->num_rows > 0) {
                // 检查表结构
                $sql = "DESCRIBE $table";
                $result = $conn->query($sql);
                $columns = $result->num_rows;
                
                $results['tables'][$table] = [
                    'status' => 'success',
                    'message' => "表存在，包含 $columns 个字段"
                ];
            } else {
                $results['tables'][$table] = [
                    'status' => 'error',
                    'message' => '表不存在'
                ];
            }
        } catch (Exception $e) {
            $results['tables'][$table] = [
                'status' => 'error',
                'message' => '检查失败: ' . $e->getMessage()
            ];
        }
    }
    
    echo json_encode(['status' => 1, 'data' => $results]);
}

/**
 * 测试SSE连接
 */
function testSSEConnection() {
    $sseUrl = 'http://localhost/kunlun/kafka.php';
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'user_agent' => 'System Test'
        ]
    ]);
    
    try {
        $headers = get_headers($sseUrl, 1, $context);
        
        if ($headers && strpos($headers[0], '200') !== false) {
            // 尝试读取SSE流
            $stream = fopen($sseUrl, 'r', false, $context);
            
            if ($stream) {
                stream_set_timeout($stream, 5);
                $line = fgets($stream);
                fclose($stream);
                
                if ($line !== false) {
                    echo json_encode([
                        'status' => 1,
                        'message' => 'SSE连接测试成功，服务器响应正常',
                        'data' => ['response' => trim($line)]
                    ]);
                } else {
                    echo json_encode([
                        'status' => 0,
                        'message' => 'SSE服务器无响应数据'
                    ]);
                }
            } else {
                echo json_encode([
                    'status' => 0,
                    'message' => 'SSE流打开失败'
                ]);
            }
        } else {
            echo json_encode([
                'status' => 0,
                'message' => 'SSE服务器返回错误状态'
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'status' => 0,
            'message' => 'SSE连接测试失败: ' . $e->getMessage()
        ]);
    }
}

/**
 * 测试短信服务
 */
function testSmsService() {
    $phone = $_POST['phone'] ?? '';
    $content = $_POST['content'] ?? '【测试】这是一条系统测试短信，请忽略。';
    
    if (empty($phone)) {
        echo json_encode(['status' => 0, 'message' => '请提供测试手机号']);
        return;
    }
    
    try {
        require_once '../api/SmsSystem.php';
        
        $response = SMS_sendmessage($phone, $content, '岳池县公安局');
        
        $responseData = json_decode($response, true);
        
        if ($responseData && isset($responseData['renturncode'])) {
            if ($responseData['renturncode'] === '0' || $responseData['renturncode'] === 0) {
                echo json_encode([
                    'status' => 1,
                    'message' => '短信发送测试成功',
                    'data' => ['response' => $response]
                ]);
            } else {
                echo json_encode([
                    'status' => 0,
                    'message' => '短信发送失败，错误码: ' . $responseData['renturncode'],
                    'data' => ['response' => $response]
                ]);
            }
        } else {
            echo json_encode([
                'status' => 0,
                'message' => '短信服务响应格式异常',
                'data' => ['response' => $response]
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'status' => 0,
            'message' => '短信服务测试失败: ' . $e->getMessage()
        ]);
    }
}

/**
 * 模拟预警数据
 */
function simulateAlarmData() {
    global $conn;
    
    // 生成模拟预警数据
    $testData = [
        'id' => 'TEST_' . uniqid(),
        'alarmNo' => 'ALARM_' . date('YmdHis'),
        'name' => '测试人员' . rand(1, 100),
        'sfz' => '510000' . date('Ymd') . sprintf('%04d', rand(1, 9999)),
        'score' => rand(70, 95),
        'location' => '测试地点' . rand(1, 10),
        'lat' => 30.5371 + (rand(-1000, 1000) / 10000),
        'lon' => 106.44307 + (rand(-1000, 1000) / 10000),
        'image' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'imageUrl' => 'http://example.com/test.jpg',
        'album' => '测试图库',
        'severity' => ['low', 'medium', 'high'][rand(0, 2)],
        'time' => date('Y-m-d H:i:s')
    ];
    
    try {
        // 插入模拟数据到数据库
        $sql = "INSERT INTO Kunlun_alarm_records (
            alarm_id, alarm_no, person_name, person_sfz, score,
            location_name, latitude, longitude, image_url,
            album_name, severity, captured_time, created_time
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssssdsdssss",
            $testData['id'],
            $testData['alarmNo'],
            $testData['name'],
            $testData['sfz'],
            $testData['score'],
            $testData['location'],
            $testData['lat'],
            $testData['lon'],
            $testData['imageUrl'],
            $testData['album'],
            $testData['severity'],
            $testData['time']
        );
        
        if ($stmt->execute()) {
            $insertId = $stmt->insert_id;
            
            echo json_encode([
                'status' => 1,
                'message' => '模拟预警数据生成成功',
                'data' => [
                    'id' => $insertId,
                    'testData' => $testData
                ]
            ]);
        } else {
            echo json_encode([
                'status' => 0,
                'message' => '模拟数据插入失败: ' . $stmt->error
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'status' => 0,
            'message' => '模拟数据生成失败: ' . $e->getMessage()
        ]);
    }
}

/**
 * 测试完整流程
 */
function testFullFlow() {
    global $conn;
    
    $results = [];
    
    // 1. 测试数据库
    $results['database'] = testDatabaseQuick();
    
    // 2. 生成测试配置
    $results['config'] = createTestConfig();
    
    // 3. 生成测试预警
    $results['alarm'] = createTestAlarm();
    
    // 4. 检查短信发送
    $results['sms'] = checkSmsGeneration();
    
    echo json_encode([
        'status' => 1,
        'message' => '完整流程测试完成',
        'data' => $results
    ]);
}

/**
 * 快速数据库测试
 */
function testDatabaseQuick() {
    global $conn;
    
    try {
        $result = $conn->query("SELECT 1");
        return ['status' => 'success', 'message' => '数据库连接正常'];
    } catch (Exception $e) {
        return ['status' => 'error', 'message' => '数据库连接失败'];
    }
}

/**
 * 创建测试配置
 */
function createTestConfig() {
    global $conn, $user_id;
    
    try {
        // 获取一个有效的用户ID
        $sql = "SELECT id FROM 3_user WHERE phone IS NOT NULL AND phone != '' LIMIT 1";
        $result = $conn->query($sql);
        
        if ($result->num_rows === 0) {
            return ['status' => 'error', 'message' => '没有找到有效的接收用户'];
        }
        
        $user = $result->fetch_assoc();
        $testSfz = '510000' . date('Ymd') . '0001';
        
        // 检查是否已存在测试配置
        $checkSql = "SELECT id FROM Kunlun_sms_alert_config WHERE alarm_person_sfz = ?";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bind_param("s", $testSfz);
        $checkStmt->execute();
        
        if ($checkStmt->get_result()->num_rows > 0) {
            return ['status' => 'success', 'message' => '测试配置已存在'];
        }
        
        // 创建测试配置
        $sql = "INSERT INTO Kunlun_sms_alert_config (
            alarm_person_sfz, alarm_person_name, recipient_user_id, 
            alert_level, is_active, created_by, created_time
        ) VALUES (?, '测试人员', ?, 'all', 1, ?, NOW())";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sii", $testSfz, $user['id'], $user_id);
        
        if ($stmt->execute()) {
            return ['status' => 'success', 'message' => '测试配置创建成功'];
        } else {
            return ['status' => 'error', 'message' => '测试配置创建失败'];
        }
    } catch (Exception $e) {
        return ['status' => 'error', 'message' => '配置创建异常: ' . $e->getMessage()];
    }
}

/**
 * 创建测试预警
 */
function createTestAlarm() {
    global $conn;
    
    try {
        $testSfz = '510000' . date('Ymd') . '0001';
        
        $sql = "INSERT INTO Kunlun_alarm_records (
            alarm_id, alarm_no, person_name, person_sfz, score, 
            location_name, latitude, longitude, severity, 
            captured_time, created_time
        ) VALUES (?, ?, '测试人员', ?, 85, '测试地点', 30.5371, 106.44307, 'high', NOW(), NOW())";
        
        $alarmId = 'TEST_FLOW_' . uniqid();
        $alarmNo = 'FLOW_' . date('YmdHis');
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sss", $alarmId, $alarmNo, $testSfz);
        
        if ($stmt->execute()) {
            return ['status' => 'success', 'message' => '测试预警创建成功', 'id' => $stmt->insert_id];
        } else {
            return ['status' => 'error', 'message' => '测试预警创建失败'];
        }
    } catch (Exception $e) {
        return ['status' => 'error', 'message' => '预警创建异常: ' . $e->getMessage()];
    }
}

/**
 * 检查短信生成
 */
function checkSmsGeneration() {
    global $conn;
    
    try {
        $sql = "SELECT COUNT(*) as count FROM Kunlun_sms_send_log 
                WHERE created_time >= DATE_SUB(NOW(), INTERVAL 1 MINUTE)";
        $result = $conn->query($sql);
        $count = $result->fetch_assoc()['count'];
        
        if ($count > 0) {
            return ['status' => 'success', 'message' => "检测到 $count 条新短信记录"];
        } else {
            return ['status' => 'warning', 'message' => '未检测到新的短信记录，请检查后台处理程序'];
        }
    } catch (Exception $e) {
        return ['status' => 'error', 'message' => '短信检查异常: ' . $e->getMessage()];
    }
}

/**
 * 生成测试数据
 */
function generateTestData() {
    global $conn, $user_id;
    
    $count = intval($_POST['count'] ?? 10);
    $count = min($count, 100); // 限制最多100条
    
    $successCount = 0;
    $errorCount = 0;
    
    for ($i = 0; $i < $count; $i++) {
        try {
            $testData = [
                'id' => 'BATCH_' . uniqid(),
                'alarmNo' => 'BATCH_' . date('YmdHis') . '_' . $i,
                'name' => '批量测试人员' . ($i + 1),
                'sfz' => '510000' . date('Ymd') . sprintf('%04d', $i + 1),
                'score' => rand(60, 95),
                'location' => '批量测试地点' . rand(1, 5),
                'lat' => 30.5371 + (rand(-1000, 1000) / 10000),
                'lon' => 106.44307 + (rand(-1000, 1000) / 10000),
                'severity' => ['low', 'medium', 'high'][rand(0, 2)]
            ];
            
            $sql = "INSERT INTO Kunlun_alarm_records (
                alarm_id, alarm_no, person_name, person_sfz, score, 
                location_name, latitude, longitude, severity, 
                captured_time, created_time
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
            
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ssssdsdds", 
                $testData['id'],
                $testData['alarmNo'],
                $testData['name'],
                $testData['sfz'],
                $testData['score'],
                $testData['location'],
                $testData['lat'],
                $testData['lon'],
                $testData['severity']
            );
            
            if ($stmt->execute()) {
                $successCount++;
            } else {
                $errorCount++;
            }
        } catch (Exception $e) {
            $errorCount++;
        }
    }
    
    // 记录操作日志
    global $APP_ID;
    logOperation($conn, $user_id, $APP_ID, "生成批量测试数据，成功: $successCount 条，失败: $errorCount 条");
    
    echo json_encode([
        'status' => 1,
        'message' => "批量测试数据生成完成，成功: $successCount 条，失败: $errorCount 条",
        'data' => [
            'success' => $successCount,
            'error' => $errorCount,
            'total' => $count
        ]
    ]);
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预警系统测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section h3 {
            margin: 0 0 1rem 0;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            margin: 0.25rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        
        .btn:hover { opacity: 0.8; }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        
        .result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .result-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .result-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 1rem;
            color: #666;
        }
        
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 0.5rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 预警系统测试</h1>
            <p>全面测试预警系统的各个组件和功能</p>
        </div>
        
        <div class="grid">
            <!-- 数据库测试 -->
            <div class="test-section">
                <h3>🗄️ 数据库测试</h3>
                <p>测试数据库连接和表结构</p>
                <button class="btn btn-primary" onclick="testDatabase()">测试数据库</button>
                <div id="databaseResult"></div>
            </div>
            
            <!-- SSE连接测试 -->
            <div class="test-section">
                <h3>📡 SSE连接测试</h3>
                <p>测试服务器发送事件连接</p>
                <button class="btn btn-primary" onclick="testSSE()">测试SSE连接</button>
                <div id="sseResult"></div>
            </div>
            
            <!-- 短信服务测试 -->
            <div class="test-section">
                <h3>📱 短信服务测试</h3>
                <div class="form-group">
                    <label>测试手机号:</label>
                    <input type="tel" class="form-control" id="testPhone" placeholder="请输入测试手机号">
                </div>
                <div class="form-group">
                    <label>短信内容:</label>
                    <textarea class="form-control" id="testContent" rows="3">【测试】这是一条系统测试短信，请忽略。</textarea>
                </div>
                <button class="btn btn-warning" onclick="testSms()">发送测试短信</button>
                <div id="smsResult"></div>
            </div>
            
            <!-- 模拟预警数据 -->
            <div class="test-section">
                <h3>🚨 模拟预警数据</h3>
                <p>生成模拟预警数据进行测试</p>
                <button class="btn btn-success" onclick="simulateAlarm()">生成模拟预警</button>
                <div id="alarmResult"></div>
            </div>
            
            <!-- 完整流程测试 -->
            <div class="test-section">
                <h3>🔄 完整流程测试</h3>
                <p>测试从预警生成到短信发送的完整流程</p>
                <button class="btn btn-danger" onclick="testFullFlow()">测试完整流程</button>
                <div id="flowResult"></div>
            </div>
            
            <!-- 批量测试数据 -->
            <div class="test-section">
                <h3>📊 批量测试数据</h3>
                <div class="form-group">
                    <label>生成数量:</label>
                    <input type="number" class="form-control" id="batchCount" value="10" min="1" max="100">
                </div>
                <button class="btn btn-warning" onclick="generateTestData()">生成批量数据</button>
                <div id="batchResult"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔗 快速链接</h3>
            <button class="btn btn-primary" onclick="window.open('system_manager.php')">系统管理器</button>
            <button class="btn btn-primary" onclick="window.open('sms_admin/')">短信管理后台</button>
            <button class="btn btn-primary" onclick="window.open('alarm-display.html')">实时监控页面</button>
            <button class="btn btn-primary" onclick="window.open('install_database.php')">数据库安装</button>
        </div>
    </div>
    
    <script>
        // 测试数据库
        async function testDatabase() {
            await executeTest('testDatabase', 'databaseResult', '数据库测试');
        }
        
        // 测试SSE连接
        async function testSSE() {
            await executeTest('testSSE', 'sseResult', 'SSE连接测试');
        }
        
        // 测试短信服务
        async function testSms() {
            const phone = document.getElementById('testPhone').value;
            const content = document.getElementById('testContent').value;
            
            if (!phone) {
                showResult('smsResult', 'error', '请输入测试手机号');
                return;
            }
            
            await executeTest('testSms', 'smsResult', '短信服务测试', { phone, content });
        }
        
        // 模拟预警数据
        async function simulateAlarm() {
            await executeTest('simulateAlarm', 'alarmResult', '模拟预警数据');
        }
        
        // 测试完整流程
        async function testFullFlow() {
            await executeTest('testFullFlow', 'flowResult', '完整流程测试');
        }
        
        // 生成批量测试数据
        async function generateTestData() {
            const count = document.getElementById('batchCount').value;
            await executeTest('generateTestData', 'batchResult', '批量测试数据', { count });
        }
        
        // 执行测试
        async function executeTest(action, resultId, description, extraData = {}) {
            const resultDiv = document.getElementById(resultId);
            showLoading(resultDiv, `正在执行${description}...`);
            
            try {
                const formData = new FormData();
                formData.append('action', action);
                
                for (const [key, value] of Object.entries(extraData)) {
                    formData.append(key, value);
                }
                
                const response = await fetch('system_test.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.status === 1) {
                    showResult(resultId, 'success', `${description}成功:\n${data.message}\n\n详细结果:\n${JSON.stringify(data.data, null, 2)}`);
                } else {
                    showResult(resultId, 'error', `${description}失败:\n${data.message}`);
                }
            } catch (error) {
                showResult(resultId, 'error', `${description}异常:\n${error.message}`);
            }
        }
        
        // 显示加载状态
        function showLoading(element, message) {
            element.innerHTML = `
                <div class="loading" style="display: block;">
                    <div class="spinner"></div>
                    ${message}
                </div>
            `;
        }
        
        // 显示结果
        function showResult(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result result-${type}">${message}</div>`;
        }
    </script>
</body>
</html>
