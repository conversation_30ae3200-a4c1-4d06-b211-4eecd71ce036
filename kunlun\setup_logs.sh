#!/bin/bash
# 设置日志目录和权限
# 创建时间: 2025-08-12

echo "设置昆仑预警系统日志目录..."

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="$SCRIPT_DIR/logs"

echo "脚本目录: $SCRIPT_DIR"
echo "日志目录: $LOG_DIR"

# 检查当前用户
echo "当前用户: $(whoami)"
echo "当前用户ID: $(id)"

# 创建日志目录
if [ ! -d "$LOG_DIR" ]; then
    echo "创建日志目录..."
    mkdir -p "$LOG_DIR"
    
    if [ $? -eq 0 ]; then
        echo "日志目录创建成功"
    else
        echo "错误: 无法创建日志目录"
        echo "请尝试使用sudo权限:"
        echo "sudo mkdir -p $LOG_DIR"
        echo "sudo chown $(whoami):$(whoami) $LOG_DIR"
        echo "sudo chmod 755 $LOG_DIR"
        exit 1
    fi
else
    echo "日志目录已存在"
fi

# 设置权限
echo "设置目录权限..."
chmod 755 "$LOG_DIR"

if [ $? -eq 0 ]; then
    echo "权限设置成功"
else
    echo "警告: 权限设置失败，可能需要sudo权限"
fi

# 检查权限
echo "检查目录权限:"
ls -la "$SCRIPT_DIR" | grep logs

# 测试写入权限
TEST_FILE="$LOG_DIR/test_write.tmp"
echo "测试写入权限..."

if touch "$TEST_FILE" 2>/dev/null; then
    echo "写入权限测试成功"
    rm -f "$TEST_FILE"
else
    echo "错误: 写入权限测试失败"
    echo "请检查目录权限或使用以下命令修复:"
    echo "sudo chown -R $(whoami):$(whoami) $LOG_DIR"
    echo "sudo chmod -R 755 $LOG_DIR"
    exit 1
fi

# 查找PHP CLI路径
echo ""
echo "查找PHP CLI路径..."

PHP_PATHS=(
    "/usr/bin/php"
    "/usr/local/bin/php"
    "/www/server/php/80/bin/php"
    "/www/server/php/81/bin/php"
    "/www/server/php/74/bin/php"
)

FOUND_PHP=""
for php_path in "${PHP_PATHS[@]}"; do
    if [ -f "$php_path" ] && [ -x "$php_path" ]; then
        echo "找到PHP: $php_path"
        VERSION=$($php_path -v 2>/dev/null | head -n1)
        echo "版本: $VERSION"
        
        if [[ "$VERSION" =~ "cli" ]]; then
            echo "这是CLI版本 ✓"
            if [ -z "$FOUND_PHP" ]; then
                FOUND_PHP="$php_path"
            fi
        else
            echo "这不是CLI版本"
        fi
        echo ""
    fi
done

if [ -n "$FOUND_PHP" ]; then
    echo "推荐使用的PHP路径: $FOUND_PHP"
    
    # 测试脚本执行
    SCRIPT_FILE="$SCRIPT_DIR/background_processor.php"
    if [ -f "$SCRIPT_FILE" ]; then
        echo "测试脚本执行..."
        TEST_OUTPUT=$($FOUND_PHP "$SCRIPT_FILE" --test 2>&1)
        
        if [[ "$TEST_OUTPUT" =~ "test mode started" ]]; then
            echo "脚本测试成功 ✓"
        else
            echo "脚本测试失败:"
            echo "$TEST_OUTPUT"
        fi
    fi
else
    echo "警告: 未找到合适的PHP CLI版本"
    echo "请安装PHP CLI或检查路径"
fi

echo ""
echo "设置完成！"
echo ""
echo "如果仍有权限问题，请运行:"
echo "sudo chown -R \$(whoami):\$(whoami) $SCRIPT_DIR"
echo "sudo chmod -R 755 $SCRIPT_DIR"
