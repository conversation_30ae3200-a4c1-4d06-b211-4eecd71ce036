<?php
/**
 * 调试页面 - 帮助诊断任务执行问题
 */

// 数据库配置
$db_config = [
    'host' => '127.0.0.1',
    'port' => 3306,
    'database' => 'taskmgr',
    'username' => 'root',
    'password' => 'YC@yc110',
    'charset' => 'utf8mb4'
];

// 数据库连接
function getDB() {
    global $db_config;
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['database']};charset={$db_config['charset']}";
            $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]);
        } catch (PDOException $e) {
            die("数据库连接失败: " . $e->getMessage());
        }
    }
    
    return $pdo;
}

// 如果是POST请求，执行测试任务
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_command'])) {
    $command = $_POST['test_command'];
    
    echo "<h2>🧪 命令测试结果</h2>";
    echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<strong>执行命令:</strong> " . htmlspecialchars($command) . "<br><br>";
    
    $start = microtime(true);
    $output = '';
    $exit_code = 0;

    // 尝试不同的执行函数
    if (function_exists('exec')) {
        exec($command . ' 2>&1', $output_lines, $exit_code);
        $output = implode("\n", $output_lines);
    } elseif (function_exists('shell_exec')) {
        $output = shell_exec($command . ' 2>&1');
        $exit_code = 0; // shell_exec无法获取退出码
    } elseif (function_exists('system')) {
        ob_start();
        $exit_code = system($command . ' 2>&1');
        $output = ob_get_clean();
    } elseif (function_exists('passthru')) {
        ob_start();
        passthru($command . ' 2>&1', $exit_code);
        $output = ob_get_clean();
    } else {
        $output = "错误：所有命令执行函数都被禁用";
        $exit_code = -1;
    }

    $duration = round(microtime(true) - $start, 2);
    
    echo "<strong>退出码:</strong> $exit_code " . ($exit_code === 0 ? '✅' : '❌') . "<br>";
    echo "<strong>执行时间:</strong> {$duration}秒<br>";
    echo "<strong>输出内容:</strong><br>";
    echo "<pre style='background: white; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars($output ?: '(无输出)');
    echo "</pre>";
    echo "</div>";
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理器调试工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        h1, h2 {
            color: #333;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        th, td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-failed {
            color: #dc3545;
            font-weight: bold;
        }
        
        input, textarea, button {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
            border: none;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .test-form {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .quick-test {
            display: inline-block;
            margin: 5px;
            padding: 5px 10px;
            background: #6c757d;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .quick-test:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="card">
        <h1>🔧 任务管理器调试工具</h1>
        <p>这个工具可以帮助您诊断任务执行问题</p>
        <p><a href="index.php">← 返回主界面</a></p>
    </div>

    <!-- 数据库状态检查 -->
    <div class="card">
        <h2>📊 数据库状态</h2>
        <?php
        try {
            $pdo = getDB();
            echo "✅ 数据库连接正常<br>";
            
            // 检查表结构
            $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
            echo "📋 数据表: " . implode(', ', $tables) . "<br>";
            
            // 检查任务数量
            $taskCount = $pdo->query("SELECT COUNT(*) FROM tasks")->fetchColumn();
            echo "📝 任务总数: $taskCount<br>";
            
            // 检查日志数量
            $logCount = $pdo->query("SELECT COUNT(*) FROM task_logs")->fetchColumn();
            echo "📄 日志总数: $logCount<br>";
            
        } catch (Exception $e) {
            echo "❌ 数据库错误: " . $e->getMessage();
        }
        ?>
    </div>

    <!-- 任务列表 -->
    <div class="card">
        <h2>📋 任务列表</h2>
        <?php
        try {
            $pdo = getDB();
            $tasks = $pdo->query("SELECT * FROM tasks ORDER BY id")->fetchAll();
            
            if (empty($tasks)) {
                echo "<p>暂无任务</p>";
            } else {
                echo "<table>";
                echo "<tr><th>ID</th><th>名称</th><th>命令</th><th>状态</th><th>执行次数</th><th>上次执行</th></tr>";
                foreach ($tasks as $task) {
                    echo "<tr>";
                    echo "<td>{$task['id']}</td>";
                    echo "<td>" . htmlspecialchars($task['name']) . "</td>";
                    echo "<td>" . htmlspecialchars(substr($task['command'], 0, 50)) . "...</td>";
                    echo "<td>{$task['status']}</td>";
                    echo "<td>{$task['run_count']}</td>";
                    echo "<td>" . ($task['last_run'] ?: '从未执行') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } catch (Exception $e) {
            echo "❌ 查询错误: " . $e->getMessage();
        }
        ?>
    </div>

    <!-- 执行日志 -->
    <div class="card">
        <h2>📄 最近执行日志</h2>
        <?php
        try {
            $pdo = getDB();
            $logs = $pdo->query("
                SELECT l.*, t.name as task_name 
                FROM task_logs l 
                LEFT JOIN tasks t ON l.task_id = t.id 
                ORDER BY l.start_time DESC 
                LIMIT 10
            ")->fetchAll();
            
            if (empty($logs)) {
                echo "<p>暂无执行日志</p>";
            } else {
                echo "<table>";
                echo "<tr><th>任务名称</th><th>状态</th><th>开始时间</th><th>耗时</th><th>输出</th><th>错误信息</th></tr>";
                foreach ($logs as $log) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($log['task_name'] ?: '未知任务') . "</td>";
                    echo "<td class='status-{$log['status']}'>{$log['status']}</td>";
                    echo "<td>{$log['start_time']}</td>";
                    echo "<td>{$log['duration']}秒</td>";
                    echo "<td>" . htmlspecialchars(substr($log['output'] ?: '', 0, 50)) . "...</td>";
                    echo "<td>" . htmlspecialchars($log['error_message'] ?: '') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } catch (Exception $e) {
            echo "❌ 查询错误: " . $e->getMessage();
        }
        ?>
    </div>

    <!-- 命令测试工具 -->
    <div class="card">
        <h2>🧪 命令测试工具</h2>
        <p>在这里测试命令是否能正常执行</p>
        
        <div class="test-form">
            <form method="POST">
                <div style="margin-bottom: 10px;">
                    <label>测试命令:</label><br>
                    <input type="text" name="test_command" style="width: 70%;" placeholder="例如: df -h" value="<?= htmlspecialchars($_POST['test_command'] ?? '') ?>">
                    <button type="submit">执行测试</button>
                </div>
            </form>
            
            <div>
                <strong>常用命令:</strong><br>
                <span class="quick-test" onclick="setCommand('df -h')">磁盘空间</span>
                <span class="quick-test" onclick="setCommand('free -m')">内存使用</span>
                <span class="quick-test" onclick="setCommand('uptime')">系统运行时间</span>
                <span class="quick-test" onclick="setCommand('date')">当前时间</span>
                <span class="quick-test" onclick="setCommand('whoami')">当前用户</span>
                <span class="quick-test" onclick="setCommand('pwd')">当前目录</span>
                <span class="quick-test" onclick="setCommand('ls -la')">列出文件</span>
            </div>
        </div>
    </div>

    <!-- 系统信息 -->
    <div class="card">
        <h2>💻 系统信息</h2>
        <?php
        echo "🐘 PHP版本: " . PHP_VERSION . "<br>";
        echo "💾 操作系统: " . PHP_OS . "<br>";
        echo "⏰ 当前时间: " . date('Y-m-d H:i:s') . "<br>";
        echo "📁 当前目录: " . __DIR__ . "<br>";
        echo "👤 Web服务器用户: " . get_current_user() . "<br>";
        
        // 检查函数可用性
        $functions = ['shell_exec', 'exec', 'system', 'passthru'];
        echo "🔧 可用函数: ";
        foreach ($functions as $func) {
            if (function_exists($func)) {
                echo "<span style='color: green;'>$func ✅</span> ";
            } else {
                echo "<span style='color: red;'>$func ❌</span> ";
            }
        }
        ?>
    </div>

    <script>
        function setCommand(cmd) {
            document.querySelector('input[name="test_command"]').value = cmd;
        }
    </script>
</body>
</html>
