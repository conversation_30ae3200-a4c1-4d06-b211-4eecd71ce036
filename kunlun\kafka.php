<?php
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('X-Accel-Buffering: no');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');
ini_set('output_buffering', 'off');
ini_set('zlib.output_compression', false);
ini_set('max_execution_time', 0);
date_default_timezone_set('Asia/Shanghai');

// 错误处理函数
function sendError($message) {
    echo "event: error\n";
    echo "data: " . json_encode(['error' => $message, 'timestamp' => time()], JSON_UNESCAPED_UNICODE) . "\n\n";
    ob_flush();
    flush();
}

// 发送连接状态
function sendConnectionStatus($status, $message = '') {
    echo "event: connection\n";
    echo "data: " . json_encode([
        'status' => $status,
        'message' => $message,
        'timestamp' => time(),
        'server_time' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE) . "\n\n";
    ob_flush();
    flush();
}

// 发送心跳
function sendHeartbeat() {
    echo "event: heartbeat\n";
    echo "data: " . json_encode([
        'timestamp' => time(),
        'server_time' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE) . "\n\n";
    ob_flush();
    flush();
}

// 下载图片并转换为base64
function downloadImageAsBase64($imageUrl) {
    if (empty($imageUrl)) {
        return '';
    }

    try {
        // 设置超时和用户代理
        $context = stream_context_create([
            'http' => [
                'timeout' => 10, // 10秒超时
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'follow_location' => true,
                'max_redirects' => 3
            ]
        ]);

        // 下载图片数据
        $imageData = file_get_contents($imageUrl, false, $context);

        if ($imageData === false) {
            error_log("无法下载图片: $imageUrl");
            return '';
        }

        // 检测图片类型
        $finfo = new finfo(FILEINFO_MIME_TYPE);
        $mimeType = $finfo->buffer($imageData);

        // 验证是否为图片
        if (!in_array($mimeType, ['image/jpeg', 'image/png', 'image/gif', 'image/webp'])) {
            error_log("不支持的图片类型: $mimeType for URL: $imageUrl");
            return '';
        }

        // 转换为base64
        $base64 = base64_encode($imageData);
        return "data:$mimeType;base64,$base64";

    } catch (Exception $e) {
        error_log("下载图片时发生错误: " . $e->getMessage() . " URL: $imageUrl");
        return '';
    }
}

try {
    // 发送初始连接状态
    sendConnectionStatus('connected', 'SSE连接已建立');
    
    $conf = new RdKafka\Conf();
    $conf->set('group.id', 'G21811886847A24B00011');
    $conf->set('metadata.broker.list', '80.164.12.151:9192');
    $conf->set('session.timeout.ms', 6000);
    $conf->set('auto.offset.reset', 'latest');
    
    $consumer = new RdKafka\KafkaConsumer($conf);
    $consumer->subscribe(['face_alarm_000005']);
    
    sendConnectionStatus('kafka_connected', 'Kafka消费者已连接');
    
    $lastHeartbeat = time();
    $heartbeatInterval = 30; // 30秒发送一次心跳
    
    while (true) {
        // 检查客户端连接状态
        if (connection_aborted()) {
            break;
        }
        
        // 定期发送心跳
        if (time() - $lastHeartbeat >= $heartbeatInterval) {
            sendHeartbeat();
            $lastHeartbeat = time();
        }
        
        $message = $consumer->consume(1000); // 1秒超时
        
        switch ($message->err) {
            case RD_KAFKA_RESP_ERR_NO_ERROR:
                $alarmData = json_decode($message->payload, true);
                
                if ($alarmData === null) {
                    sendError('无效的JSON数据');
                    continue 2;
                }
                
                // 下载并转换图片
                $imageUrl = $alarmData['fullUrl'] ?? '';
                $imageBase64 = downloadImageAsBase64($imageUrl);

                // 格式化报警数据
                $eventData = [
                    'id' => $alarmData['id'] ?? uniqid(),
                    'alarmNo' => $alarmData['alarmNo'] ?? '未知',
                    'time' => isset($alarmData['capturedTime']) ?
                        date('Y-m-d H:i:s', $alarmData['capturedTime']/1000) :
                        date('Y-m-d H:i:s'),
                    'score' => $alarmData['score'] ?? 0,
                    'name' => $alarmData['photo']['name'] ?? '未知',
                    'sfz' => $alarmData['photo']['identity']['sfz'] ?? '未知',
                    'location' => $alarmData['camera']['name'] ?? '未知位置',
                    'lat' => $alarmData['camera']['lat'] ?? 0,
                    'lon' => $alarmData['camera']['lon'] ?? 0,
                    'image' => $imageBase64, // 使用base64编码的图片
                    'imageUrl' => $imageUrl, // 保留原始URL用于调试
                    'album' => $alarmData['album']['name'] ?? '未知图库',
                    'severity' => $alarmData['score'] >= 90 ? 'high' :
                                ($alarmData['score'] >= 70 ? 'medium' : 'low')
                ];
                
                // 发送SSE事件
                echo "event: alarm\n";
                echo "data: " . json_encode($eventData, JSON_UNESCAPED_UNICODE) . "\n\n";
                ob_flush();
                flush();
                break;
                
            case RD_KAFKA_RESP_ERR__PARTITION_EOF:
                // 分区结束，继续等待
                break;
                
            case RD_KAFKA_RESP_ERR__TIMED_OUT:
                // 超时，继续循环
                break;
                
            default:
                $errorMsg = sprintf('Kafka错误: %s (代码: %d)', $message->errstr(), $message->err);
                sendError($errorMsg);
                error_log($errorMsg);
                
                // 对于严重错误，尝试重新连接
                if ($message->err !== RD_KAFKA_RESP_ERR__TRANSPORT) {
                    sleep(5);
                    sendConnectionStatus('reconnecting', '尝试重新连接Kafka...');
                }
                break;
        }
    }
    
} catch (Exception $e) {
    $errorMsg = 'SSE服务器错误: ' . $e->getMessage();
    sendError($errorMsg);
    error_log($errorMsg);
} finally {
    sendConnectionStatus('disconnected', 'SSE连接已断开');
}
?>
