<?php
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('X-Accel-Buffering: no');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');
ini_set('output_buffering', 'off');
ini_set('zlib.output_compression', false);
ini_set('max_execution_time', 0);
date_default_timezone_set('Asia/Shanghai');

// 错误处理函数
function sendError($message) {
    echo "event: error\n";
    echo "data: " . json_encode(['error' => $message, 'timestamp' => time()], JSON_UNESCAPED_UNICODE) . "\n\n";
    ob_flush();
    flush();
}

// 发送连接状态
function sendConnectionStatus($status, $message = '') {
    echo "event: connection\n";
    echo "data: " . json_encode([
        'status' => $status,
        'message' => $message,
        'timestamp' => time(),
        'server_time' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE) . "\n\n";
    ob_flush();
    flush();
}

// 发送心跳
function sendHeartbeat() {
    echo "event: heartbeat\n";
    echo "data: " . json_encode([
        'timestamp' => time(),
        'server_time' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE) . "\n\n";
    ob_flush();
    flush();
}

// 验证图片URL有效性（可选）
function validateImageUrl($imageUrl) {
    if (empty($imageUrl)) {
        return false;
    }

    // 基本URL格式验证
    if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
        return false;
    }

    // 检查是否为图片URL（基于扩展名）
    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    $pathInfo = pathinfo(parse_url($imageUrl, PHP_URL_PATH));
    $extension = strtolower($pathInfo['extension'] ?? '');

    return in_array($extension, $allowedExtensions);
}

try {
    // 发送初始连接状态
    sendConnectionStatus('connected', 'SSE连接已建立');
    
    $conf = new RdKafka\Conf();
    $conf->set('group.id', 'G21811886847A24B00011');
    $conf->set('metadata.broker.list', '80.164.12.151:9192');
    $conf->set('session.timeout.ms', 6000);
    $conf->set('auto.offset.reset', 'latest');
    
    $consumer = new RdKafka\KafkaConsumer($conf);
    $consumer->subscribe(['face_alarm_000005']);
    
    sendConnectionStatus('kafka_connected', 'Kafka消费者已连接');
    
    $lastHeartbeat = time();
    $heartbeatInterval = 30; // 30秒发送一次心跳
    
    while (true) {
        // 检查客户端连接状态
        if (connection_aborted()) {
            break;
        }
        
        // 定期发送心跳
        if (time() - $lastHeartbeat >= $heartbeatInterval) {
            sendHeartbeat();
            $lastHeartbeat = time();
        }
        
        $message = $consumer->consume(1000); // 1秒超时
        
        switch ($message->err) {
            case RD_KAFKA_RESP_ERR_NO_ERROR:
                $alarmData = json_decode($message->payload, true);
                
                if ($alarmData === null) {
                    sendError('无效的JSON数据');
                    continue 2;
                }
                
                // 获取图片URL
                $imageUrl = $alarmData['fullUrl'] ?? '';

                // 格式化报警数据
                $eventData = [
                    'id' => $alarmData['id'] ?? uniqid(),
                    'alarmNo' => $alarmData['alarmNo'] ?? '未知',
                    'time' => isset($alarmData['capturedTime']) ?
                        date('Y-m-d H:i:s', $alarmData['capturedTime']/1000) :
                        date('Y-m-d H:i:s'),
                    'score' => $alarmData['score'] ?? 0,
                    'name' => $alarmData['photo']['name'] ?? '未知',
                    'sfz' => $alarmData['photo']['identity']['sfz'] ?? '未知',
                    'location' => $alarmData['camera']['name'] ?? '未知位置',
                    'lat' => $alarmData['camera']['lat'] ?? 0,
                    'lon' => $alarmData['camera']['lon'] ?? 0,
                    'image' => $imageUrl, // 直接使用原始图片URL
                    'imageUrl' => $imageUrl, // 保留原始URL字段以保持兼容性
                    'album' => $alarmData['album']['name'] ?? '未知图库',
                    'severity' => $alarmData['score'] >= 90 ? 'high' :
                                ($alarmData['score'] >= 70 ? 'medium' : 'low')
                ];
                
                // 发送SSE事件
                echo "event: alarm\n";
                echo "data: " . json_encode($eventData, JSON_UNESCAPED_UNICODE) . "\n\n";
                ob_flush();
                flush();
                break;
                
            case RD_KAFKA_RESP_ERR__PARTITION_EOF:
                // 分区结束，继续等待
                break;
                
            case RD_KAFKA_RESP_ERR__TIMED_OUT:
                // 超时，继续循环
                break;
                
            default:
                $errorMsg = sprintf('Kafka错误: %s (代码: %d)', $message->errstr(), $message->err);
                sendError($errorMsg);
                error_log($errorMsg);
                
                // 对于严重错误，尝试重新连接
                if ($message->err !== RD_KAFKA_RESP_ERR__TRANSPORT) {
                    sleep(5);
                    sendConnectionStatus('reconnecting', '尝试重新连接Kafka...');
                }
                break;
        }
    }
    
} catch (Exception $e) {
    $errorMsg = 'SSE服务器错误: ' . $e->getMessage();
    sendError($errorMsg);
    error_log($errorMsg);
} finally {
    sendConnectionStatus('disconnected', 'SSE连接已断开');
}
?>
