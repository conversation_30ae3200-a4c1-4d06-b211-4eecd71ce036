<?php
/**
 * 基础数据库连接（不依赖mysqli和PDO扩展）
 * 使用原生MySQL函数或文件存储
 * 创建时间: 2025-08-12
 */

// 数据库配置
$servername = "localhost";
$username = "root";
$password = "YC@yc110";
$dbname = "application";

// 检查可用的数据库扩展
$db_method = 'none';
$conn = null;

// 1. 尝试使用mysql扩展（PHP 5.x的旧扩展）
if (function_exists('mysql_connect')) {
    $db_method = 'mysql';
    try {
        $link = mysql_connect($servername, $username, $password);
        if ($link) {
            mysql_select_db($dbname, $link);
            $conn = new MySQLWrapper($link);
        }
    } catch (Exception $e) {
        error_log("MySQL connection failed: " . $e->getMessage());
    }
}

// 2. 如果没有数据库扩展，使用文件存储模拟
if (!$conn) {
    $db_method = 'file';
    $conn = new FileDBWrapper();
}

// MySQL扩展包装类
class MySQLWrapper {
    private $link;
    public $insert_id = 0;
    public $affected_rows = 0;
    public $error = '';
    
    public function __construct($link) {
        $this->link = $link;
    }
    
    public function query($sql) {
        $result = mysql_query($sql, $this->link);
        if ($result === false) {
            $this->error = mysql_error($this->link);
            return false;
        }
        
        $this->insert_id = mysql_insert_id($this->link);
        $this->affected_rows = mysql_affected_rows($this->link);
        
        if (is_resource($result)) {
            return new MySQLResultWrapper($result);
        }
        return true;
    }
    
    public function prepare($sql) {
        // 简单的预处理模拟
        return new MySQLStatementWrapper($this, $sql);
    }
    
    public function real_escape_string($string) {
        return mysql_real_escape_string($string, $this->link);
    }
    
    public function close() {
        return mysql_close($this->link);
    }
}

class MySQLResultWrapper {
    private $result;
    public $num_rows;
    
    public function __construct($result) {
        $this->result = $result;
        $this->num_rows = mysql_num_rows($result);
    }
    
    public function fetch_assoc() {
        return mysql_fetch_assoc($this->result);
    }
    
    public function fetch_all($mode = null) {
        $rows = [];
        while ($row = mysql_fetch_assoc($this->result)) {
            $rows[] = $row;
        }
        return $rows;
    }
    
    public function free() {
        mysql_free_result($this->result);
    }
}

class MySQLStatementWrapper {
    private $conn;
    private $sql;
    private $params = [];
    
    public function __construct($conn, $sql) {
        $this->conn = $conn;
        $this->sql = $sql;
    }
    
    public function bind_param($types, ...$params) {
        $this->params = $params;
        return true;
    }
    
    public function execute() {
        $sql = $this->sql;
        
        // 简单的参数替换
        foreach ($this->params as $param) {
            $escaped = $this->conn->real_escape_string($param);
            $sql = preg_replace('/\?/', "'$escaped'", $sql, 1);
        }
        
        return $this->conn->query($sql);
    }
    
    public function get_result() {
        return $this->execute();
    }
}

// 文件存储包装类（当没有数据库扩展时使用）
class FileDBWrapper {
    private $data_dir;
    public $insert_id = 0;
    public $affected_rows = 0;
    public $error = '';
    
    public function __construct() {
        $this->data_dir = __DIR__ . '/data';
        if (!is_dir($this->data_dir)) {
            @mkdir($this->data_dir, 0755, true);
        }
    }
    
    public function query($sql) {
        // 简单的SQL解析和文件操作
        $sql = trim($sql);
        
        if (stripos($sql, 'SELECT') === 0) {
            return $this->handleSelect($sql);
        } elseif (stripos($sql, 'INSERT') === 0) {
            return $this->handleInsert($sql);
        } elseif (stripos($sql, 'UPDATE') === 0) {
            return $this->handleUpdate($sql);
        } elseif (stripos($sql, 'DELETE') === 0) {
            return $this->handleDelete($sql);
        }
        
        return true;
    }
    
    private function handleSelect($sql) {
        // 返回模拟数据
        $mockData = [
            ['id' => 1, 'name' => 'Test Config', 'is_active' => 1],
            ['id' => 2, 'name' => 'Test Config 2', 'is_active' => 0]
        ];
        
        return new FileResultWrapper($mockData);
    }
    
    private function handleInsert($sql) {
        $this->insert_id = rand(1000, 9999);
        $this->affected_rows = 1;
        return true;
    }
    
    private function handleUpdate($sql) {
        $this->affected_rows = 1;
        return true;
    }
    
    private function handleDelete($sql) {
        $this->affected_rows = 1;
        return true;
    }
    
    public function prepare($sql) {
        return new FileStatementWrapper($this, $sql);
    }
    
    public function real_escape_string($string) {
        return addslashes($string);
    }
    
    public function close() {
        return true;
    }
}

class FileResultWrapper {
    private $data;
    public $num_rows;
    private $position = 0;
    
    public function __construct($data) {
        $this->data = $data;
        $this->num_rows = count($data);
    }
    
    public function fetch_assoc() {
        if ($this->position < count($this->data)) {
            return $this->data[$this->position++];
        }
        return null;
    }
    
    public function fetch_all($mode = null) {
        return $this->data;
    }
    
    public function free() {
        $this->data = null;
    }
}

class FileStatementWrapper {
    private $conn;
    private $sql;
    private $params = [];
    
    public function __construct($conn, $sql) {
        $this->conn = $conn;
        $this->sql = $sql;
    }
    
    public function bind_param($types, ...$params) {
        $this->params = $params;
        return true;
    }
    
    public function execute() {
        return $this->conn->query($this->sql);
    }
    
    public function get_result() {
        return $this->execute();
    }
}

// 兼容函数
if (!function_exists('mysqli_connect')) {
    function mysqli_connect($host, $username, $password, $database) {
        global $conn;
        return $conn;
    }
}

if (!function_exists('mysqli_query')) {
    function mysqli_query($conn, $sql) {
        return $conn->query($sql);
    }
}

if (!function_exists('mysqli_prepare')) {
    function mysqli_prepare($conn, $sql) {
        return $conn->prepare($sql);
    }
}

// 定义常量
if (!defined('MYSQLI_ASSOC')) {
    define('MYSQLI_ASSOC', 1);
}

// 记录使用的数据库方法
error_log("Database connection method: $db_method");

?>
