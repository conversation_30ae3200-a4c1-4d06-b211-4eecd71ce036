import{_ as F,c as d,r as l,o as a,w as s,a as m,b as e,d as r,F as _,e as f,u as h,n as y,f as u,t as n,E as N,g as P,i as S,h as V}from"./index-gqi1S6ey.js";import{_ as I}from"./logo-LbKsNqzq.js";/* empty css              */const $={class:"category-container"},L={class:"app-category"},T={class:"card-grid"},q=["onClick"],z={key:0,class:"icon-emoji"},D={class:"app-info"},O={class:"app-category"},G={class:"card-grid"},H=["onClick"],J={key:0,class:"icon-emoji"},K={class:"app-info"},M={class:"app-category"},Q={class:"card-grid"},R=["onClick"],U={key:0,class:"icon-emoji"},W={class:"app-info"},X={__name:"Front",setup(b){const i=[{name:"通讯录",path:"/address_book.html",desc:"查询人员联系方式",color:"linear-gradient(135deg, #0077dd 0%, #5c70ff 100%)",icon:"📞",category:"public"},{name:"通讯录管理",path:"/address_book_management.html",desc:"管理人员联系方式",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",icon:"📋",category:"private"},{name:"人员调动模拟",path:"PerSimTS",desc:"模拟部门人员调动",color:"linear-gradient(135deg, #f15312 0%, #ff8968 100%)",icon:"🔄",category:"private"},{name:"警情质量监督",path:"pcqss",desc:"警情质量监督",color:"linear-gradient(135deg, #1154eb 0%, #5c70ff 100%)",icon:"🚨",category:"private"},{name:"文件快递柜",path:"fileCloud",desc:"文件上传与下载",color:"linear-gradient(135deg, #fa541c 0%, #ff8968 100%)",icon:"📦",category:"private"},{name:"学生信息查询系统",path:"schInfo",desc:"学生信息查询",color:"linear-gradient(135deg, #2f54eb 0%, #5c70ff 100%)",icon:"🎓",category:"private"},{name:"数据大屏",path:"/back.html",desc:"岳池县110数据大屏预览",color:"linear-gradient(135deg, #722ed1 0%, #9254de 100%)",icon:"📊",category:"private"},{name:"值班管理",path:"/back.html",desc:"岳池县公安局内部值班管理",color:"linear-gradient(135deg, #13c2c2 0%, #36cfc9 100%)",icon:"🕐",category:"private"},{name:"重点人员管理",path:"/back.html",desc:"重点人员基本信息，动向管理",color:"linear-gradient(135deg, #eb2f96 0%, #f759ab 100%)",icon:"👥",category:"private"},{name:"重大事件管理",path:"/back.html",desc:"重点事件基本信息，处置情况管理",color:"linear-gradient(135deg, #faad14 0%, #ffc53d 100%)",icon:"⚠️",category:"private"},{name:"单位管理",path:"/back.html",desc:"",color:"linear-gradient(135deg, #52c41a 0%, #73d13d 100%)",icon:"🏢",category:"system"},{name:"用户管理",path:"/back.html",desc:"",color:"linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)",icon:"👤",category:"system"},{name:"授权管理",path:"/back.html",desc:"",color:"linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%)",icon:"🔐",category:"system"},{name:"应用管理",path:"/back.html",desc:"",color:"linear-gradient(135deg, #a0d911 0%, #b7eb8f 100%)",icon:"📱",category:"system"},{name:"角色管理",path:"/back.html",desc:"",color:"linear-gradient(135deg, #fa8c16 0%, #ffb347 100%)",icon:"🎭",category:"system"}],C=i.filter(c=>c.category==="public"),w=i.filter(c=>c.category==="private"),x=i.filter(c=>c.category==="system"),g=c=>{if(c==="/undefined"){N({title:"提示",message:"应用开发中，预计上线时间：2025年07月28日！",type:"info",duration:3e3});return}else window.location.href=c};return(c,t)=>{const j=l("el-header"),p=l("el-card"),A=l("el-main"),B=l("el-footer"),E=l("el-container");return a(),d(E,{class:"portal-container"},{default:s(()=>[m(j,{class:"header"},{default:s(()=>t[0]||(t[0]=[e("div",{class:"header-left"},[e("img",{src:I,alt:"Logo",class:"header-logo"}),e("span",{class:"logo"},"CloudPivot")],-1)])),_:1,__:[0]}),m(A,null,{default:s(()=>[e("div",$,[e("div",L,[t[1]||(t[1]=e("h2",{class:"category-title"},"公用应用",-1)),e("div",T,[(a(!0),r(_,null,f(h(C),o=>(a(),d(p,{key:o.id,class:"app-card","body-style":{padding:"0px"},shadow:"hover"},{default:s(()=>[e("div",{class:"card-content",onClick:k=>g(o.path)},[e("div",{class:"app-icon",style:y({background:o.color})},[o.icon?(a(),r("span",z,n(o.icon),1)):u("",!0)],4),e("div",D,[e("h3",null,n(o.name),1),e("p",null,n(o.desc),1)])],8,q)]),_:2},1024))),128))])]),e("div",O,[t[2]||(t[2]=e("h2",{class:"category-title"},"专用应用",-1)),e("div",G,[(a(!0),r(_,null,f(h(w),o=>(a(),d(p,{key:o.id,class:"app-card","body-style":{padding:"0px"},shadow:"hover"},{default:s(()=>[e("div",{class:"card-content",onClick:k=>g(o.path)},[e("div",{class:"app-icon",style:y({background:o.color})},[o.icon?(a(),r("span",J,n(o.icon),1)):u("",!0)],4),e("div",K,[e("h3",null,n(o.name),1),e("p",null,n(o.desc),1)])],8,H)]),_:2},1024))),128))])]),e("div",M,[t[3]||(t[3]=e("h2",{class:"category-title"},"系统应用",-1)),e("div",Q,[(a(!0),r(_,null,f(h(x),o=>(a(),d(p,{key:o.id,class:"app-card","body-style":{padding:"0px"},shadow:"hover"},{default:s(()=>[e("div",{class:"card-content",onClick:k=>g(o.path)},[e("div",{class:"app-icon",style:y({background:o.color})},[o.icon?(a(),r("span",U,n(o.icon),1)):u("",!0)],4),e("div",W,[e("h3",null,n(o.name),1),e("p",null,n(o.desc),1)])],8,R)]),_:2},1024))),128))])])])]),_:1}),m(B,{class:"portal-footer"},{default:s(()=>t[4]||(t[4]=[e("p",null,"Powered by ：科技通信中队",-1)])),_:1,__:[4]})]),_:1})}}},Y=F(X,[["__scopeId","data-v-be26e0c7"]]),v=P(Y);v.use(S);for(const[b,i]of Object.entries(V))v.component(b,i);v.mount("#app");
