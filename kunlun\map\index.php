<?php
// 获取GET参数中的经纬度，如果没有则使用默认值
$lat = isset($_GET['lat']) ? floatval($_GET['lat']) : 30.5371;
$lon = isset($_GET['lon']) ? floatval($_GET['lon']) : 106.44307;

// 验证经纬度范围
if ($lat < -90 || $lat > 90) {
    $lat = 30.5371;
}
if ($lon < -180 || $lon > 180) {
    $lon = 106.44307;
}

// 获取标签信息
$label = isset($_GET['label']) ? htmlspecialchars($_GET['label']) : '报警位置';
?>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报警位置地图 - <?php echo $label; ?></title>
    <script type="text/javascript" src="js/apiv1.3.min.js"></script>
    <script type="text/javascript" src="js/TextIconOverlay_min.js"></script>
    <script type="text/javascript" src="js/MarkerClusterer_min.js"></script>
    <link rel="stylesheet" type="text/css" href="bmap.css"/>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        #container {
            width: 100%;
            height: 100vh;
            border: none;
            position: relative;
        }
        #position {
            display: none;
        }
    </style>
</head>
<body>
    <div id="container"></div>
    <div id="position">
        <div class="info-title">📍 <?php echo $label; ?></div>
        <div class="coord-info">
            <span class="coord-label">经度:</span> 
            <span class="coord-value" id="lng-display"><?php echo $lon; ?></span>
        </div>
        <div class="coord-info">
            <span class="coord-label">纬度:</span> 
            <span class="coord-value" id="lat-display"><?php echo $lat; ?></span>
        </div>
        <div class="coord-info">
            <span class="coord-label">缩放级别:</span> 
            <span class="coord-value" id="zoom-display">17</span>
        </div>
    </div>

    <script type="text/javascript">
        // 从PHP传递的经纬度
        var lng = <?php echo $lon; ?>;
        var lat = <?php echo $lat; ?>;
        var label = "<?php echo $label; ?>";
        
        // 地图配置
        var mapOptions = {
            mapType: BMAP_NORMAL_MAP,
            enableHighResolution: true
        };
        
        // 创建地图实例
        var map = new BMap.Map("container", mapOptions);
        
        // 创建中心点
        var initPoint = new BMap.Point(lng, lat);
        
        // 初始化地图，设置中心点和缩放级别为16
        map.centerAndZoom(initPoint, 16);
        
        // 启用地图功能
        map.enableScrollWheelZoom();  // 启用滚轮缩放
        map.enableKeyboard();         // 启用键盘操作
        map.enableDragging();         // 启用拖拽
        map.enableDoubleClickZoom();  // 启用双击缩放
        
        // 添加地图控件
        map.addControl(new BMap.NavigationControl()); // 缩放平移控件
        map.addControl(new BMap.ScaleControl());      // 比例尺控件
        
        // 创建标注点
        function addAlarmMarker(lng, lat, label) {
            var point = new BMap.Point(lng, lat);
            
            // 创建自定义图标
            var alarmIcon = new BMap.Icon("images/marker_red_sprite.png", new BMap.Size(32, 32), {
                anchor: new BMap.Size(16, 32),  // 图标的定位点
                imageOffset: new BMap.Size(0, 0) // 图像偏移量
            });
            
            // 标注选项
            var markerOptions = {
                icon: alarmIcon,
                title: label
            };
            
            // 创建标注
            var marker = new BMap.Marker(point, markerOptions);
            
            // 添加动画效果
            marker.setAnimation(BMAP_ANIMATION_BOUNCE);
            
            // 添加到地图
            map.addOverlay(marker);
            
        }
        
        // 添加报警标注
        addAlarmMarker(lng, lat, label);
        

        
        // 添加右键菜单功能
        var contextMenu = new BMap.ContextMenu();
        contextMenu.addItem(new BMap.MenuItem('放大', function() {
            map.zoomIn();
        }));
        contextMenu.addItem(new BMap.MenuItem('缩小', function() {
            map.zoomOut();
        }));
        contextMenu.addItem(new BMap.MenuItem('回到报警点', function() {
            map.centerAndZoom(initPoint, 16);
        }));
        map.addContextMenu(contextMenu);
        
        // 响应式处理
        window.addEventListener('resize', function() {
            map.getViewport();
        });
    </script>
</body>
</html>
