<?php
// 简单的语法检查脚本
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "开始语法检查...\n";

// 检查PHP语法
$output = [];
$return_var = 0;
exec('php -l index.php 2>&1', $output, $return_var);

if ($return_var === 0) {
    echo "✅ PHP语法检查通过\n";
} else {
    echo "❌ PHP语法错误:\n";
    foreach ($output as $line) {
        echo $line . "\n";
    }
}

// 检查可能的问题
$content = file_get_contents('index.php');

// 检查括号匹配
$openParens = substr_count($content, '(');
$closeParens = substr_count($content, ')');
echo "括号统计: 开括号 $openParens, 闭括号 $closeParens\n";

if ($openParens !== $closeParens) {
    echo "❌ 括号不匹配!\n";
} else {
    echo "✅ 括号匹配\n";
}

// 检查大括号匹配
$openBraces = substr_count($content, '{');
$closeBraces = substr_count($content, '}');
echo "大括号统计: 开大括号 $openBraces, 闭大括号 $closeBraces\n";

if ($openBraces !== $closeBraces) {
    echo "❌ 大括号不匹配!\n";
} else {
    echo "✅ 大括号匹配\n";
}

// 检查方括号匹配
$openSquare = substr_count($content, '[');
$closeSquare = substr_count($content, ']');
echo "方括号统计: 开方括号 $openSquare, 闭方括号 $closeSquare\n";

if ($openSquare !== $closeSquare) {
    echo "❌ 方括号不匹配!\n";
} else {
    echo "✅ 方括号匹配\n";
}

echo "语法检查完成\n";
?>
