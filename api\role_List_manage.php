<?php
require_once '../conn_waf.php';

/**
 * 添加新角色
 * @param mysqli $conn 数据库连接对象
 * @param string $roleName 角色名称
 * @param string $roleDesc 角色描述
 * @return array 包含状态、消息和数据的数组
 * @throws Exception 当角色名称为空或数据库操作失败时抛出异常
 */
function addRole() {
    global $conn, $roleName, $roleDesc;
    if (empty($roleName)) {
        throw new Exception('角色名称不能为空');
    }
    $sql = "INSERT INTO 4_Role_List (roleName, roleDesc) VALUES (?, ?)";
    $params = [$roleName, $roleDesc];
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'ss', $params[0], $params[1]);
    $executeResult = mysqli_stmt_execute($stmt);
    
    if ($executeResult === false) {
        throw new Exception('插入失败: ' . mysqli_error($conn));
    }
    
    $affectedRows = mysqli_stmt_affected_rows($stmt);
    if ($affectedRows <= 0) {
        throw new Exception('插入失败: 没有影响任何行');
    }
    
    return ['status' => 1, 'message' => '插入成功', 'data' => []];
}

/**
 * 修改现有角色
 * @param mysqli $conn 数据库连接对象
 * @param int $id 角色ID
 * @param string $roleName 新角色名称
 * @param string $roleDesc 新角色描述
 * @return array 包含状态、消息和数据的数组
 * @throws Exception 当缺少ID参数或数据库操作失败时抛出异常
 */
function modifyRole() {
    global $conn, $id, $roleName, $roleDesc;
    if (empty($id)) {
        throw new Exception('缺少必要参数');
    }
    
    $sql = "UPDATE 4_Role_List SET roleName = ? , roleDesc = ? WHERE id = ?";
    $params = [$roleName, $roleDesc, $id];
    
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'sss', $params[0], $params[1], $params[2]);
    $executeResult = mysqli_stmt_execute($stmt);
    
    if ($executeResult === false) {
        throw new Exception('修改失败: ' . mysqli_error($conn));
    }
    
    $affectedRows = mysqli_stmt_affected_rows($stmt);
    if ($affectedRows <= 0) {
        throw new Exception('修改失败: 没有影响任何行');
    }
    
    return ['status' => 1, 'message' => '修改成功', 'data' => []];
}

/**
 * 删除角色
 * @param mysqli $conn 数据库连接对象
 * @param int $id 要删除的角色ID
 * @return array 包含状态、消息和数据的数组
 * @throws Exception 当缺少ID参数或数据库操作失败时抛出异常
 */
function deleteRole() {
    global $conn, $id;
    if (empty($id)) {
        throw new Exception('缺少必要参数');
    }
    
    $sql = "DELETE FROM 4_Role_List WHERE id = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'i', $id);
    $executeResult = mysqli_stmt_execute($stmt);
    
    if ($executeResult === false) {
        throw new Exception('删除失败: ' . mysqli_error($conn));
    }
    
    $affectedRows = mysqli_stmt_affected_rows($stmt);
    if ($affectedRows <= 0) {
        throw new Exception('删除失败: 没有影响任何行');
    }
    
    return ['status' => 1, 'message' => '删除成功', 'data' => []];
}

/**
 * 查询所有角色
 * @param mysqli $conn 数据库连接对象
 * @param int|null $page 页码(可选)
 * @param int|null $pagesize 每页数量(可选)
 * @param string|null $roleName 角色名称模糊搜索(可选)
 * @param string|null $roleDesc 角色描述模糊搜索(可选)
 * @return array 包含状态、消息和角色数据的数组
 */
function queryRoles() {
    global $conn;
    $page = !empty($_POST['page']) && $_POST['page'] !== "" ? max(1, intval($_POST['page'])) : 1;
    $pagesize = !empty($_POST['pagesize']) && $_POST['pagesize'] !== "" ? max(1, intval($_POST['pagesize'])) : 9999;
    $roleName = $_POST['roleName'] ?? null;
    $roleDesc = $_POST['roleDesc'] ?? null;

    $sql = "SELECT * FROM 4_Role_List";
    $where = [];
    $params = [];
    $types = '';

    if (!empty($roleName)) {
        $where[] = "roleName LIKE ?";
        $params[] = "%" . $roleName . "%";
        $types .= 's';
    }

    if (!empty($roleDesc)) {
        $where[] = "roleDesc LIKE ?";
        $params[] = "%" . $roleDesc . "%";
        $types .= 's';
    }

    if (!empty($where)) {
        $sql .= " WHERE " . implode(" AND ", $where);
    }

    // 获取总数用于分页
    $countSql = "SELECT COUNT(*) as total FROM 4_Role_List";
    if (!empty($where)) {
        $countSql .= " WHERE " . implode(" AND ", $where);
    }
    $countStmt = mysqli_prepare($conn, $countSql);
    if (!empty($params)) {
        mysqli_stmt_bind_param($countStmt, $types, ...$params);
    }
    mysqli_stmt_execute($countStmt);
    $totalResult = mysqli_stmt_get_result($countStmt);
    $totalRow = mysqli_fetch_assoc($totalResult);
    $total = $totalRow['total'];

    // 添加分页
    $sql .= " LIMIT ? OFFSET ?";
    $params[] = $pagesize;
    $params[] = ($page - 1) * $pagesize;
    $types .= 'ii';

    $stmt = mysqli_prepare($conn, $sql);
    if (!empty($params)) {
        mysqli_stmt_bind_param($stmt, $types, ...$params);
    }
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $data = [];

    if (mysqli_num_rows($result) > 0) {
        while ($row = mysqli_fetch_assoc($result)) {
            $data[] = $row;
        }
    }

    return [
        'status' => 1,
        'message' => '查询成功',
        'data' => $data,
        'total' => $total,
        'page' => $page,
        'pagesize' => $pagesize
    ];
}
function isHasPerm(){
    if (!isset($_SESSION['user_id'])) {
        echo json_encode([
            'status' => 0,
            'message' => '用户未登录',
            'data' => []
        ]);
        exit;
    }
    global $APP_ID;
    if (!isAdmin() && !isAppAdmin($APP_ID)) {
    echo json_encode([
        'status' => 0,
        'message' => '当前用户无权限操作',
        'data' => []
    ]);
    exit;
    }
}
try {
    // 获取参数
    $controlCode = $_POST['controlCode'] ?? '';
    $id = $_POST['id'] ?? '';
    $roleName = $_POST['roleName'] ?? '';
    $roleDesc = $_POST['roleDesc'] ?? '';

    switch ($controlCode) {
        case 'add': // 插入操作
            isHasPerm();
            $result = addRole();
            break;

        case 'modify': // 修改操作
            isHasPerm();
            $result = modifyRole();
            break;

        case 'del': // 删除操作
            isHasPerm();
            $result = deleteRole();
            break;

        case 'query': // 查询操作
            $result = queryRoles();
            break;

        default:
            throw new Exception('无效的控制码');
    }

    echo json_encode($result);
} catch (Exception $e) {
    $conn->rollback();
    echo json_encode(['status' => 0, 'message' => $e->getMessage(), 'data' => []]);
} finally {
    $conn->close();
}