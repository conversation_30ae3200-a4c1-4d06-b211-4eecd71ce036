<?php
/**
 * 重点警情处置情况表API接口
 * 支持增删改查操作
 */

// 引入数据库连接文件
require_once '../../conn_waf.php';
$APP_ID = 8; // 应用ID; 7表示重点警情事件应用
// 设置响应头为JSON
header('Content-Type: application/json; charset=utf-8');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'status' => 0,
        'message' => '仅支持POST请求',
        'data' => []
    ]);
    exit;
}

// 检查所有输入参数
waf_check_all_inputs();

// 获取控制参数
$controlCode = isset($_POST['controlCode']) ? $_POST['controlCode'] : '';

// 根据控制参数执行相应操作
switch ($controlCode) {
    case 'add':
        isHasPerm();
        addAlarmHandling();
        break;
    case 'del':
        isHasPerm();
        deleteAlarmHandling();
        break;
    case 'modify':
        isHasPerm();
        modifyAlarmHandling();
        break;
    case 'query':
        queryAlarmHandling();
        break;
    default:
        echo json_encode([
            'status' => 0,
            'message' => '无效的控制参数',
            'data' => []
        ]);
        break;
}
function isHasPerm(){
    if (!isset($_SESSION['user_id'])) {
        echo json_encode([
            'status' => 0,
            'message' => '用户未登录',
            'data' => []
        ]);
        exit;
    }
    global $APP_ID;
    if (!isAdmin() && !isAppAdmin($APP_ID)) {
    echo json_encode([
        'status' => 0,
        'message' => '当前用户无权限操作',
        'data' => []
    ]);
    exit;
    }
}
/**
 * 新增重点警情处置情况
 */
function addAlarmHandling() {
    global $conn;
    
    // 获取参数
    $alarmId = isset($_POST['alarm_id']) ? intval($_POST['alarm_id']) : 0;
    $handlingTime = isset($_POST['handling_time']) ? $_POST['handling_time'] : '';
    $handlingContent = isset($_POST['handling_content']) ? $_POST['handling_content'] : '';
    
    // 验证必填参数
    if ($alarmId <= 0 || empty($handlingTime) || empty($handlingContent)) {
        echo json_encode([
            'status' => 0,
            'message' => '缺少必要参数',
            'data' => []
        ]);
        return;
    }
    
    // 验证警情ID是否存在
    $checkAlarmSql = "SELECT id FROM 7_important_alarm_basic WHERE id = ?";
    $stmt = $conn->prepare($checkAlarmSql);
    $stmt->bind_param("i", $alarmId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        echo json_encode([
            'status' => 0,
            'message' => '指定的警情ID不存在',
            'data' => []
        ]);
        return;
    }
    
    // 插入数据
    $sql = "INSERT INTO 7_important_alarm_handling (alarm_id, handling_time, handling_content) VALUES (?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("iss", $alarmId, $handlingTime, $handlingContent);
    
    if ($stmt->execute()) {
        $insertId = $stmt->insert_id;
        echo json_encode([
            'status' => 1,
            'message' => '新增处置情况成功',
            'data' => ['id' => $insertId]
        ]);
        
        // 记录操作日志
        $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0;
        global $APP_ID;
        logOperation($conn, $userId, $APP_ID, "新增处置情况ID: $insertId, 警情ID: $alarmId");
        
        // 更新警情状态为处置中
        $updateSql = "UPDATE 7_important_alarm_basic SET alarm_status = '处置中' WHERE id = ?";
        $updateStmt = $conn->prepare($updateSql);
        $updateStmt->bind_param("i", $alarmId);
        $updateStmt->execute();
        $updateStmt->close();
    } else {
        echo json_encode([
            'status' => 0,
            'message' => '新增处置情况失败: ' . $stmt->error,
            'data' => []
        ]);
    }
    
    $stmt->close();
}

/**
 * 删除重点警情处置情况
 */
function deleteAlarmHandling() {
    global $conn;
    
    // 获取参数
    $id = isset($_POST['id']) ? $_POST['id'] : 0;
    
    // 处理批量删除（id为逗号分隔的字符串）
    if (is_string($id) && strpos($id, ',') !== false) {
        $ids = array_filter(array_map('intval', explode(',', $id)));
        if (empty($ids)) {
            echo json_encode([
                'status' => 0,
                'message' => '无效的处置情况ID',
                'data' => []
            ]);
            return;
        }
        
        // 获取关联的警情ID
        $getAlarmIdSql = "SELECT alarm_id FROM 7_important_alarm_handling WHERE id IN (" . implode(',', $ids) . ")";
        $result = $conn->query($getAlarmIdSql);
        
        if ($result->num_rows == 0) {
            echo json_encode([
                'status' => 0,
                'message' => '处置情况不存在',
                'data' => []
            ]);
            return;
        }
        
        // 删除处置情况
        $deleteSql = "DELETE FROM 7_important_alarm_handling WHERE id IN (" . implode(',', $ids) . ")";
        if ($conn->query($deleteSql)) {
            echo json_encode([
                'status' => 1,
                'message' => '批量删除处置情况成功',
                'data' => []
            ]);
            
            // 记录操作日志
            $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0;
            global $APP_ID;
            logOperation($conn, $userId, $APP_ID, "批量删除处置情况ID: " . implode(',', $ids));
        } else {
            echo json_encode([
                'status' => 0,
                'message' => '批量删除处置情况失败: ' . $conn->error,
                'data' => []
            ]);
        }
        return;
    }
    
    // 处理单个删除（id为整数）
    $id = intval($id);
    if ($id <= 0) {
        echo json_encode([
            'status' => 0,
            'message' => '无效的处置情况ID',
            'data' => []
        ]);
        return;
    }
    
    // 查询关联的警情ID，用于后续更新警情状态
    $getAlarmIdSql = "SELECT alarm_id FROM 7_important_alarm_handling WHERE id = ?";
    $stmt = $conn->prepare($getAlarmIdSql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        echo json_encode([
            'status' => 0,
            'message' => '处置情况不存在',
            'data' => []
        ]);
        $stmt->close();
        return;
    }
    
    $row = $result->fetch_assoc();
    $alarmId = $row['alarm_id'];
    $stmt->close();
    
    // 删除处置情况
    $deleteSql = "DELETE FROM 7_important_alarm_handling WHERE id = ?";
    $stmt = $conn->prepare($deleteSql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    
    if ($stmt->affected_rows > 0) {
        echo json_encode([
            'status' => 1,
            'message' => '删除处置情况成功',
            'data' => []
        ]);
        
        // 记录操作日志
        $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0;
        global $APP_ID;
        logOperation($conn, $userId, $APP_ID, "删除处置情况ID: $id, 警情ID: $alarmId");
        
        // 检查是否还有其他处置情况记录
        $checkSql = "SELECT COUNT(*) as count FROM 7_important_alarm_handling WHERE alarm_id = ?";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bind_param("i", $alarmId);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();
        $checkRow = $checkResult->fetch_assoc();
        
        // 如果没有其他处置情况记录，更新警情状态为处置完毕
        if ($checkRow['count'] == 0) {
            $updateSql = "UPDATE 7_important_alarm_basic SET alarm_status = '处置完毕' WHERE id = ?";
            $updateStmt = $conn->prepare($updateSql);
            $updateStmt->bind_param("i", $alarmId);
            $updateStmt->execute();
            $updateStmt->close();
        }
        
        $checkStmt->close();
    } else {
        echo json_encode([
            'status' => 0,
            'message' => '删除处置情况失败',
            'data' => []
        ]);
    }
    
    $stmt->close();
}

/**
 * 修改重点警情处置情况
 */
function modifyAlarmHandling() {
    global $conn;
    
    // 获取参数
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    $handlingTime = isset($_POST['handling_time']) ? $_POST['handling_time'] : null;
    $handlingContent = isset($_POST['handling_content']) ? $_POST['handling_content'] : null;
    
    // 验证参数
    if ($id <= 0) {
        echo json_encode([
            'status' => 0,
            'message' => '无效的处置情况ID',
            'data' => []
        ]);
        return;
    }
    
    // 检查处置情况是否存在
    $checkSql = "SELECT id FROM 7_important_alarm_handling WHERE id = ?";
    $stmt = $conn->prepare($checkSql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        echo json_encode([
            'status' => 0,
            'message' => '处置情况不存在',
            'data' => []
        ]);
        $stmt->close();
        return;
    }
    $stmt->close();
    
    
    // 构建更新SQL
    $updateFields = [];
    $params = [];
    $types = "";
    
    
    if ($handlingTime !== null) {
        $updateFields[] = "handling_time = ?";
        $params[] = $handlingTime;
        $types .= "s";
    }
    
    if ($handlingContent !== null) {
        $updateFields[] = "handling_content = ?";
        $params[] = $handlingContent;
        $types .= "s";
    }
    
    // 如果没有要更新的字段
    if (empty($updateFields)) {
        echo json_encode([
            'status' => 0,
            'message' => '没有提供要更新的字段',
            'data' => []
        ]);
        return;
    }
    
    // 添加ID参数
    $params[] = $id;
    $types .= "i";
    
    // 执行更新
    $sql = "UPDATE 7_important_alarm_handling SET " . implode(", ", $updateFields) . " WHERE id = ?";
    $stmt = $conn->prepare($sql);
    
    // 动态绑定参数
    $bindParams = array_merge([$stmt, $types], $params);
    $refParams = [];
    foreach($bindParams as $key => $value) {
        $refParams[$key] = &$bindParams[$key];
    }
    call_user_func_array('mysqli_stmt_bind_param', $refParams);
    
    if ($stmt->execute()) {
        echo json_encode([
            'status' => 1,
            'message' => '更新处置情况成功',
            'data' => []
        ]);
        
        // 记录操作日志
        $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0;
        global $APP_ID;
        $logMessage = "更新处置情况ID: $id - 更新内容: handling_time=$handlingTime, handling_content=$handlingContent";
        logOperation($conn, $userId, $APP_ID, $logMessage);
    } else {
        echo json_encode([
            'status' => 0,
            'message' => '更新处置情况失败: ' . $stmt->error,
            'data' => []
        ]);
    }
    
    $stmt->close();
}

/**
 * 查询重点警情处置情况
 */
function queryAlarmHandling() {
    global $conn;
    
    // 获取分页参数
    $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
    $pageSize = isset($_POST['pagesize']) ? intval($_POST['pagesize']) : 10;
    
    // 验证分页参数
    if ($page < 1) $page = 1;
    if ($pageSize < 1) $pageSize = 10;
    
    // 计算偏移量
    $offset = ($page - 1) * $pageSize;
    
    // 获取筛选参数
    $id = isset($_POST['id']) ? intval($_POST['id']) : null;
    $alarmId = isset($_POST['alarm_id']) ? intval($_POST['alarm_id']) : null;
    $startTime = isset($_POST['start_time']) ? $_POST['start_time'] : null;
    $endTime = isset($_POST['end_time']) ? $_POST['end_time'] : null;
    $alarmName = isset($_POST['alarm_name']) ? $_POST['alarm_name'] : null;
    $handlingContent = isset($_POST['handling_content']) ? $_POST['handling_content'] : null;
    
    // 构建查询条件
    $conditions = [];
    $params = [];
    $types = "";
    
    if ($id !== null && $id > 0) {
        $conditions[] = "h.id = ?";
        $params[] = $id;
        $types .= "i";
    }
    
    if ($alarmId !== null && $alarmId > 0) {
        $conditions[] = "h.alarm_id = ?";
        $params[] = $alarmId;
        $types .= "i";
    }
    
    if ($startTime !== null && $startTime !== '') {
        $conditions[] = "h.handling_time >= ?";
        $params[] = $startTime;
        $types .= "s";
    }
    
    if ($endTime !== null && $endTime !== '') {
        $conditions[] = "h.handling_time <= ?";
        $params[] = $endTime;
        $types .= "s";
    }
    
    if ($alarmName !== null && $alarmName !== '') {
        $conditions[] = "a.alarm_name LIKE ?";
        $params[] = "%$alarmName%";
        $types .= "s";
    }
    
    if ($handlingContent !== null && $handlingContent !== '') {
        $conditions[] = "h.handling_content LIKE ?";
        $params[] = "%$handlingContent%";
        $types .= "s";
    }
    
    // 构建WHERE子句
    $whereClause = "";
    if (!empty($conditions)) {
        $whereClause = "WHERE " . implode(" AND ", $conditions);
    }
    
    // 查询总记录数
    $countSql = "SELECT COUNT(*) as total FROM 7_important_alarm_handling h LEFT JOIN 7_important_alarm_basic a ON h.alarm_id = a.id $whereClause";
    $stmt = $conn->prepare($countSql);
    
    if ($stmt === false) {
        echo json_encode([
            'status' => 0,
            'message' => 'SQL预处理失败: ' . $conn->error
        ]);
        return;
    }
    
    if (!empty($params)) {
        $refParams = [];
        foreach($params as &$param) {
            $refParams[] = &$param;
        }
        array_unshift($refParams, $stmt, $types);
        if (!call_user_func_array('mysqli_stmt_bind_param', $refParams)) {
            echo json_encode([
                'status' => 0,
                'message' => '参数绑定失败: ' . $stmt->error
            ]);
            return;
        }
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $total = $row['total'];
    $stmt->close();
    
    // 查询数据
    $sql = "SELECT h.*, a.alarm_name 
            FROM 7_important_alarm_handling h 
            LEFT JOIN 7_important_alarm_basic a ON h.alarm_id = a.id 
            $whereClause 
            ORDER BY h.handling_time DESC 
            LIMIT ?, ?";
    
    $stmt = $conn->prepare($sql);
    
    // 添加分页参数
    $params[] = $offset;
    $params[] = $pageSize;
    $types .= "ii";
    
    $refParams = [];
    foreach($params as &$param) {
        $refParams[] = &$param;
    }
    array_unshift($refParams, $stmt, $types);
    call_user_func_array('mysqli_stmt_bind_param', $refParams);
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }
    
    echo json_encode([
        'status' => 1,
        'message' => '查询成功',
        'data' => $data,
        'total' => $total,
        'page' => $page,
        'pageSize' => $pageSize,
    ]);
    
    $stmt->close();
}