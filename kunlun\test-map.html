<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地图功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
        }
        label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            grid-column: 1 / -1;
        }
        button:hover {
            background: #0056b3;
        }
        .map-frame {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-top: 20px;
        }
        .preset-locations {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        .preset-btn {
            background: #28a745;
            padding: 8px 12px;
            font-size: 12px;
        }
        .preset-btn:hover {
            background: #218838;
        }
        .current-params {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🗺️ 地图功能测试</h1>
    
    <div class="test-panel">
        <h3>地图参数设置</h3>
        <div class="test-form">
            <div class="form-group">
                <label for="lat">纬度 (Latitude):</label>
                <input type="number" id="lat" value="30.5371" step="0.000001" min="-90" max="90">
            </div>
            <div class="form-group">
                <label for="lon">经度 (Longitude):</label>
                <input type="number" id="lon" value="106.44307" step="0.000001" min="-180" max="180">
            </div>
            <div class="form-group">
                <label for="label">标签:</label>
                <input type="text" id="label" value="测试报警点">
            </div>
            <button onclick="loadMap()">加载地图</button>
        </div>
        
        <div class="current-params" id="currentParams">
            当前参数: lat=30.5371, lon=106.44307, label=测试报警点
        </div>
    </div>

    <div class="test-panel">
        <h3>预设位置</h3>
        <div class="preset-locations">
            <button class="preset-btn" onclick="setLocation(39.9042, 116.4074, '北京天安门')">北京天安门</button>
            <button class="preset-btn" onclick="setLocation(31.2304, 121.4737, '上海外滩')">上海外滩</button>
            <button class="preset-btn" onclick="setLocation(22.3193, 114.1694, '香港中环')">香港中环</button>
            <button class="preset-btn" onclick="setLocation(30.5728, 104.0668, '成都春熙路')">成都春熙路</button>
            <button class="preset-btn" onclick="setLocation(26.0745, 119.2965, '福州三坊七巷')">福州三坊七巷</button>
            <button class="preset-btn" onclick="setLocation(36.0611, 103.8343, '兰州中山桥')">兰州中山桥</button>
        </div>
    </div>

    <div class="test-panel">
        <h3>地图显示</h3>
        <iframe id="mapFrame" class="map-frame" src="map/index.php?lat=30.5371&lon=106.44307&label=测试报警点"></iframe>
    </div>

    <div class="test-panel">
        <h3>测试说明</h3>
        <ul>
            <li>✅ 修改经纬度参数，点击"加载地图"查看不同位置</li>
            <li>✅ 使用预设位置快速测试不同城市</li>
            <li>✅ 地图会自动以17级缩放显示指定位置</li>
            <li>✅ 红色标记点会显示在指定的经纬度位置</li>
            <li>✅ 点击标记点会显示详细信息窗口</li>
            <li>✅ 支持地图缩放、拖拽等交互操作</li>
            <li>✅ 右键菜单提供快捷操作</li>
        </ul>
    </div>

    <script>
        function loadMap() {
            const lat = document.getElementById('lat').value;
            const lon = document.getElementById('lon').value;
            const label = document.getElementById('label').value;
            
            // 验证输入
            if (!lat || !lon) {
                alert('请输入有效的经纬度');
                return;
            }
            
            if (lat < -90 || lat > 90) {
                alert('纬度必须在-90到90之间');
                return;
            }
            
            if (lon < -180 || lon > 180) {
                alert('经度必须在-180到180之间');
                return;
            }
            
            // 构建URL
            const url = `map/index.php?lat=${lat}&lon=${lon}&label=${encodeURIComponent(label)}`;
            
            // 更新地图
            document.getElementById('mapFrame').src = url;
            
            // 更新参数显示
            document.getElementById('currentParams').textContent = 
                `当前参数: lat=${lat}, lon=${lon}, label=${label}`;
        }
        
        function setLocation(lat, lon, label) {
            document.getElementById('lat').value = lat;
            document.getElementById('lon').value = lon;
            document.getElementById('label').value = label;
            loadMap();
        }
        
        // 监听输入框变化
        document.getElementById('lat').addEventListener('input', updateParams);
        document.getElementById('lon').addEventListener('input', updateParams);
        document.getElementById('label').addEventListener('input', updateParams);
        
        function updateParams() {
            const lat = document.getElementById('lat').value;
            const lon = document.getElementById('lon').value;
            const label = document.getElementById('label').value;
            document.getElementById('currentParams').textContent = 
                `当前参数: lat=${lat}, lon=${lon}, label=${label}`;
        }
    </script>
</body>
</html>
