<?php
/**
 * 预警系统管理器
 * 创建时间: 2025-08-12
 * 功能: 系统启动、停止、状态检查等管理功能
 */

require_once '../conn_waf.php';

$APP_ID = 35; // 预警系统应用ID

// 检查用户权限
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.html');
    exit;
}

// 检查应用权限
function isHasPerm($APP_ID) {
    global $conn;
    
    if (!isset($_SESSION['user_id'])) {
        return false;
    }
    
    // 系统管理员拥有所有权限
    if (isAdmin()) {
        return true;
    }
    
    // 检查应用管理员权限
    if (isAppAdmin($APP_ID)) {
        return true;
    }
    
    return false;
}

if (!isHasPerm($APP_ID)) {
    $errorMessage = urlencode('您没有访问预警系统管理的权限。');
    header("Location: ../permission_error.html?type=permission&message=" . $errorMessage);
    exit;
}

$user_id = $_SESSION['user_id'];

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'checkStatus':
            checkSystemStatus();
            break;
        case 'startProcessor':
            startBackgroundProcessor();
            break;
        case 'stopProcessor':
            stopBackgroundProcessor();
            break;
        case 'restartProcessor':
            restartBackgroundProcessor();
            break;
        case 'checkDatabase':
            checkDatabaseStatus();
            break;
        case 'cleanupData':
            cleanupOldData();
            break;
        default:
            echo json_encode(['status' => 0, 'message' => '无效的操作']);
    }
    exit;
}

/**
 * 检查系统状态
 */
function checkSystemStatus() {
    global $conn;
    
    $status = [
        'database' => checkDatabaseConnection(),
        'processor' => checkProcessorStatus(),
        'sseServer' => checkSSEServer(),
        'smsService' => checkSmsService(),
        'diskSpace' => checkDiskSpace(),
        'lastAlarm' => getLastAlarmTime(),
        'systemHealth' => getSystemHealth()
    ];
    
    echo json_encode(['status' => 1, 'data' => $status]);
}

/**
 * 检查数据库连接
 */
function checkDatabaseConnection() {
    global $conn;
    
    try {
        $result = $conn->query("SELECT 1");
        if ($result) {
            return ['status' => 'healthy', 'message' => '数据库连接正常'];
        } else {
            return ['status' => 'error', 'message' => '数据库查询失败'];
        }
    } catch (Exception $e) {
        return ['status' => 'error', 'message' => '数据库连接失败: ' . $e->getMessage()];
    }
}

/**
 * 检查后台处理程序状态
 */
function checkProcessorStatus() {
    $logFile = __DIR__ . '/logs/background_processor.log';
    $pidFile = __DIR__ . '/logs/processor.pid';

    // 检查PID文件（Linux系统）
    if (file_exists($pidFile)) {
        $pid = trim(file_get_contents($pidFile));
        if ($pid && is_numeric($pid)) {
            // Linux系统检查进程
            $result = shell_exec("ps -p $pid 2>/dev/null");
            if ($result && strpos($result, $pid) !== false) {
                return ['status' => 'running', 'message' => "后台处理程序运行正常 (PID: $pid)"];
            }

            // PID文件存在但进程不存在，删除PID文件
            unlink($pidFile);
        }
    }

    // 检查日志文件
    if (!file_exists($logFile)) {
        return ['status' => 'stopped', 'message' => '后台处理程序未运行（无日志文件）'];
    }

    $lastModified = filemtime($logFile);
    $timeDiff = time() - $lastModified;

    // 检查日志文件大小和内容
    $fileSize = filesize($logFile);
    if ($fileSize == 0) {
        return ['status' => 'stopped', 'message' => '后台处理程序未运行（日志文件为空）'];
    }

    // 检查最后几行日志
    $lines = file($logFile);
    if (empty($lines)) {
        return ['status' => 'stopped', 'message' => '后台处理程序未运行（无日志内容）'];
    }

    $lastLines = array_slice($lines, -10);
    $hasError = false;
    $hasRecentActivity = false;

    foreach ($lastLines as $line) {
        // 检查错误
        if (strpos($line, '[ERROR]') !== false || strpos($line, '[FATAL]') !== false) {
            $hasError = true;
        }

        // 检查最近活动（最后一行的时间戳）
        if (preg_match('/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $line, $matches)) {
            $logTime = strtotime($matches[1]);
            if ($logTime && (time() - $logTime) < 300) { // 5分钟内有活动
                $hasRecentActivity = true;
            }
        }
    }

    if ($hasError) {
        return ['status' => 'error', 'message' => '后台处理程序运行异常（检测到错误日志）'];
    }

    if (!$hasRecentActivity && $timeDiff > 300) {
        return ['status' => 'stopped', 'message' => '后台处理程序可能已停止（超过5分钟无活动）'];
    }

    // 检查是否有启动成功的标志
    $lastLine = trim(end($lines));
    if (strpos($lastLine, '启动成功') !== false || strpos($lastLine, 'started') !== false) {
        return ['status' => 'running', 'message' => '后台处理程序运行正常'];
    }

    if ($hasRecentActivity) {
        return ['status' => 'running', 'message' => '后台处理程序运行正常（有最近活动）'];
    }

    return ['status' => 'unknown', 'message' => '后台处理程序状态未知'];
}

/**
 * 检查SSE服务器状态
 */
function checkSSEServer() {
    $sseUrl = 'http://localhost/kunlun/kafka.php';
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'user_agent' => 'System Health Check'
        ]
    ]);
    
    $headers = @get_headers($sseUrl, 1, $context);
    
    if ($headers && strpos($headers[0], '200') !== false) {
        return ['status' => 'healthy', 'message' => 'SSE服务器响应正常'];
    } else {
        return ['status' => 'error', 'message' => 'SSE服务器无响应或返回错误'];
    }
}

/**
 * 检查短信服务状态
 */
function checkSmsService() {
    try {
        require_once '../api/SmsSystem.php';
        
        // 这里可以添加短信服务的健康检查
        // 由于没有专门的健康检查接口，我们只检查文件是否存在
        if (function_exists('SMS_sendmessage')) {
            return ['status' => 'healthy', 'message' => '短信服务接口可用'];
        } else {
            return ['status' => 'error', 'message' => '短信服务接口不可用'];
        }
    } catch (Exception $e) {
        return ['status' => 'error', 'message' => '短信服务检查失败: ' . $e->getMessage()];
    }
}

/**
 * 检查磁盘空间
 */
function checkDiskSpace() {
    $freeBytes = disk_free_space('.');
    $totalBytes = disk_total_space('.');
    
    if ($freeBytes === false || $totalBytes === false) {
        return ['status' => 'error', 'message' => '无法获取磁盘空间信息'];
    }
    
    $freePercent = ($freeBytes / $totalBytes) * 100;
    
    if ($freePercent < 10) {
        return ['status' => 'warning', 'message' => sprintf('磁盘空间不足，剩余: %.1f%%', $freePercent)];
    } else {
        return ['status' => 'healthy', 'message' => sprintf('磁盘空间充足，剩余: %.1f%%', $freePercent)];
    }
}

/**
 * 获取最后预警时间
 */
function getLastAlarmTime() {
    global $conn;
    
    $sql = "SELECT captured_time FROM Kunlun_alarm_records ORDER BY captured_time DESC LIMIT 1";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return $row['captured_time'];
    }
    
    return null;
}

/**
 * 获取系统健康状态
 */
function getSystemHealth() {
    global $conn;
    
    $health = ['status' => 'healthy', 'issues' => []];
    
    // 检查最近的错误
    $sql = "SELECT COUNT(*) as failed_count 
            FROM Kunlun_sms_send_log 
            WHERE send_status = 'failed' 
            AND created_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)";
    $result = $conn->query($sql);
    $failedCount = $result->fetch_assoc()['failed_count'];
    
    if ($failedCount > 5) {
        $health['status'] = 'warning';
        $health['issues'][] = "最近1小时内有 {$failedCount} 条短信发送失败";
    }
    
    return $health;
}

/**
 * 启动后台处理程序
 */
function startBackgroundProcessor() {
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        if (!@mkdir($logDir, 0755, true)) {
            echo json_encode([
                'status' => 0,
                'message' => '无法创建日志目录，权限不足',
                'debug' => [
                    'log_dir' => $logDir,
                    'current_user' => get_current_user(),
                    'parent_dir_writable' => is_writable(__DIR__),
                    'parent_dir_perms' => substr(sprintf('%o', fileperms(__DIR__)), -4)
                ]
            ]);
            return;
        }
    }

    // 获取PHP路径，考虑open_basedir限制
    $phpPath = 'php'; // 使用系统PATH中的php命令

    // 检查是否可以直接使用php命令
    $testPhp = shell_exec('which php 2>/dev/null');
    if ($testPhp && trim($testPhp)) {
        $phpPath = trim($testPhp);
    } else {
        // 如果which命令不可用，尝试直接使用php
        $testOutput = shell_exec('php -v 2>/dev/null');
        if ($testOutput && strpos($testOutput, 'PHP') !== false) {
            $phpPath = 'php';
        } else {
            echo json_encode([
                'status' => 0,
                'message' => '无法找到PHP命令，请确保PHP CLI已安装并在PATH中',
                'debug' => [
                    'which_php' => $testPhp,
                    'php_test' => $testOutput,
                    'open_basedir' => ini_get('open_basedir')
                ]
            ]);
            return;
        }
    }
    $scriptPath = __DIR__ . '/background_processor.php';
    $logFile = $logDir . '/background_processor.log';

    // 检查脚本文件是否存在
    if (!file_exists($scriptPath)) {
        echo json_encode(['status' => 0, 'message' => '后台处理程序文件不存在: ' . $scriptPath]);
        return;
    }

    // Linux系统 - 使用nohup启动后台进程

    // 先测试脚本是否能正常执行
    $testCommand = "$phpPath $scriptPath --test 2>&1";
    $testOutput = shell_exec($testCommand);

    if (!$testOutput || strpos($testOutput, 'test mode started') === false) {
        echo json_encode([
            'status' => 0,
            'message' => '后台处理程序脚本测试失败',
            'debug' => [
                'test_command' => $testCommand,
                'test_output' => $testOutput,
                'php_path' => $phpPath,
                'script_path' => $scriptPath
            ]
        ]);
        return;
    }

    // 检查是否已经有进程在运行
    $pidFile = $logDir . '/processor.pid';
    if (file_exists($pidFile)) {
        $existingPid = trim(file_get_contents($pidFile));
        if ($existingPid && is_numeric($existingPid)) {
            $processCheck = shell_exec("ps -p $existingPid 2>/dev/null");
            if ($processCheck && strpos($processCheck, $existingPid) !== false) {
                echo json_encode([
                    'status' => 0,
                    'message' => "后台处理程序已在运行 (PID: $existingPid)"
                ]);
                return;
            } else {
                // PID文件存在但进程不存在，删除PID文件
                unlink($pidFile);
            }
        }
    }

    // 使用nohup启动后台进程，确保脱离终端
    $command = "nohup $phpPath $scriptPath > $logFile 2>&1 & echo \$!";

    $pid = shell_exec($command);
    $pid = trim($pid);

    if ($pid && is_numeric($pid)) {
        // 保存PID到文件
        file_put_contents($pidFile, $pid);

        // 等待一下让进程启动
        sleep(3);

        // 检查进程是否真的在运行
        $processCheck = shell_exec("ps -p $pid 2>/dev/null");
        if ($processCheck && strpos($processCheck, $pid) !== false) {
            // 检查日志文件是否有内容
            if (file_exists($logFile) && filesize($logFile) > 0) {
                $logContent = file_get_contents($logFile);
                if (strpos($logContent, '预警数据处理程序启动') !== false) {
                    echo json_encode([
                        'status' => 1,
                        'message' => "后台处理程序启动成功 (PID: $pid)",
                        'data' => [
                            'pid' => $pid,
                            'log_file' => $logFile,
                            'command' => $command
                        ]
                    ]);
                } else {
                    echo json_encode([
                        'status' => 0,
                        'message' => '后台处理程序启动失败，日志中未找到启动成功标志',
                        'debug' => [
                            'pid' => $pid,
                            'log_content' => substr($logContent, -500)
                        ]
                    ]);
                }
            } else {
                echo json_encode([
                    'status' => 0,
                    'message' => '后台处理程序启动失败，未生成日志文件',
                    'debug' => [
                        'pid' => $pid,
                        'log_file_exists' => file_exists($logFile),
                        'log_file_size' => file_exists($logFile) ? filesize($logFile) : 0
                    ]
                ]);
            }
        } else {
            echo json_encode([
                'status' => 0,
                'message' => '后台处理程序启动失败，进程未运行',
                'debug' => [
                    'pid' => $pid,
                    'process_check' => $processCheck,
                    'command' => $command
                ]
            ]);
        }
    } else {
        echo json_encode([
            'status' => 0,
            'message' => '后台处理程序启动失败，无法获取PID',
            'debug' => [
                'command' => $command,
                'output' => $pid
            ]
        ]);
    }

}

/**
 * 停止后台处理程序
 */
function stopBackgroundProcessor() {
    // 这里需要根据实际情况实现停止逻辑
    // 可以通过创建停止标志文件或发送信号等方式
    
    $stopFile = __DIR__ . '/logs/stop_processor';
    file_put_contents($stopFile, date('Y-m-d H:i:s'));
    
    echo json_encode(['status' => 1, 'message' => '停止信号已发送']);
}

/**
 * 重启后台处理程序
 */
function restartBackgroundProcessor() {
    stopBackgroundProcessor();
    sleep(2);
    startBackgroundProcessor();
}

/**
 * 检查数据库状态
 */
function checkDatabaseStatus() {
    global $conn;
    
    $tables = ['Kunlun_alarm_records', 'Kunlun_sms_alert_config', 'Kunlun_sms_send_log'];
    $status = [];
    
    foreach ($tables as $table) {
        $sql = "SELECT COUNT(*) as count FROM $table";
        $result = $conn->query($sql);
        
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            $status[$table] = ['exists' => true, 'count' => $count];
        } else {
            $status[$table] = ['exists' => false, 'error' => $conn->error];
        }
    }
    
    echo json_encode(['status' => 1, 'data' => $status]);
}

/**
 * 清理旧数据
 */
function cleanupOldData() {
    global $conn, $user_id;
    
    $days = intval($_POST['days'] ?? 30);
    
    try {
        $conn->autocommit(false);
        
        // 清理已处理的预警记录
        $sql1 = "DELETE FROM Kunlun_alarm_records 
                WHERE is_processed = 1 
                AND created_time < DATE_SUB(NOW(), INTERVAL ? DAY)";
        $stmt1 = $conn->prepare($sql1);
        $stmt1->bind_param("i", $days);
        $stmt1->execute();
        $deletedAlarms = $stmt1->affected_rows;
        
        // 清理短信发送记录
        $sql2 = "DELETE FROM Kunlun_sms_send_log 
                WHERE created_time < DATE_SUB(NOW(), INTERVAL ? DAY)";
        $stmt2 = $conn->prepare($sql2);
        $stmt2->bind_param("i", $days);
        $stmt2->execute();
        $deletedSms = $stmt2->affected_rows;
        
        $conn->commit();
        
        // 记录操作日志
        global $APP_ID;
        logOperation($conn, $user_id, $APP_ID, "清理旧数据，删除预警记录: $deletedAlarms 条，短信记录: $deletedSms 条");
        
        echo json_encode([
            'status' => 1,
            'message' => "数据清理完成，删除预警记录: $deletedAlarms 条，短信记录: $deletedSms 条",
            'data' => [
                'deletedAlarms' => $deletedAlarms,
                'deletedSms' => $deletedSms
            ]
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        echo json_encode(['status' => 0, 'message' => '数据清理失败: ' . $e->getMessage()]);
    } finally {
        $conn->autocommit(true);
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预警系统管理器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2rem;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .status-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status-card h3 {
            margin: 0 0 1rem 0;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        
        .status-healthy { background: #27ae60; }
        .status-warning { background: #f39c12; }
        .status-error { background: #e74c3c; }
        .status-stopped { background: #95a5a6; }
        .status-running { background: #27ae60; }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            margin: 0.25rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        
        .btn:hover { opacity: 0.8; }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; }
        
        .actions {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .actions h3 {
            margin: 0 0 1rem 0;
            color: #2c3e50;
        }
        
        .action-group {
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }
        
        .action-group:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .logs {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .log-content {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛠️ 预警系统管理器</h1>
            <p>系统状态监控与管理</p>
        </div>
        
        <div class="status-grid" id="statusGrid">
            <!-- 状态卡片将通过JavaScript动态生成 -->
        </div>
        
        <div class="actions">
            <h3>系统操作</h3>
            
            <div class="action-group">
                <h4>后台处理程序</h4>
                <button class="btn btn-success" onclick="startProcessor()">启动处理程序</button>
                <button class="btn btn-warning" onclick="restartProcessor()">重启处理程序</button>
                <button class="btn btn-danger" onclick="stopProcessor()">停止处理程序</button>
            </div>
            
            <div class="action-group">
                <h4>数据管理</h4>
                <button class="btn btn-primary" onclick="checkDatabase()">检查数据库</button>
                <button class="btn btn-warning" onclick="cleanupData()">清理旧数据</button>
            </div>
            
            <div class="action-group">
                <h4>系统管理</h4>
                <button class="btn btn-primary" onclick="window.open('sms_admin/')">短信管理后台</button>
                <button class="btn btn-primary" onclick="window.open('alarm-display.html')">实时监控页面</button>
                <button class="btn btn-primary" onclick="window.open('install_database.php')">数据库安装</button>
            </div>
        </div>
        
        <div class="logs">
            <h3>系统日志 <button class="btn btn-primary" onclick="refreshLogs()">刷新日志</button></h3>
            <div class="log-content" id="logContent">
                正在加载日志...
            </div>
        </div>
    </div>
    
    <button class="refresh-btn" onclick="refreshStatus()" title="刷新状态">🔄</button>
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
            refreshLogs();
            
            // 每30秒自动刷新状态
            setInterval(refreshStatus, 30000);
        });
        
        // 刷新系统状态
        async function refreshStatus() {
            try {
                const response = await fetch('system_manager.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=checkStatus'
                });
                
                const data = await response.json();
                
                if (data.status === 1) {
                    renderStatusCards(data.data);
                } else {
                    console.error('获取状态失败:', data.message);
                }
            } catch (error) {
                console.error('刷新状态失败:', error);
            }
        }
        
        // 渲染状态卡片
        function renderStatusCards(status) {
            const grid = document.getElementById('statusGrid');
            
            const cards = [
                {
                    title: '🗄️ 数据库',
                    status: status.database.status,
                    message: status.database.message
                },
                {
                    title: '⚙️ 后台处理程序',
                    status: status.processor.status,
                    message: status.processor.message
                },
                {
                    title: '📡 SSE服务器',
                    status: status.sseServer.status,
                    message: status.sseServer.message
                },
                {
                    title: '📱 短信服务',
                    status: status.smsService.status,
                    message: status.smsService.message
                },
                {
                    title: '💾 磁盘空间',
                    status: status.diskSpace.status,
                    message: status.diskSpace.message
                },
                {
                    title: '🚨 最后预警',
                    status: status.lastAlarm ? 'healthy' : 'warning',
                    message: status.lastAlarm ? '最后预警: ' + status.lastAlarm : '暂无预警记录'
                }
            ];
            
            grid.innerHTML = cards.map(card => `
                <div class="status-card">
                    <h3>
                        <span class="status-indicator status-${card.status}"></span>
                        ${card.title}
                    </h3>
                    <p>${card.message}</p>
                </div>
            `).join('');
        }
        
        // 启动处理程序
        async function startProcessor() {
            await executeAction('startProcessor', '启动后台处理程序');
        }
        
        // 停止处理程序
        async function stopProcessor() {
            await executeAction('stopProcessor', '停止后台处理程序');
        }
        
        // 重启处理程序
        async function restartProcessor() {
            await executeAction('restartProcessor', '重启后台处理程序');
        }
        
        // 检查数据库
        async function checkDatabase() {
            await executeAction('checkDatabase', '检查数据库状态');
        }
        
        // 清理旧数据
        async function cleanupData() {
            const days = prompt('请输入要保留的天数（默认30天）:', '30');
            if (days === null) return;
            
            await executeAction('cleanupData', '清理旧数据', { days: parseInt(days) || 30 });
        }
        
        // 执行操作
        async function executeAction(action, description, extraData = {}) {
            try {
                const formData = new FormData();
                formData.append('action', action);
                
                for (const [key, value] of Object.entries(extraData)) {
                    formData.append(key, value);
                }
                
                const response = await fetch('system_manager.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.status === 1) {
                    alert(`${description}成功: ${data.message}`);
                    refreshStatus();
                } else {
                    alert(`${description}失败: ${data.message}`);
                }
            } catch (error) {
                alert(`${description}失败: ${error.message}`);
            }
        }
        
        // 刷新日志
        function refreshLogs() {
            // 这里可以实现日志刷新逻辑
            document.getElementById('logContent').textContent = '日志功能开发中...';
        }
    </script>
</body>
</html>
