<?php
/**
 * 数据库连接和表结构测试脚本
 */

require_once '../../../conn_waf.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // 测试数据库连接
    if (!$conn) {
        throw new Exception('数据库连接失败');
    }
    
    // 检查表是否存在
    $sql = "SHOW TABLES LIKE 'Kunlun_sms_alert_config'";
    $result = $conn->query($sql);
    
    if ($result->num_rows === 0) {
        throw new Exception('表 Kunlun_sms_alert_config 不存在');
    }
    
    // 检查表结构
    $sql = "DESCRIBE Kunlun_sms_alert_config";
    $result = $conn->query($sql);
    
    $columns = [];
    while ($row = $result->fetch_assoc()) {
        $columns[] = $row;
    }
    
    // 检查必要的字段是否存在
    $requiredFields = ['alarm_type', 'album_name'];
    $existingFields = array_column($columns, 'Field');
    $missingFields = array_diff($requiredFields, $existingFields);
    
    if (!empty($missingFields)) {
        echo json_encode([
            'status' => 0,
            'message' => '缺少必要字段: ' . implode(', ', $missingFields),
            'existing_fields' => $existingFields,
            'table_structure' => $columns
        ]);
        exit;
    }
    
    // 测试简单查询
    $sql = "SELECT COUNT(*) as count FROM Kunlun_sms_alert_config";
    $result = $conn->query($sql);
    $count = $result->fetch_assoc()['count'];
    
    echo json_encode([
        'status' => 1,
        'message' => '数据库连接和表结构正常',
        'data' => [
            'table_exists' => true,
            'record_count' => $count,
            'table_structure' => $columns
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 0,
        'message' => '测试失败: ' . $e->getMessage(),
        'error_details' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
