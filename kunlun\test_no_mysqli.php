<?php
/**
 * 测试不依赖mysqli扩展的数据库连接
 */

header('Content-Type: application/json; charset=utf-8');

try {
    $results = [];
    
    // 1. 检查PDO扩展
    $results['pdo_check'] = [
        'available' => extension_loaded('pdo'),
        'mysql_driver' => extension_loaded('pdo_mysql'),
        'note' => 'PDO是PHP内置扩展，通常都可用'
    ];
    
    if (!extension_loaded('pdo')) {
        echo json_encode([
            'status' => 0,
            'message' => 'PDO扩展不可用，这是PHP的基础扩展',
            'data' => $results
        ]);
        exit;
    }
    
    if (!extension_loaded('pdo_mysql')) {
        echo json_encode([
            'status' => 0,
            'message' => 'PDO MySQL驱动不可用',
            'data' => $results
        ]);
        exit;
    }
    
    // 2. 测试数据库连接
    $results['database_test'] = [
        'connection_attempt' => false,
        'connection_success' => false,
        'error' => null
    ];
    
    try {
        $results['database_test']['connection_attempt'] = true;
        
        // 尝试包含新的数据库连接
        include_once 'db_connection.php';
        
        if (isset($conn)) {
            $results['database_test']['connection_success'] = true;
            $results['database_test']['connection_type'] = 'PDO with mysqli wrapper';
            $results['database_test']['note'] = '成功使用PDO连接数据库，不依赖mysqli扩展';
            
            // 测试简单查询
            $testQuery = $conn->query("SELECT 1 as test");
            if ($testQuery) {
                $results['database_test']['query_test'] = true;
                $results['database_test']['query_result'] = $testQuery->fetch_assoc();
            }
        } else {
            $results['database_test']['error'] = '数据库连接对象未创建';
        }
    } catch (Exception $e) {
        $results['database_test']['error'] = $e->getMessage();
    }
    
    // 3. 测试脚本执行
    $results['script_test'] = [
        'test_mode_available' => false,
        'error' => null
    ];
    
    try {
        $scriptPath = __DIR__ . '/background_processor.php';
        if (file_exists($scriptPath)) {
            $testCommand = "php $scriptPath --test 2>&1";
            $testOutput = shell_exec($testCommand);
            
            $results['script_test']['command'] = $testCommand;
            $results['script_test']['output'] = $testOutput;
            $results['script_test']['test_mode_available'] = strpos($testOutput, 'test mode started') !== false;
            $results['script_test']['has_fatal_error'] = strpos($testOutput, 'Fatal error') !== false;
            $results['script_test']['has_parse_error'] = strpos($testOutput, 'Parse error') !== false;
        } else {
            $results['script_test']['error'] = 'background_processor.php文件不存在';
        }
    } catch (Exception $e) {
        $results['script_test']['error'] = $e->getMessage();
    }
    
    // 4. 检查必需的PHP功能
    $results['php_features'] = [
        'json_encode' => function_exists('json_encode'),
        'json_decode' => function_exists('json_decode'),
        'curl_init' => function_exists('curl_init'),
        'shell_exec' => function_exists('shell_exec'),
        'file_get_contents' => function_exists('file_get_contents'),
        'stream_context_create' => function_exists('stream_context_create')
    ];
    
    // 5. 总体状态评估
    $overall_status = [
        'pdo_ready' => $results['pdo_check']['available'] && $results['pdo_check']['mysql_driver'],
        'database_ready' => $results['database_test']['connection_success'] ?? false,
        'script_ready' => $results['script_test']['test_mode_available'] ?? false,
        'no_fatal_errors' => !($results['script_test']['has_fatal_error'] ?? true),
        'all_features_available' => !in_array(false, $results['php_features'])
    ];
    
    $overall_status['ready'] = $overall_status['pdo_ready'] && 
                               $overall_status['database_ready'] && 
                               $overall_status['no_fatal_errors'] && 
                               $overall_status['all_features_available'];
    
    $results['overall_status'] = $overall_status;
    
    // 6. 建议和解决方案
    $recommendations = [];
    
    if (!$overall_status['pdo_ready']) {
        $recommendations[] = '需要安装PDO和PDO MySQL扩展';
    }
    
    if (!$overall_status['database_ready']) {
        $recommendations[] = '数据库连接失败，请检查数据库配置';
    }
    
    if (!$overall_status['script_ready']) {
        if ($results['script_test']['has_fatal_error'] || $results['script_test']['has_parse_error']) {
            $recommendations[] = '脚本有语法错误，需要修复';
        } else {
            $recommendations[] = '脚本测试模式不可用，请检查脚本';
        }
    }
    
    if ($overall_status['ready']) {
        $recommendations[] = '✓ 所有检查通过，可以启动后台处理程序';
    }
    
    $results['recommendations'] = $recommendations;
    
    echo json_encode([
        'status' => $overall_status['ready'] ? 1 : 0,
        'message' => $overall_status['ready'] ? 
            '环境检查通过，已使用PDO替代mysqli' : 
            '环境检查发现问题，请查看详细信息',
        'data' => $results
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 0,
        'message' => '检查失败: ' . $e->getMessage(),
        'error' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
