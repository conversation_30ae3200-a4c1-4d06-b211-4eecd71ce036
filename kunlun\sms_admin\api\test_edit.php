<?php
/**
 * 测试编辑配置功能
 */

require_once '../../../conn_waf.php';

header('Content-Type: application/json; charset=utf-8');

// 模拟POST数据
$_POST = [
    'action' => 'edit',
    'id' => 1,
    'alarm_type' => 'sfz',
    'alarm_person_sfz' => '511621198407220075',
    'alarm_person_name' => '测试人员',
    'recipients' => '["910"]',
    'valid_start_time' => '2025-08-12T00:00',
    'valid_end_time' => '2025-08-12T23:59',
    'is_active' => 0
];

// 模拟用户ID
$user_id = 1;

try {
    // 检查表结构
    $checkSql = "SHOW COLUMNS FROM Kunlun_sms_alert_config LIKE 'alarm_type'";
    $checkResult = $conn->query($checkSql);
    $hasNewFields = $checkResult->num_rows > 0;
    
    // 检查配置是否存在
    $configSql = "SELECT * FROM Kunlun_sms_alert_config WHERE id = 1 LIMIT 1";
    $configResult = $conn->query($configSql);
    $configExists = $configResult->num_rows > 0;
    
    echo json_encode([
        'status' => 1,
        'message' => '测试数据准备完成',
        'data' => [
            'has_new_fields' => $hasNewFields,
            'config_exists' => $configExists,
            'post_data' => $_POST,
            'user_id' => $user_id,
            'config_data' => $configExists ? $configResult->fetch_assoc() : null
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 0,
        'message' => '测试失败: ' . $e->getMessage(),
        'error' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
