<?php
/**
 * 短信预警管理后台主页面
 * 创建时间: 2025-08-12
 * 功能: 短信预警配置的增删改查管理
 */

require_once '../../conn_waf.php';

$APP_ID = 36; // 预警系统应用ID

// 检查用户权限
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.html');
    exit;
}

// 检查应用权限
function isHasPerm($APP_ID) {
    global $conn;
    
    if (!isset($_SESSION['user_id'])) {
        return false;
    }
    
    // 系统管理员拥有所有权限
    if (isAdmin()) {
        return true;
    }
    
    // 检查应用管理员权限
    if (isAppAdmin($APP_ID)) {
        return true;
    }
    
    return false;
}

if (!isHasPerm($APP_ID)) {
    $errorMessage = urlencode('您没有访问昆仑人脸预警系统管理后台的权限。');
    header("Location: ../../permission_error.html?type=permission&message=" . $errorMessage);
    exit;
}

$user_id = $_SESSION['user_id'];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>昆仑人脸预警管理系统</title>
    <link rel="stylesheet" href="css/admin.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .nav-tabs {
            background: white;
            border-bottom: 1px solid #e1e5e9;
            padding: 0 2rem;
        }
        
        .nav-tabs ul {
            list-style: none;
            display: flex;
            margin: 0;
            padding: 0;
        }
        
        .nav-tabs li {
            margin-right: 2rem;
        }
        
        .nav-tabs a {
            display: block;
            padding: 1rem 0;
            text-decoration: none;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .nav-tabs a:hover,
        .nav-tabs a.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-header h3 {
            color: #2c3e50;
            font-size: 1.2rem;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #2c3e50;
        }
        
        .form-control {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-body {
            padding: 1.5rem;
            max-height: 70vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid #dee2e6;
            background: #f8f9fa;
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
            position: sticky;
            bottom: 0;
        }
        
        .modal-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid #e1e5e9;
            text-align: right;
        }
        
        .close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #999;
        }
        
        .close:hover {
            color: #333;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .badge-success {
            background: #d4edda;
            color: #155724;
        }
        
        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }
        
        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .search-box {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            align-items: end;
        }
        
        .search-box .form-group {
            margin-bottom: 0;
            flex: 1;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 0.5rem;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* 接收人员选择器样式 */
        .recipient-selector {
            position: relative;
        }

        .recipient-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .dropdown-item {
            padding: 0.75rem;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.2s ease;
        }

        .dropdown-item:hover {
            background: #f8f9fa;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item.selected {
            background: #e3f2fd;
            color: #1976d2;
        }

        .dropdown-loading {
            padding: 1rem;
            text-align: center;
            color: #666;
        }

        .dropdown-no-results {
            padding: 1rem;
            text-align: center;
            color: #999;
            font-style: italic;
        }

        .selected-recipients {
            margin-top: 0.5rem;
            min-height: 40px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 0.5rem;
            background: #f8f9fa;
        }

        .no-selection {
            color: #999;
            font-style: italic;
            text-align: center;
            padding: 0.5rem;
        }

        .recipient-tag {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 0.25rem 0.5rem;
            margin: 0.25rem;
            border-radius: 12px;
            font-size: 0.85rem;
            position: relative;
        }

        .recipient-tag .remove {
            margin-left: 0.5rem;
            cursor: pointer;
            font-weight: bold;
        }

        .recipient-tag .remove:hover {
            color: #ffcccb;
        }

        .recipient-info {
            display: block;
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.25rem;
        }

        /* 日期时间选择器样式 */
        .datetime-input {
            position: relative;
        }

        .datetime-input::-webkit-datetime-edit-hour-field,
        .datetime-input::-webkit-datetime-edit-minute-field {
            background: transparent;
        }

        .datetime-input::-webkit-datetime-edit-hour-field:focus,
        .datetime-input::-webkit-datetime-edit-minute-field:focus {
            background: #e3f2fd;
            outline: none;
        }

        .form-text {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .nav-tabs {
                padding: 0 1rem;
            }

            .nav-tabs ul {
                flex-wrap: wrap;
            }

            .nav-tabs li {
                margin-right: 1rem;
                margin-bottom: 0.5rem;
            }

            .search-box {
                flex-direction: column;
            }

            .modal-content {
                width: 95%;
                margin: 2% auto;
                max-height: 95vh;
            }

            .recipient-dropdown {
                max-height: 150px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📱 短信预警管理系统</h1>
    </div>
    
    <div class="nav-tabs">
        <ul>
            <li><a href="#config" class="tab-link active" onclick="showTab('config')">预警配置</a></li>
            <li><a href="#logs" class="tab-link" onclick="showTab('logs')">发送记录</a></li>
            <li><a href="#stats" class="tab-link" onclick="showTab('stats')">统计分析</a></li>
            <li><a href="#settings" class="tab-link" onclick="showTab('settings')">系统设置</a></li>
        </ul>
    </div>
    
    <div class="container">
        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalConfigs">-</div>
                <div class="stat-label">预警配置数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="todaySms">-</div>
                <div class="stat-label">今日发送</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successRate">-</div>
                <div class="stat-label">成功率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeConfigs">-</div>
                <div class="stat-label">启用配置</div>
            </div>
        </div>
        
        <!-- 预警配置管理 -->
        <div id="config" class="tab-content active">
            <div class="card">
                <div class="card-header">
                    <h3>预警配置管理</h3>
                    <button class="btn btn-primary" onclick="showAddModal()">
                        ➕ 新增配置
                    </button>
                </div>
                <div class="card-body">
                    <div class="search-box">
                        <div class="form-group">
                            <label>预警人员身份证</label>
                            <input type="text" class="form-control" id="searchSfz" placeholder="输入身份证号搜索">
                        </div>
                        <div class="form-group">
                            <label>接收人员</label>
                            <input type="text" class="form-control" id="searchRecipient" placeholder="输入接收人姓名搜索">
                        </div>

                        <div class="form-group">
                            <button class="btn btn-primary" onclick="searchConfigs()">🔍 搜索</button>
                            <button class="btn btn-warning" onclick="resetSearch()">🔄 重置</button>
                        </div>
                    </div>
                    
                    <div id="configTable"></div>
                </div>
            </div>
        </div>
        
        <!-- 发送记录 -->
        <div id="logs" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h3>短信发送记录</h3>
                    <button class="btn btn-warning" onclick="retryFailedSms()">
                        🔄 重发失败短信
                    </button>
                </div>
                <div class="card-body">
                    <div class="search-box">
                        <div class="form-group">
                            <label>发送状态</label>
                            <select class="form-control" id="searchStatus">
                                <option value="">全部状态</option>
                                <option value="pending">待发送</option>
                                <option value="success">发送成功</option>
                                <option value="failed">发送失败</option>
                                <option value="expired">已过期</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>开始日期</label>
                            <input type="date" class="form-control" id="searchStartDate">
                        </div>
                        <div class="form-group">
                            <label>结束日期</label>
                            <input type="date" class="form-control" id="searchEndDate">
                        </div>
                        <div class="form-group">
                            <button class="btn btn-primary" onclick="searchLogs()">🔍 搜索</button>
                            <button class="btn btn-success" onclick="exportLogs()">📊 导出</button>
                        </div>
                    </div>
                    
                    <div id="logsTable"></div>
                </div>
            </div>
        </div>
        
        <!-- 统计分析 -->
        <div id="stats" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h3>统计分析</h3>
                </div>
                <div class="card-body">
                    <div id="statsContent">
                        <p>统计功能开发中...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 系统设置 -->
        <div id="settings" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h3>系统设置</h3>
                </div>
                <div class="card-body">
                    <div id="settingsContent">
                        <p>系统设置功能开发中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 新增/编辑配置模态框 -->
    <div id="configModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4 id="modalTitle">新增预警配置</h4>
                <button class="close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="configForm">
                    <input type="hidden" id="configId">

                    <div class="form-group">
                        <label>预警类型 *</label>
                        <select class="form-control" id="alarmType" required onchange="toggleAlarmFields()">
                            <option value="sfz">单身份证预警</option>
                            <option value="album">图库预警</option>
                        </select>
                        <small class="form-text text-muted">选择预警匹配方式</small>
                    </div>

                    <div class="form-group" id="sfzFields">
                        <label>预警人员身份证号 *</label>
                        <input type="text" class="form-control" id="alarmPersonSfz" maxlength="18" placeholder="请输入18位身份证号">
                        <small class="form-text text-muted">单身份证预警时必填</small>
                    </div>

                    <div class="form-group" id="albumFields" style="display: none;">
                        <label>图库名称 *</label>
                        <select class="form-control" id="albumName" required>
                            <option value="">请选择图库</option>
                        </select>
                        <small class="form-text text-muted">图库预警时必填，匹配该图库内的所有人员</small>
                    </div>

                    <div class="form-group">
                        <label>预警人员姓名</label>
                        <input type="text" class="form-control" id="alarmPersonName" maxlength="100" placeholder="可选，便于识别">
                    </div>
                    <div class="form-group">
                        <label>接收人员 * <small>(可选择多个)</small></label>
                        <div class="recipient-selector">
                            <input type="text" class="form-control" id="recipientSearch" placeholder="搜索姓名、单位或电话号码..." autocomplete="off">
                            <div class="recipient-dropdown" id="recipientDropdown" style="display: none;">
                                <div class="dropdown-loading">正在加载...</div>
                            </div>
                            <div class="selected-recipients" id="selectedRecipients">
                                <div class="no-selection">请搜索并选择接收人员</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>有效开始时间</label>
                        <input type="datetime-local" class="form-control datetime-input" id="validStartTime" step="60">
                        <small class="form-text text-muted">时间范围：00:00-23:59</small>
                    </div>
                    <div class="form-group">
                        <label>有效结束时间</label>
                        <input type="datetime-local" class="form-control datetime-input" id="validEndTime" step="60">
                        <small class="form-text text-muted">时间范围：00:00-23:59</small>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="isActive" checked> 启用此配置
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="saveConfig()">保存</button>
                <button class="btn" onclick="closeModal()">取消</button>
            </div>
        </div>
    </div>
    
    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 20;
        let users = [];
        let albums = [];
        let selectedRecipients = [];
        let searchTimeout = null;

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 移除所有标签链接的active类
            document.querySelectorAll('.tab-link').forEach(link => {
                link.classList.remove('active');
            });

            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            // 根据标签页加载相应数据
            switch(tabName) {
                case 'config':
                    loadConfigs();
                    break;
                case 'logs':
                    loadLogs();
                    break;
                case 'stats':
                    loadStats();
                    break;
                case 'settings':
                    loadSettings();
                    break;
            }
        }

        // 加载统计数据
        async function loadStats() {
            try {
                const response = await fetch('api/stats.php');
                const data = await response.json();

                if (data.status === 1) {
                    document.getElementById('totalConfigs').textContent = data.data.totalConfigs;
                    document.getElementById('todaySms').textContent = data.data.todaySms;
                    document.getElementById('successRate').textContent = data.data.successRate + '%';
                    document.getElementById('activeConfigs').textContent = data.data.activeConfigs;
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }

        // 加载配置列表
        async function loadConfigs() {
            try {
                const response = await fetch('api/config_manage.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=list&page=' + currentPage + '&pageSize=' + pageSize
                });

                const data = await response.json();

                if (data.status === 1) {
                    renderConfigTable(data.data.list);
                } else {
                    showAlert('danger', data.message);
                }
            } catch (error) {
                console.error('加载配置列表失败:', error);
                showAlert('danger', '加载配置列表失败');
            }
        }

        // 渲染配置表格
        function renderConfigTable(configs) {
            const tableHtml = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>预警类型</th>
                            <th>预警人员</th>
                            <th>身份证号/图库名称</th>
                            <th>接收人员</th>

                            <th>状态</th>
                            <th>有效期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${configs.map(config => `
                            <tr>
                                <td>${config.id}</td>
                                <td><span class="badge badge-${config.alarm_type === 'sfz' ? 'primary' : 'info'}">${config.alarm_type === 'sfz' ? '身份证' : '图库'}</span></td>
                                <td>${config.alarm_person_name || '-'}</td>
                                <td>${config.alarm_type === 'sfz' ? (config.alarm_person_sfz || '-') : (config.album_name || '-')}</td>
                                <td>${config.recipient_name} (${config.recipient_unit_name || '-'})</td>
                                <td><span class="badge badge-${config.is_active ? 'success' : 'danger'}">${config.is_active ? '启用' : '禁用'}</span></td>
                                <td>${formatValidTime(config.valid_start_time, config.valid_end_time)}</td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="editConfig(${config.id})">编辑</button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteConfig(${config.id})">删除</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            document.getElementById('configTable').innerHTML = tableHtml;
        }

        // 加载用户列表
        async function loadUsers() {
            try {
                const response = await fetch('api/config_manage.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=getUsers'
                });

                const data = await response.json();

                if (data.status === 1) {
                    users = data.data;
                    console.log('Loaded users:', users); // 调试日志
                    initRecipientSelector();
                } else {
                    console.error('Failed to load users:', data.message);
                }
            } catch (error) {
                console.error('加载用户列表失败:', error);
            }
        }

        // 加载图库列表
        async function loadAlbums() {
            try {
                const response = await fetch('api/config_manage.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=getAlbums'
                });

                const data = await response.json();

                if (data.status === 1) {
                    albums = data.data;
                    console.log('Loaded albums:', albums); // 调试日志
                    renderAlbumSelect();
                } else {
                    console.error('Failed to load albums:', data.message);
                }
            } catch (error) {
                console.error('加载图库列表失败:', error);
            }
        }

        // 渲染图库选择框
        function renderAlbumSelect() {
            const select = document.getElementById('albumName');
            select.innerHTML = '<option value="">请选择图库</option>';

            albums.forEach(album => {
                const option = document.createElement('option');
                option.value = album.name;
                option.textContent = `${album.name} (${album.count}条记录)`;
                select.appendChild(option);
            });
        }

        // 初始化接收人员选择器
        function initRecipientSelector() {
            const searchInput = document.getElementById('recipientSearch');
            const dropdown = document.getElementById('recipientDropdown');

            console.log('initRecipientSelector called'); // 调试日志

            if (!searchInput || !dropdown) {
                console.error('Required elements not found');
                return;
            }

            // 搜索输入事件
            searchInput.addEventListener('input', function() {
                console.log('Search input changed:', this.value); // 调试日志
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    searchRecipients(this.value);
                }, 300);
            });

            // 获得焦点时显示下拉框
            searchInput.addEventListener('focus', function() {
                console.log('Search input focused'); // 调试日志
                if (this.value.trim()) {
                    searchRecipients(this.value);
                } else {
                    showAllUsers();
                }
            });

            // 点击外部关闭下拉框
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.recipient-selector')) {
                    dropdown.style.display = 'none';
                }
            });

            console.log('Recipient selector initialized successfully'); // 调试日志
        }

        // 搜索接收人员
        function searchRecipients(query) {
            const dropdown = document.getElementById('recipientDropdown');

            console.log('searchRecipients called with query:', query); // 调试日志

            if (!query.trim()) {
                dropdown.style.display = 'none';
                return;
            }

            const filteredUsers = users.filter(user => {
                const searchText = query.toLowerCase();
                return user.name.toLowerCase().includes(searchText) ||
                       (user.unit_name && user.unit_name.toLowerCase().includes(searchText)) ||
                       (user.phone && user.phone.includes(searchText));
            });

            console.log('Filtered users:', filteredUsers); // 调试日志
            renderDropdown(filteredUsers);
        }

        // 显示所有用户
        function showAllUsers() {
            renderDropdown(users.slice(0, 20)); // 限制显示前20个
        }

        // 渲染下拉框
        function renderDropdown(userList) {
            const dropdown = document.getElementById('recipientDropdown');

            if (userList.length === 0) {
                dropdown.innerHTML = '<div class="dropdown-no-results">未找到匹配的用户</div>';
            } else {
                dropdown.innerHTML = userList.map(user => {
                    const isSelected = selectedRecipients.some(r => r.id == user.id);
                    return `
                        <div class="dropdown-item ${isSelected ? 'selected' : ''}" data-user-id="${user.id}">
                            <div><strong>${user.name}</strong></div>
                            <div class="recipient-info">${user.unit_name || '无单位'} | ${user.phone || '无电话'}</div>
                        </div>
                    `;
                }).join('');

                // 添加点击事件监听器
                dropdown.querySelectorAll('.dropdown-item').forEach(item => {
                    item.addEventListener('click', function() {
                        const userId = parseInt(this.getAttribute('data-user-id'));
                        if (userId) {
                            toggleRecipient(userId);
                        }
                    });
                });
            }

            dropdown.style.display = 'block';
        }

        // 切换接收人员选择状态
        function toggleRecipient(userId) {
            console.log('toggleRecipient called with userId:', userId); // 调试日志

            const user = users.find(u => u.id == userId);
            if (!user) {
                console.log('User not found for id:', userId);
                return;
            }

            console.log('Found user:', user); // 调试日志

            const existingIndex = selectedRecipients.findIndex(r => r.id == userId);

            if (existingIndex >= 0) {
                // 移除选择
                selectedRecipients.splice(existingIndex, 1);
                console.log('Removed user from selection');
            } else {
                // 添加选择
                selectedRecipients.push(user);
                console.log('Added user to selection');
            }

            renderSelectedRecipients();

            // 更新下拉框中的选中状态
            const searchInput = document.getElementById('recipientSearch');
            if (searchInput.value.trim()) {
                searchRecipients(searchInput.value);
            } else {
                showAllUsers();
            }
        }

        // 渲染已选择的接收人员
        function renderSelectedRecipients() {
            const container = document.getElementById('selectedRecipients');

            if (selectedRecipients.length === 0) {
                container.innerHTML = '<div class="no-selection">请搜索并选择接收人员</div>';
            } else {
                container.innerHTML = selectedRecipients.map(user => `
                    <span class="recipient-tag">
                        ${user.name} (${user.unit_name || '无单位'})
                        <span class="remove" data-user-id="${user.id}">&times;</span>
                    </span>
                `).join('');

                // 为移除按钮添加事件监听器
                container.querySelectorAll('.remove').forEach(removeBtn => {
                    removeBtn.addEventListener('click', function() {
                        const userId = parseInt(this.getAttribute('data-user-id'));
                        if (userId) {
                            removeRecipient(userId);
                        }
                    });
                });
            }
        }

        // 移除接收人员
        function removeRecipient(userId) {
            console.log('removeRecipient called with userId:', userId); // 调试日志

            const index = selectedRecipients.findIndex(r => r.id == userId);
            if (index >= 0) {
                selectedRecipients.splice(index, 1);
                renderSelectedRecipients();

                // 更新下拉框状态
                const searchInput = document.getElementById('recipientSearch');
                if (searchInput.value.trim()) {
                    searchRecipients(searchInput.value);
                } else if (document.getElementById('recipientDropdown').style.display === 'block') {
                    showAllUsers();
                }
            }
        }

        // 显示新增模态框
        function showAddModal() {
            document.getElementById('modalTitle').textContent = '新增预警配置';
            document.getElementById('configForm').reset();
            document.getElementById('configId').value = '';
            document.getElementById('isActive').checked = true;

            // 重置预警类型为默认值
            document.getElementById('alarmType').value = 'sfz';
            toggleAlarmFields();

            // 清空接收人员选择
            selectedRecipients = [];
            renderSelectedRecipients();
            document.getElementById('recipientSearch').value = '';
            document.getElementById('recipientDropdown').style.display = 'none';

            document.getElementById('configModal').style.display = 'block';
        }

        // 编辑配置
        async function editConfig(id) {
            try {
                const response = await fetch('api/config_manage.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=get&id=' + id
                });

                const data = await response.json();

                if (data.status === 1) {
                    const config = data.data;
                    document.getElementById('modalTitle').textContent = '编辑预警配置';
                    document.getElementById('configId').value = config.id;

                    // 设置预警类型
                    document.getElementById('alarmType').value = config.alarm_type || 'sfz';
                    toggleAlarmFields();

                    // 根据预警类型设置相应字段
                    if (config.alarm_type === 'album') {
                        document.getElementById('albumName').value = config.album_name || '';
                    } else {
                        document.getElementById('alarmPersonSfz').value = config.alarm_person_sfz || '';
                    }

                    document.getElementById('alarmPersonName').value = config.alarm_person_name || '';

                    // 设置选中的接收人员
                    selectedRecipients = [];
                    if (config.recipients && Array.isArray(config.recipients)) {
                        selectedRecipients = config.recipients;
                    } else if (config.recipient_user_id) {
                        // 兼容单个接收人员的情况
                        const user = users.find(u => u.id == config.recipient_user_id);
                        if (user) {
                            selectedRecipients = [user];
                        }
                    }
                    renderSelectedRecipients();

                    document.getElementById('validStartTime').value = config.valid_start_time ? config.valid_start_time.replace(' ', 'T') : '';
                    document.getElementById('validEndTime').value = config.valid_end_time ? config.valid_end_time.replace(' ', 'T') : '';
                    document.getElementById('isActive').checked = config.is_active == 1;
                    document.getElementById('configModal').style.display = 'block';
                } else {
                    showAlert('danger', data.message);
                }
            } catch (error) {
                console.error('获取配置详情失败:', error);
                showAlert('danger', '获取配置详情失败');
            }
        }

        // 保存配置
        async function saveConfig() {
            // 验证必填字段
            const alarmType = document.getElementById('alarmType').value;
            const alarmPersonSfz = document.getElementById('alarmPersonSfz').value.trim();
            const albumName = document.getElementById('albumName').value.trim();

            if (alarmType === 'sfz' && !alarmPersonSfz) {
                showAlert('danger', '请填写预警人员身份证号');
                return;
            }

            if (alarmType === 'album' && !albumName) {
                showAlert('danger', '请选择图库名称');
                return;
            }

            if (selectedRecipients.length === 0) {
                showAlert('danger', '请至少选择一个接收人员');
                return;
            }

            // 验证日期时间格式和范围
            const startTimeInput = document.getElementById('validStartTime');
            const endTimeInput = document.getElementById('validEndTime');

            if (!validateDateTime(startTimeInput) || !validateDateTime(endTimeInput)) {
                return;
            }

            if (!validateTimeRange()) {
                return;
            }

            const formData = new FormData();
            const configId = document.getElementById('configId').value;

            formData.append('action', configId ? 'edit' : 'add');
            if (configId) formData.append('id', configId);
            formData.append('alarm_type', alarmType);
            if (alarmType === 'sfz') {
                formData.append('alarm_person_sfz', alarmPersonSfz);
            } else {
                formData.append('album_name', albumName);
            }
            formData.append('alarm_person_name', document.getElementById('alarmPersonName').value);
            formData.append('recipients', JSON.stringify(selectedRecipients.map(r => r.id)));
            formData.append('valid_start_time', document.getElementById('validStartTime').value);
            formData.append('valid_end_time', document.getElementById('validEndTime').value);
            formData.append('is_active', document.getElementById('isActive').checked ? 1 : 0);

            try {
                const response = await fetch('api/config_manage.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.status === 1) {
                    showAlert('success', configId ? '配置更新成功' : '配置添加成功');
                    closeModal();
                    loadConfigs();
                    loadStats();
                } else {
                    showAlert('danger', data.message);
                }
            } catch (error) {
                console.error('保存配置失败:', error);
                showAlert('danger', '保存配置失败');
            }
        }

        // 删除配置
        async function deleteConfig(id) {
            if (!confirm('确定要删除这个配置吗？')) {
                return;
            }

            try {
                const response = await fetch('api/config_manage.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=remove&id=' + id
                });

                const data = await response.json();

                if (data.status === 1) {
                    showAlert('success', '配置删除成功');
                    loadConfigs();
                    loadStats();
                } else {
                    showAlert('danger', data.message);
                }
            } catch (error) {
                console.error('删除配置失败:', error);
                showAlert('danger', '删除配置失败');
            }
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('configModal').style.display = 'none';
        }

        // 显示提示信息
        function showAlert(type, message) {
            const alertHtml = `
                <div class="alert alert-${type}" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    ${message}
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', alertHtml);

            // 3秒后自动移除
            setTimeout(() => {
                const alerts = document.querySelectorAll('.alert');
                if (alerts.length > 0) {
                    alerts[alerts.length - 1].remove();
                }
            }, 3000);
        }

        // 切换预警类型字段显示
        function toggleAlarmFields() {
            const alarmType = document.getElementById('alarmType').value;
            const sfzFields = document.getElementById('sfzFields');
            const albumFields = document.getElementById('albumFields');
            const alarmPersonSfz = document.getElementById('alarmPersonSfz');
            const albumName = document.getElementById('albumName');

            if (alarmType === 'sfz') {
                sfzFields.style.display = 'block';
                albumFields.style.display = 'none';
                alarmPersonSfz.required = true;
                albumName.required = false;
                albumName.value = '';
            } else {
                sfzFields.style.display = 'none';
                albumFields.style.display = 'block';
                alarmPersonSfz.required = false;
                alarmPersonSfz.value = '';
                albumName.required = true;
                albumName.value = '';
            }
        }

        // 辅助函数

        function formatValidTime(startTime, endTime) {
            if (!startTime && !endTime) {
                return '永久有效';
            }

            const start = startTime ? new Date(startTime).toLocaleDateString() : '不限';
            const end = endTime ? new Date(endTime).toLocaleDateString() : '不限';

            return `${start} ~ ${end}`;
        }

        // 搜索配置
        function searchConfigs() {
            // 实现搜索逻辑
            loadConfigs();
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchSfz').value = '';
            document.getElementById('searchRecipient').value = '';
            loadConfigs();
        }

        // 加载发送记录
        function loadLogs() {
            // 实现加载发送记录逻辑
            console.log('加载发送记录');
        }

        // 加载设置
        function loadSettings() {
            // 实现加载设置逻辑
            console.log('加载设置');
        }

        // 重发失败短信
        function retryFailedSms() {
            // 实现重发失败短信逻辑
            console.log('重发失败短信');
        }

        // 搜索日志
        function searchLogs() {
            // 实现搜索日志逻辑
            console.log('搜索日志');
        }

        // 导出日志
        function exportLogs() {
            // 实现导出日志逻辑
            console.log('导出日志');
        }

        // 初始化日期时间选择器
        function initDateTimeInputs() {
            const dateTimeInputs = document.querySelectorAll('.datetime-input');

            dateTimeInputs.forEach(input => {
                // 添加输入验证
                input.addEventListener('input', function() {
                    validateDateTime(this);
                });

                // 添加失焦验证
                input.addEventListener('blur', function() {
                    validateDateTime(this);
                });
            });
        }

        // 验证日期时间格式
        function validateDateTime(input) {
            const value = input.value;
            if (!value) return true;

            try {
                const date = new Date(value);
                if (isNaN(date.getTime())) {
                    showDateTimeError(input, '请输入有效的日期时间');
                    return false;
                }

                const hours = date.getHours();
                const minutes = date.getMinutes();

                // 验证小时范围 (0-23)
                if (hours < 0 || hours > 23) {
                    showDateTimeError(input, '小时必须在00-23之间');
                    return false;
                }

                // 验证分钟范围 (0-59)
                if (minutes < 0 || minutes > 59) {
                    showDateTimeError(input, '分钟必须在00-59之间');
                    return false;
                }

                clearDateTimeError(input);
                return true;

            } catch (e) {
                showDateTimeError(input, '日期时间格式不正确');
                return false;
            }
        }

        // 显示日期时间错误
        function showDateTimeError(input, message) {
            clearDateTimeError(input);

            const errorDiv = document.createElement('div');
            errorDiv.className = 'datetime-error text-danger';
            errorDiv.style.fontSize = '0.8rem';
            errorDiv.style.marginTop = '0.25rem';
            errorDiv.textContent = message;

            input.parentNode.appendChild(errorDiv);
            input.style.borderColor = '#dc3545';
        }

        // 清除日期时间错误
        function clearDateTimeError(input) {
            const existingError = input.parentNode.querySelector('.datetime-error');
            if (existingError) {
                existingError.remove();
            }
            input.style.borderColor = '';
        }

        // 验证时间范围
        function validateTimeRange() {
            const startTime = document.getElementById('validStartTime').value;
            const endTime = document.getElementById('validEndTime').value;

            if (startTime && endTime) {
                const start = new Date(startTime);
                const end = new Date(endTime);

                if (start >= end) {
                    showAlert('danger', '结束时间必须晚于开始时间');
                    return false;
                }
            }

            return true;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadConfigs();
            loadUsers();
            loadAlbums();
            initDateTimeInputs();

            // 点击模态框外部关闭
            window.onclick = function(event) {
                const modal = document.getElementById('configModal');
                if (event.target === modal) {
                    closeModal();
                }
            }
        });
    </script>
</body>
</html>
