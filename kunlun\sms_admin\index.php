<?php
/**
 * 短信预警管理后台主页面
 * 创建时间: 2025-08-12
 * 功能: 短信预警配置的增删改查管理
 */

require_once '../../conn_waf.php';

$APP_ID = 35; // 预警系统应用ID

// 检查用户权限
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.html');
    exit;
}

// 检查应用权限
function isHasPerm($APP_ID) {
    global $conn;
    
    if (!isset($_SESSION['user_id'])) {
        return false;
    }
    
    // 系统管理员拥有所有权限
    if (isAdmin()) {
        return true;
    }
    
    // 检查应用管理员权限
    if (isAppAdmin($APP_ID)) {
        return true;
    }
    
    return false;
}

if (!isHasPerm($APP_ID)) {
    $errorMessage = urlencode('您没有访问预警系统管理后台的权限。');
    header("Location: ../../permission_error.html?type=permission&message=" . $errorMessage);
    exit;
}

$user_id = $_SESSION['user_id'];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短信预警管理系统</title>
    <link rel="stylesheet" href="css/admin.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .nav-tabs {
            background: white;
            border-bottom: 1px solid #e1e5e9;
            padding: 0 2rem;
        }
        
        .nav-tabs ul {
            list-style: none;
            display: flex;
            margin: 0;
            padding: 0;
        }
        
        .nav-tabs li {
            margin-right: 2rem;
        }
        
        .nav-tabs a {
            display: block;
            padding: 1rem 0;
            text-decoration: none;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .nav-tabs a:hover,
        .nav-tabs a.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-header h3 {
            color: #2c3e50;
            font-size: 1.2rem;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #2c3e50;
        }
        
        .form-control {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-body {
            padding: 1.5rem;
        }
        
        .modal-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid #e1e5e9;
            text-align: right;
        }
        
        .close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #999;
        }
        
        .close:hover {
            color: #333;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .badge-success {
            background: #d4edda;
            color: #155724;
        }
        
        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }
        
        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .search-box {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            align-items: end;
        }
        
        .search-box .form-group {
            margin-bottom: 0;
            flex: 1;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 0.5rem;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .nav-tabs {
                padding: 0 1rem;
            }
            
            .nav-tabs ul {
                flex-wrap: wrap;
            }
            
            .nav-tabs li {
                margin-right: 1rem;
                margin-bottom: 0.5rem;
            }
            
            .search-box {
                flex-direction: column;
            }
            
            .modal-content {
                width: 95%;
                margin: 10% auto;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📱 短信预警管理系统</h1>
    </div>
    
    <div class="nav-tabs">
        <ul>
            <li><a href="#config" class="tab-link active" onclick="showTab('config')">预警配置</a></li>
            <li><a href="#logs" class="tab-link" onclick="showTab('logs')">发送记录</a></li>
            <li><a href="#stats" class="tab-link" onclick="showTab('stats')">统计分析</a></li>
            <li><a href="#settings" class="tab-link" onclick="showTab('settings')">系统设置</a></li>
        </ul>
    </div>
    
    <div class="container">
        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalConfigs">-</div>
                <div class="stat-label">预警配置数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="todaySms">-</div>
                <div class="stat-label">今日发送</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successRate">-</div>
                <div class="stat-label">成功率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeConfigs">-</div>
                <div class="stat-label">启用配置</div>
            </div>
        </div>
        
        <!-- 预警配置管理 -->
        <div id="config" class="tab-content active">
            <div class="card">
                <div class="card-header">
                    <h3>预警配置管理</h3>
                    <button class="btn btn-primary" onclick="showAddModal()">
                        ➕ 新增配置
                    </button>
                </div>
                <div class="card-body">
                    <div class="search-box">
                        <div class="form-group">
                            <label>预警人员身份证</label>
                            <input type="text" class="form-control" id="searchSfz" placeholder="输入身份证号搜索">
                        </div>
                        <div class="form-group">
                            <label>接收人员</label>
                            <input type="text" class="form-control" id="searchRecipient" placeholder="输入接收人姓名搜索">
                        </div>
                        <div class="form-group">
                            <label>预警级别</label>
                            <select class="form-control" id="searchLevel">
                                <option value="">全部级别</option>
                                <option value="all">全部</option>
                                <option value="high">高危</option>
                                <option value="medium">中危</option>
                                <option value="low">低危</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-primary" onclick="searchConfigs()">🔍 搜索</button>
                            <button class="btn btn-warning" onclick="resetSearch()">🔄 重置</button>
                        </div>
                    </div>
                    
                    <div id="configTable"></div>
                </div>
            </div>
        </div>
        
        <!-- 发送记录 -->
        <div id="logs" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h3>短信发送记录</h3>
                    <button class="btn btn-warning" onclick="retryFailedSms()">
                        🔄 重发失败短信
                    </button>
                </div>
                <div class="card-body">
                    <div class="search-box">
                        <div class="form-group">
                            <label>发送状态</label>
                            <select class="form-control" id="searchStatus">
                                <option value="">全部状态</option>
                                <option value="pending">待发送</option>
                                <option value="success">发送成功</option>
                                <option value="failed">发送失败</option>
                                <option value="expired">已过期</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>开始日期</label>
                            <input type="date" class="form-control" id="searchStartDate">
                        </div>
                        <div class="form-group">
                            <label>结束日期</label>
                            <input type="date" class="form-control" id="searchEndDate">
                        </div>
                        <div class="form-group">
                            <button class="btn btn-primary" onclick="searchLogs()">🔍 搜索</button>
                            <button class="btn btn-success" onclick="exportLogs()">📊 导出</button>
                        </div>
                    </div>
                    
                    <div id="logsTable"></div>
                </div>
            </div>
        </div>
        
        <!-- 统计分析 -->
        <div id="stats" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h3>统计分析</h3>
                </div>
                <div class="card-body">
                    <div id="statsContent">
                        <p>统计功能开发中...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 系统设置 -->
        <div id="settings" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h3>系统设置</h3>
                </div>
                <div class="card-body">
                    <div id="settingsContent">
                        <p>系统设置功能开发中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 新增/编辑配置模态框 -->
    <div id="configModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4 id="modalTitle">新增预警配置</h4>
                <button class="close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="configForm">
                    <input type="hidden" id="configId">
                    <div class="form-group">
                        <label>预警人员身份证号 *</label>
                        <input type="text" class="form-control" id="alarmPersonSfz" required maxlength="18">
                    </div>
                    <div class="form-group">
                        <label>预警人员姓名</label>
                        <input type="text" class="form-control" id="alarmPersonName" maxlength="100">
                    </div>
                    <div class="form-group">
                        <label>接收人员 *</label>
                        <select class="form-control" id="recipientUserId" required>
                            <option value="">请选择接收人员</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>预警级别 *</label>
                        <select class="form-control" id="alertLevel" required>
                            <option value="all">全部级别</option>
                            <option value="high">仅高危</option>
                            <option value="medium">中危及以上</option>
                            <option value="low">低危及以上</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>有效开始时间</label>
                        <input type="datetime-local" class="form-control" id="validStartTime">
                    </div>
                    <div class="form-group">
                        <label>有效结束时间</label>
                        <input type="datetime-local" class="form-control" id="validEndTime">
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="isActive" checked> 启用此配置
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="saveConfig()">保存</button>
                <button class="btn" onclick="closeModal()">取消</button>
            </div>
        </div>
    </div>
    
    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 20;
        let users = [];

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 移除所有标签链接的active类
            document.querySelectorAll('.tab-link').forEach(link => {
                link.classList.remove('active');
            });

            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            // 根据标签页加载相应数据
            switch(tabName) {
                case 'config':
                    loadConfigs();
                    break;
                case 'logs':
                    loadLogs();
                    break;
                case 'stats':
                    loadStats();
                    break;
                case 'settings':
                    loadSettings();
                    break;
            }
        }

        // 加载统计数据
        async function loadStats() {
            try {
                const response = await fetch('api/stats.php');
                const data = await response.json();

                if (data.status === 1) {
                    document.getElementById('totalConfigs').textContent = data.data.totalConfigs;
                    document.getElementById('todaySms').textContent = data.data.todaySms;
                    document.getElementById('successRate').textContent = data.data.successRate + '%';
                    document.getElementById('activeConfigs').textContent = data.data.activeConfigs;
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }

        // 加载配置列表
        async function loadConfigs() {
            try {
                const response = await fetch('api/config_manage.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=list&page=' + currentPage + '&pageSize=' + pageSize
                });

                const data = await response.json();

                if (data.status === 1) {
                    renderConfigTable(data.data.list);
                } else {
                    showAlert('danger', data.message);
                }
            } catch (error) {
                console.error('加载配置列表失败:', error);
                showAlert('danger', '加载配置列表失败');
            }
        }

        // 渲染配置表格
        function renderConfigTable(configs) {
            const tableHtml = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>预警人员</th>
                            <th>身份证号</th>
                            <th>接收人员</th>
                            <th>预警级别</th>
                            <th>状态</th>
                            <th>有效期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${configs.map(config => `
                            <tr>
                                <td>${config.id}</td>
                                <td>${config.alarm_person_name || '-'}</td>
                                <td>${config.alarm_person_sfz}</td>
                                <td>${config.recipient_name} (${config.recipient_unit_name || '-'})</td>
                                <td><span class="badge badge-${getLevelColor(config.alert_level)}">${getLevelText(config.alert_level)}</span></td>
                                <td><span class="badge badge-${config.is_active ? 'success' : 'danger'}">${config.is_active ? '启用' : '禁用'}</span></td>
                                <td>${formatValidTime(config.valid_start_time, config.valid_end_time)}</td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="editConfig(${config.id})">编辑</button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteConfig(${config.id})">删除</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            document.getElementById('configTable').innerHTML = tableHtml;
        }

        // 加载用户列表
        async function loadUsers() {
            try {
                const response = await fetch('api/config_manage.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=getUsers'
                });

                const data = await response.json();

                if (data.status === 1) {
                    users = data.data;
                    renderUserSelect();
                }
            } catch (error) {
                console.error('加载用户列表失败:', error);
            }
        }

        // 渲染用户选择框
        function renderUserSelect() {
            const select = document.getElementById('recipientUserId');
            select.innerHTML = '<option value="">请选择接收人员</option>';

            users.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.name} - ${user.unit_name || '无单位'} (${user.phone || '无电话'})`;
                select.appendChild(option);
            });
        }

        // 显示新增模态框
        function showAddModal() {
            document.getElementById('modalTitle').textContent = '新增预警配置';
            document.getElementById('configForm').reset();
            document.getElementById('configId').value = '';
            document.getElementById('isActive').checked = true;
            document.getElementById('configModal').style.display = 'block';
        }

        // 编辑配置
        async function editConfig(id) {
            try {
                const response = await fetch('api/config_manage.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=get&id=' + id
                });

                const data = await response.json();

                if (data.status === 1) {
                    const config = data.data;
                    document.getElementById('modalTitle').textContent = '编辑预警配置';
                    document.getElementById('configId').value = config.id;
                    document.getElementById('alarmPersonSfz').value = config.alarm_person_sfz;
                    document.getElementById('alarmPersonName').value = config.alarm_person_name || '';
                    document.getElementById('recipientUserId').value = config.recipient_user_id;
                    document.getElementById('alertLevel').value = config.alert_level;
                    document.getElementById('validStartTime').value = config.valid_start_time ? config.valid_start_time.replace(' ', 'T') : '';
                    document.getElementById('validEndTime').value = config.valid_end_time ? config.valid_end_time.replace(' ', 'T') : '';
                    document.getElementById('isActive').checked = config.is_active == 1;
                    document.getElementById('configModal').style.display = 'block';
                } else {
                    showAlert('danger', data.message);
                }
            } catch (error) {
                console.error('获取配置详情失败:', error);
                showAlert('danger', '获取配置详情失败');
            }
        }

        // 保存配置
        async function saveConfig() {
            const formData = new FormData();
            const configId = document.getElementById('configId').value;

            formData.append('action', configId ? 'update' : 'add');
            if (configId) formData.append('id', configId);
            formData.append('alarm_person_sfz', document.getElementById('alarmPersonSfz').value);
            formData.append('alarm_person_name', document.getElementById('alarmPersonName').value);
            formData.append('recipient_user_id', document.getElementById('recipientUserId').value);
            formData.append('alert_level', document.getElementById('alertLevel').value);
            formData.append('valid_start_time', document.getElementById('validStartTime').value);
            formData.append('valid_end_time', document.getElementById('validEndTime').value);
            formData.append('is_active', document.getElementById('isActive').checked ? 1 : 0);

            try {
                const response = await fetch('api/config_manage.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.status === 1) {
                    showAlert('success', configId ? '配置更新成功' : '配置添加成功');
                    closeModal();
                    loadConfigs();
                    loadStats();
                } else {
                    showAlert('danger', data.message);
                }
            } catch (error) {
                console.error('保存配置失败:', error);
                showAlert('danger', '保存配置失败');
            }
        }

        // 删除配置
        async function deleteConfig(id) {
            if (!confirm('确定要删除这个配置吗？')) {
                return;
            }

            try {
                const response = await fetch('api/config_manage.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=delete&id=' + id
                });

                const data = await response.json();

                if (data.status === 1) {
                    showAlert('success', '配置删除成功');
                    loadConfigs();
                    loadStats();
                } else {
                    showAlert('danger', data.message);
                }
            } catch (error) {
                console.error('删除配置失败:', error);
                showAlert('danger', '删除配置失败');
            }
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('configModal').style.display = 'none';
        }

        // 显示提示信息
        function showAlert(type, message) {
            const alertHtml = `
                <div class="alert alert-${type}" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    ${message}
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', alertHtml);

            // 3秒后自动移除
            setTimeout(() => {
                const alerts = document.querySelectorAll('.alert');
                if (alerts.length > 0) {
                    alerts[alerts.length - 1].remove();
                }
            }, 3000);
        }

        // 辅助函数
        function getLevelColor(level) {
            const colors = {
                'all': 'primary',
                'high': 'danger',
                'medium': 'warning',
                'low': 'success'
            };
            return colors[level] || 'secondary';
        }

        function getLevelText(level) {
            const texts = {
                'all': '全部级别',
                'high': '仅高危',
                'medium': '中危及以上',
                'low': '低危及以上'
            };
            return texts[level] || level;
        }

        function formatValidTime(startTime, endTime) {
            if (!startTime && !endTime) {
                return '永久有效';
            }

            const start = startTime ? new Date(startTime).toLocaleDateString() : '不限';
            const end = endTime ? new Date(endTime).toLocaleDateString() : '不限';

            return `${start} ~ ${end}`;
        }

        // 搜索配置
        function searchConfigs() {
            // 实现搜索逻辑
            loadConfigs();
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchSfz').value = '';
            document.getElementById('searchRecipient').value = '';
            document.getElementById('searchLevel').value = '';
            loadConfigs();
        }

        // 加载发送记录
        function loadLogs() {
            // 实现加载发送记录逻辑
            console.log('加载发送记录');
        }

        // 加载设置
        function loadSettings() {
            // 实现加载设置逻辑
            console.log('加载设置');
        }

        // 重发失败短信
        function retryFailedSms() {
            // 实现重发失败短信逻辑
            console.log('重发失败短信');
        }

        // 搜索日志
        function searchLogs() {
            // 实现搜索日志逻辑
            console.log('搜索日志');
        }

        // 导出日志
        function exportLogs() {
            // 实现导出日志逻辑
            console.log('导出日志');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadConfigs();
            loadUsers();

            // 点击模态框外部关闭
            window.onclick = function(event) {
                const modal = document.getElementById('configModal');
                if (event.target === modal) {
                    closeModal();
                }
            }
        });
    </script>
</body>
</html>
