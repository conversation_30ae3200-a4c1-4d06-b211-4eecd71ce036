-- 移除预警级别字段的数据库更新脚本
-- 更新时间: 2025-08-12
-- 说明: 从Kunlun_sms_alert_config表中移除alert_level字段

-- 检查表是否存在alert_level字段
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'Kunlun_sms_alert_config' 
AND COLUMN_NAME = 'alert_level';

-- 如果字段存在，则删除它
-- 注意：在生产环境中执行前请先备份数据
ALTER TABLE `Kunlun_sms_alert_config` DROP COLUMN IF EXISTS `alert_level`;

-- 更新表注释
ALTER TABLE `Kunlun_sms_alert_config` 
COMMENT = '短信预警配置表（已移除预警级别功能）';

-- 验证表结构
DESCRIBE `Kunlun_sms_alert_config`;

-- 显示表的存储引擎和字符集信息
SHOW TABLE STATUS LIKE 'Kunlun_sms_alert_config';

-- 检查相关索引
SHOW INDEX FROM `Kunlun_sms_alert_config`;
