
<?php
// 显示错误信息
ini_set('display_errors', 1);
error_reporting(E_ALL);


use Rdkafka\Conf;
use Rdkafka\Consumer\Consumer;

try {
    // 创建配置对象
    $config = new Conf();

    // 设置配置参数
    $config->set('bootstrap.servers', '80.164.12.151:9192');
    $config->set('group.id', 'test_group');
    $config->set('enable.partition.Consumer', true);
    $config->set('enable.auto.Commit', false);
    $config->set('session.timeout.ms', '30000');
    $config->set('max.partition.fetchs', '1');
    $config->set('fetch.wait.max.ms', '100');
    $config->set('retry.backoff.ms', '100');

    // 创建消费者实例
    $consumer = new Consumer($config);

    // 订阅主题
    $consumer->subscribe(['face_alarm_000005']);

    echo '已订阅主题：face_alarm_000005' . PHP_EOL;

    while (true) {
        $message = $consumer->consume();

        if ($message === null) {
            continue;
        }

        if ($message->err) {
            echo '消费错误：' . $message->errstr . PHP_EOL;
            $consumer->close();
            exit(1);
        }

        echo '接收到消息：' . $message->payload . PHP_EOL;
        echo '主题：' . $message->topic . PHP_EOL;
        echo '分区：' . $message->partition . PHP_EOL;
        echo '偏移量：' . $message->offset . PHP_EOL;
        echo PHP_EOL;
    }
} catch (\Exception $e) {
    echo 'Exception caught: ' . $e->getMessage() . PHP_EOL;
    exit(1);
}
?>