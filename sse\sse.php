<?php
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('X-Accel-Buffering: no');
header('Access-Control-Allow-Origin: http://localhost:5173'); 
// 允许携带凭证
header('Access-Control-Allow-Credentials: true'); 
// 允许的请求方法
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS'); 
// 允许的请求头
header('Access-Control-Allow-Headers: Content-Type, Authorization');
ini_set('output_buffering', 'off');
ini_set('zlib.output_compression', false);

function get_sched() {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://**************/DutySched/api/sched_manage.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, ['controlCode' => 'query1']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    curl_close($ch);
    $responseData = json_decode($response);
    return $responseData->data;
}
function get_alarm(){
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://**************/importantAlarm/api/alarm_basic.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, [
        'controlCode' => 'query',
        'is_display' => '1'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    curl_close($ch);
    $responseData = json_decode($response);
    
    if(!empty($responseData->data)) {
        foreach($responseData->data as &$alarm) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://**************/importantAlarm/api/alarm_handling.php');
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, [
                'controlCode' => 'query',
                'alarm_id' => $alarm->id
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $handlingResponse = curl_exec($ch);
            curl_close($ch);
            $handlingData = json_decode($handlingResponse);
            
            $alarm->alarm_handling = !empty($handlingData->data) ? $handlingData->data[0] : null;
        }
    }
    
    return $responseData->data;
}
function get_keyperson(){
    // 获取重点人员基础信息
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://**************/keyPersActivity/api/key_person.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, [
        'controlCode' => 'query',
        'is_display' => '1'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $responseData = json_decode($response);
    curl_close($ch);

    if(!empty($responseData->data)) {
        foreach($responseData->data as $key => &$person) {
            // 获取每个重点人员的最新动态
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://**************/keyPersActivity/api/person_movement.php');
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, [
                'controlCode' => 'query',
                'search_idnumber' => $person->id_number
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $movementResponse = curl_exec($ch);
            curl_close($ch);
            $movementData = json_decode($movementResponse);
            
            // 合并动态数据
            $person->person_movement = !empty($movementData->data) ? $movementData->data[0] : null;
            
            // 过滤status_name为"可控"的重点人员
            if (!empty($person->person_movement) && $person->person_movement->status_name === '可控') {
                unset($responseData->data[$key]);
            }
        }
        // 重置数组索引
        $responseData->data = array_values($responseData->data);
    }
    
    return $responseData->data;
}

/**
 * 获取人员动态状态统计数据
 * @return array|object 包含状态统计数据的数组或对象，如果发生错误则返回空数组
 */
function getStatusCount() {
    // 发送HTTP请求到API端点
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://**************/keyPersActivity/api/person_movement.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, ['controlCode' => 'getStatusCount']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    // 执行请求并获取响应
    $response = curl_exec($ch);
    
    // 检查请求是否成功
    if(curl_errno($ch)) {
        // 记录错误信息
        error_log('getStatusCount API request failed: ' . curl_error($ch));
        curl_close($ch);
        return [];
    }
    
    curl_close($ch);
    
    // 解析JSON响应
    $responseData = json_decode($response);
    
    // 检查JSON解析是否成功以及数据是否存在
    if(json_last_error() !== JSON_ERROR_NONE || !isset($responseData->data)) {
        error_log('getStatusCount API response invalid or no data found');
        return [];
    }
    
    // 返回data字段内容
    return $responseData->data;
}

while(true) {
    try {
        $sched = get_sched();
        $alarm = get_alarm();
        $keyperson = get_keyperson();
        $statusCount = getStatusCount();
        $data = json_encode([
            'sched' => $sched,
            'alarm' => $alarm,
            'keyperson' => $keyperson,
            'statusCount' => $statusCount
        ]);
        echo "data: " . $data . "\n\n";
        ob_flush();
        flush();
        sleep(30); // 30秒间隔
    } catch (Exception $e) {
        break;
    }
}
?>