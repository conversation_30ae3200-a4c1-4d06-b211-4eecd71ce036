<?php
/**
 * 预警系统数据库安装脚本
 * 创建时间: 2025-08-12
 * 功能: 自动创建预警系统所需的数据库表结构
 */

require_once '../conn_waf.php';

// 设置响应头
header('Content-Type: text/html; charset=utf-8');

// 检查用户权限（需要管理员权限）
if (!isset($_SESSION['user_id'])) {
    die('<h1>错误</h1><p>请先登录系统</p>');
}

// 检查是否为系统管理员
if (!isAdmin()) {
    die('<h1>权限不足</h1><p>只有系统管理员才能执行数据库安装</p>');
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预警系统数据库安装</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
        }
        .success {
            border-left-color: #27ae60;
            background: #d4edda;
            color: #155724;
        }
        .error {
            border-left-color: #e74c3c;
            background: #f8d7da;
            color: #721c24;
        }
        .warning {
            border-left-color: #f39c12;
            background: #fff3cd;
            color: #856404;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #2980b9;
        }
        button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        .sql-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .table-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .table-card {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            background: #f9f9f9;
        }
        .table-card h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: #3498db;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ 预警系统数据库安装</h1>
        
        <div class="step">
            <h3>安装说明</h3>
            <p>此脚本将创建预警系统所需的数据库表结构，包括：</p>
            <div class="table-info">
                <div class="table-card">
                    <h4>📊 Kunlun_alarm_records</h4>
                    <p>预警人员记录表，存储所有预警信息</p>
                </div>
                <div class="table-card">
                    <h4>📱 Kunlun_sms_alert_config</h4>
                    <p>短信预警配置表，管理通知规则</p>
                </div>
                <div class="table-card">
                    <h4>📋 Kunlun_sms_send_log</h4>
                    <p>短信发送记录表，记录发送历史</p>
                </div>
            </div>
        </div>

        <div id="installProgress" style="display: none;">
            <h3>安装进度</h3>
            <div class="progress">
                <div id="progressBar" class="progress-bar" style="width: 0%"></div>
            </div>
            <div id="progressText">准备安装...</div>
        </div>

        <div id="installResult"></div>

        <div class="step">
            <button onclick="checkTables()">检查现有表</button>
            <button onclick="installTables()" id="installBtn">开始安装</button>
            <button onclick="showSQL()">查看SQL脚本</button>
        </div>

        <div id="sqlDisplay" style="display: none;">
            <h3>SQL脚本内容</h3>
            <div class="sql-output" id="sqlContent"></div>
        </div>
    </div>

    <script>
        function updateProgress(percent, text) {
            document.getElementById('progressBar').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }

        function showResult(type, title, message) {
            const resultDiv = document.getElementById('installResult');
            resultDiv.innerHTML = `
                <div class="step ${type}">
                    <h3>${title}</h3>
                    <p>${message}</p>
                </div>
            `;
        }

        async function checkTables() {
            try {
                const response = await fetch('install_database.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=check'
                });
                
                const result = await response.json();
                
                if (result.status === 'success') {
                    let message = '数据库检查结果：<br>';
                    result.tables.forEach(table => {
                        const status = table.exists ? '✅ 已存在' : '❌ 不存在';
                        message += `${table.name}: ${status}<br>`;
                    });
                    showResult('warning', '表检查完成', message);
                } else {
                    showResult('error', '检查失败', result.message);
                }
            } catch (error) {
                showResult('error', '检查失败', '网络错误: ' + error.message);
            }
        }

        async function installTables() {
            document.getElementById('installProgress').style.display = 'block';
            document.getElementById('installBtn').disabled = true;
            
            updateProgress(10, '开始安装...');
            
            try {
                const response = await fetch('install_database.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=install'
                });
                
                updateProgress(50, '正在创建表结构...');
                
                const result = await response.json();
                
                updateProgress(100, '安装完成');
                
                if (result.status === 'success') {
                    showResult('success', '安装成功', result.message);
                } else {
                    showResult('error', '安装失败', result.message);
                }
            } catch (error) {
                showResult('error', '安装失败', '网络错误: ' + error.message);
            } finally {
                document.getElementById('installBtn').disabled = false;
            }
        }

        function showSQL() {
            const sqlDiv = document.getElementById('sqlDisplay');
            if (sqlDiv.style.display === 'none') {
                fetch('database_schema.sql')
                    .then(response => response.text())
                    .then(sql => {
                        document.getElementById('sqlContent').textContent = sql;
                        sqlDiv.style.display = 'block';
                    })
                    .catch(error => {
                        document.getElementById('sqlContent').textContent = '无法加载SQL文件: ' + error.message;
                        sqlDiv.style.display = 'block';
                    });
            } else {
                sqlDiv.style.display = 'none';
            }
        }
    </script>
</body>
</html>

<?php
// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    
    if ($action === 'check') {
        checkExistingTables();
    } elseif ($action === 'install') {
        installDatabaseTables();
    }
    exit;
}

function checkExistingTables() {
    global $conn;
    
    $tables = [
        'Kunlun_alarm_records',
        'Kunlun_sms_alert_config', 
        'Kunlun_sms_send_log'
    ];
    
    $result = [];
    
    foreach ($tables as $table) {
        $sql = "SHOW TABLES LIKE '$table'";
        $query_result = mysqli_query($conn, $sql);
        $exists = mysqli_num_rows($query_result) > 0;
        
        $result[] = [
            'name' => $table,
            'exists' => $exists
        ];
    }
    
    echo json_encode([
        'status' => 'success',
        'tables' => $result
    ]);
}

function installDatabaseTables() {
    global $conn;
    
    try {
        // 读取SQL文件
        $sqlFile = __DIR__ . '/database_schema.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('SQL文件不存在: ' . $sqlFile);
        }
        
        $sql = file_get_contents($sqlFile);
        if ($sql === false) {
            throw new Exception('无法读取SQL文件');
        }
        
        // 开始事务
        mysqli_autocommit($conn, false);
        
        // 分割SQL语句
        $statements = explode(';', $sql);
        $executed = 0;
        $errors = [];
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (empty($statement) || strpos($statement, '--') === 0) {
                continue;
            }
            
            if (!mysqli_query($conn, $statement)) {
                $errors[] = mysqli_error($conn);
            } else {
                $executed++;
            }
        }
        
        if (empty($errors)) {
            mysqli_commit($conn);
            echo json_encode([
                'status' => 'success',
                'message' => "数据库安装成功！共执行 $executed 条SQL语句。"
            ]);
        } else {
            mysqli_rollback($conn);
            echo json_encode([
                'status' => 'error',
                'message' => '安装失败：' . implode('; ', $errors)
            ]);
        }
        
    } catch (Exception $e) {
        mysqli_rollback($conn);
        echo json_encode([
            'status' => 'error',
            'message' => '安装失败：' . $e->getMessage()
        ]);
    } finally {
        mysqli_autocommit($conn, true);
    }
}
?>
