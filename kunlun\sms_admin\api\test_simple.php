<?php
/**
 * 简单的API测试
 */

require_once '../../../conn_waf.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // 测试基本信息
    $info = [
        'timestamp' => date('Y-m-d H:i:s'),
        'session_id' => session_id(),
        'user_id' => $_SESSION['user_id'] ?? null,
        'post_data' => $_POST,
        'get_data' => $_GET
    ];
    
    // 测试数据库连接
    if ($conn) {
        $info['database'] = 'connected';
        
        // 测试简单查询
        $result = $conn->query("SELECT 1 as test");
        if ($result) {
            $info['query_test'] = 'success';
        } else {
            $info['query_test'] = 'failed: ' . $conn->error;
        }
    } else {
        $info['database'] = 'not connected';
    }
    
    // 测试权限函数
    if (function_exists('isAdmin')) {
        $info['is_admin'] = isAdmin();
    }
    
    if (function_exists('isAppAdmin')) {
        $info['app_admin_36'] = isAppAdmin(36);
    }
    
    echo json_encode([
        'status' => 1,
        'message' => '测试成功',
        'data' => $info
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 0,
        'message' => '测试失败: ' . $e->getMessage(),
        'error' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
