<?php
/**
 * 预警数据自动存储后台程序
 * 创建时间: 2025-08-12
 * 功能: 监听SSE数据流，自动将预警信息存储到数据库并触发短信通知
 * 运行方式: php background_processor.php 或设置为系统服务
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/logs/background_processor.log');

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 引入数据库连接
require_once '../conn_waf.php';

// 创建日志目录
$logDir = __DIR__ . '/logs';
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

/**
 * 日志记录函数
 */
function writeLog($level, $message) {
    $logFile = __DIR__ . '/logs/background_processor.log';
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] [$level] $message" . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    
    // 同时输出到控制台（如果是命令行运行）
    if (php_sapi_name() === 'cli') {
        echo $logEntry;
    }
}

/**
 * 存储预警记录到数据库
 */
function saveAlarmRecord($alarmData) {
    global $conn;
    
    try {
        // 检查是否已存在相同的alarm_id
        $checkSql = "SELECT id FROM Kunlun_alarm_records WHERE alarm_id = ?";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bind_param("s", $alarmData['id']);
        $checkStmt->execute();
        $result = $checkStmt->get_result();
        
        if ($result->num_rows > 0) {
            writeLog('INFO', "预警记录已存在，跳过: " . $alarmData['id']);
            return false;
        }
        
        // 插入新记录（不存储image_base64以节省空间）
        $sql = "INSERT INTO Kunlun_alarm_records (
            alarm_id, alarm_no, person_name, person_sfz, score,
            location_name, latitude, longitude, image_url,
            album_name, severity, captured_time, created_time
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $stmt = $conn->prepare($sql);
        
        // 处理时间格式
        $capturedTime = null;
        if (!empty($alarmData['time'])) {
            $capturedTime = date('Y-m-d H:i:s', strtotime($alarmData['time']));
        }
        
        $stmt->bind_param("ssssdsdssss",
            $alarmData['id'],
            $alarmData['alarmNo'],
            $alarmData['name'],
            $alarmData['sfz'],
            $alarmData['score'],
            $alarmData['location'],
            $alarmData['lat'],
            $alarmData['lon'],
            $alarmData['imageUrl'],
            $alarmData['album'],
            $alarmData['severity'],
            $capturedTime
        );
        
        if ($stmt->execute()) {
            $insertId = $stmt->insert_id;
            writeLog('SUCCESS', "预警记录保存成功，ID: $insertId, 人员: " . $alarmData['name']);
            
            // 触发短信通知检查
            checkAndSendSmsAlert($insertId, $alarmData);
            
            return $insertId;
        } else {
            writeLog('ERROR', "预警记录保存失败: " . $stmt->error);
            return false;
        }
        
    } catch (Exception $e) {
        writeLog('ERROR', "保存预警记录时发生异常: " . $e->getMessage());
        return false;
    }
}

/**
 * 检查并发送短信预警
 */
function checkAndSendSmsAlert($alarmRecordId, $alarmData) {
    global $conn;
    
    try {
        // 查询匹配的短信配置
        $sql = "SELECT 
            sac.id as config_id,
            sac.recipient_user_id,
            sac.alert_level,
            u.name as recipient_name,
            u.phone as recipient_phone,
            u.organization_unit as recipient_unit_id,
            unit.unit_name as recipient_unit_name
        FROM Kunlun_sms_alert_config sac
        JOIN 3_user u ON sac.recipient_user_id = u.id
        LEFT JOIN 2_unit unit ON u.organization_unit = unit.id
        WHERE sac.alarm_person_sfz = ?
        AND sac.is_active = 1
        AND (sac.valid_start_time IS NULL OR sac.valid_start_time <= NOW())
        AND (sac.valid_end_time IS NULL OR sac.valid_end_time >= NOW())
        AND (sac.alert_level = 'all' OR sac.alert_level = ?)
        AND u.phone IS NOT NULL AND u.phone != ''";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $alarmData['sfz'], $alarmData['severity']);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            writeLog('INFO', "未找到匹配的短信配置，身份证: " . $alarmData['sfz']);
            return;
        }
        
        while ($config = $result->fetch_assoc()) {
            // 创建短信发送记录
            createSmsRecord($alarmRecordId, $config, $alarmData);
        }
        
    } catch (Exception $e) {
        writeLog('ERROR', "检查短信配置时发生异常: " . $e->getMessage());
    }
}

/**
 * 创建短信发送记录
 */
function createSmsRecord($alarmRecordId, $config, $alarmData) {
    global $conn;
    
    try {
        // 生成短信内容
        $smsContent = generateSmsContent($alarmData, $config);
        
        // 插入短信发送记录
        $sql = "INSERT INTO Kunlun_sms_send_log (
            alarm_record_id, config_id, recipient_user_id, recipient_name,
            recipient_phone, recipient_unit_id, recipient_unit_name,
            sms_content, send_status, created_time
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("iiisssss",
            $alarmRecordId,
            $config['config_id'],
            $config['recipient_user_id'],
            $config['recipient_name'],
            $config['recipient_phone'],
            $config['recipient_unit_id'],
            $config['recipient_unit_name'],
            $smsContent
        );
        
        if ($stmt->execute()) {
            $smsLogId = $stmt->insert_id;
            writeLog('INFO', "短信记录创建成功，ID: $smsLogId, 接收人: " . $config['recipient_name']);
            
            // 立即尝试发送短信
            sendSmsMessage($smsLogId);
        } else {
            writeLog('ERROR', "短信记录创建失败: " . $stmt->error);
        }
        
    } catch (Exception $e) {
        writeLog('ERROR', "创建短信记录时发生异常: " . $e->getMessage());
    }
}

/**
 * 生成短信内容
 */
function generateSmsContent($alarmData, $config) {
    $severityText = [
        'low' => '低危',
        'medium' => '中危', 
        'high' => '高危'
    ];
    
    $severity = $severityText[$alarmData['severity']] ?? '未知';
    $time = date('m月d日 H:i', strtotime($alarmData['time']));
    
    $content = "【预警通知】{$time}在{$alarmData['location']}发现{$severity}预警人员{$alarmData['name']}，相似度{$alarmData['score']}%，请及时关注。";
    
    return $content;
}

/**
 * 发送短信
 */
function sendSmsMessage($smsLogId) {
    global $conn;
    
    try {
        // 获取短信记录
        $sql = "SELECT * FROM Kunlun_sms_send_log WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $smsLogId);
        $stmt->execute();
        $result = $stmt->get_result();
        $smsLog = $result->fetch_assoc();
        
        if (!$smsLog) {
            writeLog('ERROR', "短信记录不存在: $smsLogId");
            return;
        }
        
        // 引入短信发送接口
        require_once '../api/SmsSystem.php';
        
        // 发送短信
        $response = SMS_sendmessage(
            $smsLog['recipient_phone'],
            $smsLog['sms_content'],
            '岳池县公安局'
        );
        
        // 解析响应
        $responseData = json_decode($response, true);
        $sendStatus = 'failed';
        $responseMessage = $response;
        
        if ($responseData && isset($responseData['renturncode'])) {
            if ($responseData['renturncode'] === '0' || $responseData['renturncode'] === 0) {
                $sendStatus = 'success';
                writeLog('SUCCESS', "短信发送成功，接收人: " . $smsLog['recipient_name']);
            } else {
                writeLog('ERROR', "短信发送失败，错误码: " . $responseData['renturncode'] . ", 消息: " . ($responseData['message'] ?? ''));
            }
        }
        
        // 更新发送状态
        $updateSql = "UPDATE Kunlun_sms_send_log SET 
            send_status = ?, send_time = NOW(), response_message = ?
            WHERE id = ?";
        $updateStmt = $conn->prepare($updateSql);
        $updateStmt->bind_param("ssi", $sendStatus, $responseMessage, $smsLogId);
        $updateStmt->execute();
        
    } catch (Exception $e) {
        writeLog('ERROR', "发送短信时发生异常: " . $e->getMessage());
        
        // 更新为失败状态
        $updateSql = "UPDATE Kunlun_sms_send_log SET 
            send_status = 'failed', send_time = NOW(), response_message = ?
            WHERE id = ?";
        $updateStmt = $conn->prepare($updateSql);
        $errorMsg = "异常: " . $e->getMessage();
        $updateStmt->bind_param("si", $errorMsg, $smsLogId);
        $updateStmt->execute();
    }
}

/**
 * 主处理循环
 */
function startProcessing() {
    writeLog('INFO', "预警数据处理程序启动");
    
    $sseUrl = 'http://localhost/kunlun/kafka.php';
    $retryCount = 0;
    $maxRetries = 5;
    
    while (true) {
        try {
            writeLog('INFO', "连接到SSE服务器: $sseUrl");
            
            // 创建SSE连接
            $context = stream_context_create([
                'http' => [
                    'timeout' => 30,
                    'user_agent' => 'Kunlun Background Processor 1.0'
                ]
            ]);
            
            $stream = fopen($sseUrl, 'r', false, $context);
            
            if (!$stream) {
                throw new Exception("无法连接到SSE服务器");
            }
            
            writeLog('SUCCESS', "SSE连接建立成功");
            $retryCount = 0;
            
            // 读取SSE数据流
            while (!feof($stream)) {
                $line = fgets($stream);
                
                if ($line === false) {
                    break;
                }
                
                $line = trim($line);
                
                // 解析SSE事件
                if (strpos($line, 'event: alarm') === 0) {
                    // 下一行应该是数据
                    $dataLine = fgets($stream);
                    if ($dataLine && strpos($dataLine, 'data: ') === 0) {
                        $jsonData = substr($dataLine, 6);
                        $alarmData = json_decode($jsonData, true);
                        
                        if ($alarmData) {
                            writeLog('INFO', "接收到预警数据: " . $alarmData['name']);
                            saveAlarmRecord($alarmData);
                        }
                    }
                }
            }
            
            fclose($stream);
            writeLog('WARNING', "SSE连接断开，准备重连");
            
        } catch (Exception $e) {
            writeLog('ERROR', "处理异常: " . $e->getMessage());
            $retryCount++;
            
            if ($retryCount >= $maxRetries) {
                writeLog('FATAL', "达到最大重试次数，程序退出");
                break;
            }
            
            $delay = min(30, pow(2, $retryCount));
            writeLog('INFO', "等待 {$delay} 秒后重试 (第 {$retryCount}/{$maxRetries} 次)");
            sleep($delay);
        }
    }
}

// 信号处理（优雅退出）
if (function_exists('pcntl_signal')) {
    pcntl_signal(SIGTERM, function() {
        writeLog('INFO', "接收到SIGTERM信号，程序退出");
        exit(0);
    });
    
    pcntl_signal(SIGINT, function() {
        writeLog('INFO', "接收到SIGINT信号，程序退出");
        exit(0);
    });
}

// 检查是否为命令行运行
if (php_sapi_name() === 'cli') {
    // 命令行模式，启动处理循环
    startProcessing();
} else {
    // Web模式，显示状态页面
    ?>
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>预警数据处理程序状态</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .status { padding: 20px; border-radius: 8px; margin: 20px 0; }
            .info { background: #d1ecf1; color: #0c5460; }
            .warning { background: #fff3cd; color: #856404; }
        </style>
    </head>
    <body>
        <h1>预警数据处理程序</h1>
        <div class="status info">
            <h3>程序状态</h3>
            <p>此程序需要在命令行模式下运行：</p>
            <code>php <?php echo basename(__FILE__); ?></code>
        </div>
        <div class="status warning">
            <h3>注意事项</h3>
            <ul>
                <li>确保数据库连接正常</li>
                <li>确保SSE服务器可访问</li>
                <li>建议设置为系统服务自动启动</li>
            </ul>
        </div>
    </body>
    </html>
    <?php
}
?>
