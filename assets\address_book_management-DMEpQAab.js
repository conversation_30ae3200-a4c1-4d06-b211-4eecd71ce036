import{_ as ae,j as r,k as le,y as L,B as te,l as ne,r as p,ac as oe,d as se,o as D,b as c,a as l,w as o,n as re,t as j,a9 as de,u as pe,ad as ue,c as A,f as ie,m as x,p as h,g as ce,i as me,h as _e}from"./index-gqi1S6ey.js";import{s as U}from"./requests-CX9DRHRN.js";/* empty css              */const fe={class:"address-book-management-container"},ve={class:"left-panel"},he=["onClick"],ge={class:"right-panel"},be={class:"action-buttons",style:{display:"flex","align-items":"center","margin-bottom":"10px"}},Ve={class:"left-search",style:{display:"flex","align-items":"center"}},ye={class:"right-info",style:{"margin-left":"auto",display:"flex","align-items":"center",gap:"10px"}},Ce={class:"dialog-footer"},ke={__name:"AddressBookManagement",setup(B){const m=r(!1),g=r(!1),_=r(null),V=r(""),b=r(""),u=r(1),y=r(50),C=r(0),M=r([]),v=r([]),k=r([]),I=r(null),n=le({id:"",name:"",id_number:"",phone:"",alt_phone_1:"",alt_phone_2:"",short_code:"",landline:""}),$=L(()=>V.value?v.value.filter(e=>e.show!==!1&&e.unit_name.toLowerCase().includes(V.value.toLowerCase())):v.value.filter(e=>e.show!==!1)),S=(e,a=0)=>{e.forEach(s=>{s.indent=a,s.show=!0,v.value.push(s),s.children&&s.children.length>0&&S(s.children,a+20)})},F=L(()=>{if(!_.value)return"";const e=v.value.find(a=>a.id===_.value);return e?e.unit_name:""}),R=async()=>{try{console.log("开始获取单位信息..."),m.value=!0;const e=new FormData;e.append("controlCode","get_units"),console.log("发送请求到: /api/address_book_management.php"),console.log("请求参数:",{controlCode:"get_units"});const a=await U.post("/api/address_book_management.php",e);console.log("接口响应:",a),a.status===1?(M.value=a.data,v.value=[],S(a.data),console.log("单位数据加载成功，共",v.value.length,"个单位")):h.error(a.message||"获取单位信息失败")}catch(e){console.error("获取单位信息失败:",e)}finally{m.value=!1}},i=async()=>{try{m.value=!0;const e=new FormData;e.append("controlCode","get_user"),_.value?e.append("unitId",_.value):e.append("unitId",""),e.append("page",u.value),e.append("pageSize",y.value),b.value&&e.append("search",b.value);const a=await U.post("/api/address_book_management.php",e);a.status===1?(k.value=a.data,C.value=a.pagination.totalCount):(h.error(a.message||"获取用户信息失败"),k.value=[],C.value=0)}catch(e){console.error("获取用户信息失败:",e),h.error("获取用户信息失败"),k.value=[],C.value=0}finally{m.value=!1}},N=e=>{_.value=e.id,u.value=1,i()},T=e=>{N(e)},O=e=>{Object.assign(n,{id:e.id,name:e.name||"",id_number:e.id_number||"",phone:e.phone||"",alt_phone_1:e.alt_phone_1||"",alt_phone_2:e.alt_phone_2||"",short_code:e.short_code||"",landline:e.landline||""}),g.value=!0,I.value&&I.value.clearValidate()},P=async()=>{g.value=!1;try{const e=new FormData;e.append("controlCode","edit_user"),e.append("id",n.id),e.append("phone",n.phone),e.append("alt_phone_1",n.alt_phone_1),e.append("alt_phone_2",n.alt_phone_2),e.append("short_code",n.short_code),e.append("landline",n.landline);const a=await U.post("/api/address_book_management.php",e);a.status===1?(await i(),h.success("更新成功")):h.error(a.message)}catch(e){console.error("更新用户信息失败:",e),h.error("更新用户信息失败，请稍后重试")}},K=()=>{u.value=1,i()},q=()=>{b.value="",u.value=1,i()},G=e=>{u.value=e,i()},H=e=>{y.value=e,u.value=1,i()};return te(_,()=>{_.value&&i()}),ne(()=>{console.log("AddressBookManagement页面已挂载，开始初始化..."),R(),i()}),(e,a)=>{const s=p("el-input"),d=p("el-table-column"),E=p("el-table"),J=p("el-icon"),Q=p("el-tag"),w=p("el-button"),W=p("el-pagination"),f=p("el-form-item"),X=p("el-form"),Y=p("el-dialog"),Z=oe("loading");return D(),se("div",fe,[c("div",ve,[l(s,{modelValue:V.value,"onUpdate:modelValue":a[0]||(a[0]=t=>V.value=t),placeholder:"搜索单位",class:"department-search",clearable:""},null,8,["modelValue"]),l(E,{data:$.value,border:"",stripe:"",fit:"",class:"department-table",onRowClick:N},{default:o(()=>[l(d,{prop:"unit_name",label:"单位名称"},{default:o(({row:t})=>[c("span",{class:"indent",style:re({width:`${t.indent}px`})},null,4),c("span",{onClick:ee=>T(t),style:{cursor:"pointer"}},j(t.unit_name),9,he)]),_:1})]),_:1},8,["data"])]),c("div",ge,[c("div",be,[c("div",Ve,[l(s,{modelValue:b.value,"onUpdate:modelValue":a[1]||(a[1]=t=>b.value=t),placeholder:"搜索用户（姓名、身份证号、电话号码）",clearable:"",style:{width:"300px"},onInput:K,onClear:q},{prefix:o(()=>[l(J,null,{default:o(()=>[l(pe(ue))]),_:1})]),_:1},8,["modelValue"])]),c("div",ye,[F.value?(D(),A(Q,{key:0,type:"info"},{default:o(()=>[x(" 当前单位："+j(F.value),1)]),_:1})):ie("",!0)])]),de((D(),A(E,{data:k.value,border:"",stripe:"",fit:"",class:"user-table"},{default:o(()=>[l(d,{type:"index",label:"序号",width:"60",align:"center",index:t=>(u.value-1)*y.value+t+1},null,8,["index"]),l(d,{prop:"name",label:"姓名",width:"120",align:"center"}),l(d,{prop:"id_number",label:"身份证号",width:"200",align:"center"}),l(d,{prop:"phone",label:"电话I",align:"center"}),l(d,{prop:"alt_phone_1",label:"电话II",align:"center"}),l(d,{prop:"alt_phone_2",label:"电话III",align:"center"}),l(d,{prop:"short_code",label:"短号",align:"center"}),l(d,{prop:"landline",label:"座机",align:"center"}),l(d,{label:"操作",width:"120",align:"center"},{default:o(({row:t})=>[l(w,{type:"primary",size:"small",onClick:ee=>O(t)},{default:o(()=>a[11]||(a[11]=[x(" 编辑 ")])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Z,m.value]]),l(W,{"current-page":u.value,"page-size":y.value,total:C.value,layout:"prev, pager, next, jumper, sizes",onCurrentChange:G,onSizeChange:H},null,8,["current-page","page-size","total"])]),l(Y,{modelValue:g.value,"onUpdate:modelValue":a[10]||(a[10]=t=>g.value=t),title:"编辑用户联系方式",width:"600px","close-on-click-modal":!1},{footer:o(()=>[c("span",Ce,[l(w,{onClick:a[9]||(a[9]=t=>g.value=!1)},{default:o(()=>a[12]||(a[12]=[x("取消")])),_:1,__:[12]}),l(w,{type:"primary",onClick:P},{default:o(()=>a[13]||(a[13]=[x("确定")])),_:1,__:[13]})])]),default:o(()=>[l(X,{model:n,ref_key:"userFormRef",ref:I,"label-width":"120px"},{default:o(()=>[l(f,{label:"姓名",prop:"name"},{default:o(()=>[l(s,{modelValue:n.name,"onUpdate:modelValue":a[2]||(a[2]=t=>n.name=t),disabled:""},null,8,["modelValue"])]),_:1}),l(f,{label:"身份证号",prop:"id_number"},{default:o(()=>[l(s,{modelValue:n.id_number,"onUpdate:modelValue":a[3]||(a[3]=t=>n.id_number=t),disabled:""},null,8,["modelValue"])]),_:1}),l(f,{label:"电话I",prop:"phone"},{default:o(()=>[l(s,{modelValue:n.phone,"onUpdate:modelValue":a[4]||(a[4]=t=>n.phone=t),maxlength:"11"},null,8,["modelValue"])]),_:1}),l(f,{label:"电话II",prop:"alt_phone_1"},{default:o(()=>[l(s,{modelValue:n.alt_phone_1,"onUpdate:modelValue":a[5]||(a[5]=t=>n.alt_phone_1=t),maxlength:"11"},null,8,["modelValue"])]),_:1}),l(f,{label:"电话III",prop:"alt_phone_2"},{default:o(()=>[l(s,{modelValue:n.alt_phone_2,"onUpdate:modelValue":a[6]||(a[6]=t=>n.alt_phone_2=t),maxlength:"11"},null,8,["modelValue"])]),_:1}),l(f,{label:"短号",prop:"short_code"},{default:o(()=>[l(s,{modelValue:n.short_code,"onUpdate:modelValue":a[7]||(a[7]=t=>n.short_code=t)},null,8,["modelValue"])]),_:1}),l(f,{label:"座机",prop:"landline"},{default:o(()=>[l(s,{modelValue:n.landline,"onUpdate:modelValue":a[8]||(a[8]=t=>n.landline=t)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},xe=ae(ke,[["__scopeId","data-v-d8f40eac"]]),z=ce(xe);z.use(me);for(const[B,m]of Object.entries(_e))z.component(B,m);z.mount("#app");
