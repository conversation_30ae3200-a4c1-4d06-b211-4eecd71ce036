<?php
/**
 * 简单计划任务管理器
 * 单文件版本，包含所有功能
 */

// 设置时区为中国标准时间
date_default_timezone_set('Asia/Shanghai');

// 数据库配置 - 移到最前面
$db_config = [
    'host' => '127.0.0.1',
    'port' => 3306,
    'database' => 'taskmgr',
    'username' => 'root',
    'password' => 'YC@yc110',
    'charset' => 'utf8'  // 改为utf8，避免字符集问题
];

// 如果是命令行调用且参数为cron，则执行计划任务
if (php_sapi_name() === 'cli' && isset($argv[1]) && $argv[1] === 'cron') {
    runCronTasks();
    exit;
}

// 数据库连接
function getDB() {
    global $db_config;
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['database']};charset={$db_config['charset']}";
            $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]);
        } catch (PDOException $e) {
            die("数据库连接失败: " . $e->getMessage());
        }
    }
    
    return $pdo;
}

// 初始化数据库表
function initDatabase() {
    $pdo = getDB();
    
    // 创建任务表
    $sql = "CREATE TABLE IF NOT EXISTS tasks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        command TEXT NOT NULL,
        cron_expression VARCHAR(100) NOT NULL,
        status ENUM('enabled', 'disabled') DEFAULT 'enabled',
        last_run DATETIME NULL,
        next_run DATETIME NULL,
        run_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    
    // 创建日志表
    $sql = "CREATE TABLE IF NOT EXISTS task_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT NOT NULL,
        status ENUM('success', 'failed') NOT NULL,
        output TEXT,
        error_message TEXT,
        start_time DATETIME NOT NULL,
        end_time DATETIME NOT NULL,
        duration DECIMAL(10,2) NOT NULL
    )";
    $pdo->exec($sql);
}

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        $pdo = getDB();
        $action = $_POST['action'];
        
        switch ($action) {
            case 'add_task':
                $name = $_POST['name'];
                $command = $_POST['command'];
                $cronExpression = $_POST['cron_expression'];

                // 检查是否为单次执行任务
                if (strpos($cronExpression, '@once') === 0) {
                    if ($cronExpression === '@once') {
                        // 立即执行的单次任务
                        $stmt = $pdo->prepare("INSERT INTO tasks (name, command, cron_expression, status) VALUES (?, ?, ?, 'disabled')");
                        $stmt->execute([$name, $command, '* * * * *']); // 使用通用cron表达式，但状态为禁用

                        $taskId = $pdo->lastInsertId();

                        // 立即执行任务
                        $start_time = date('Y-m-d H:i:s');
                        $start = microtime(true);

                    // 执行命令
                    $output = '';
                    $exit_code = 0;

                    if (function_exists('exec')) {
                        exec($command . ' 2>&1', $output_lines, $exit_code);
                        $output = implode("\n", $output_lines);
                    } elseif (function_exists('shell_exec')) {
                        $output = shell_exec($command . ' 2>&1');
                        $exit_code = 0;
                    } else {
                        $output = "错误：命令执行函数被禁用";
                        $exit_code = -1;
                    }

                    if (empty($output)) {
                        $output = "命令执行完成，无输出内容";
                    }

                    $end = microtime(true);
                    $duration = round($end - $start, 2);
                    $end_time = date('Y-m-d H:i:s');
                    $status = ($exit_code === 0) ? 'success' : 'failed';

                    // 记录执行日志
                    $stmt = $pdo->prepare("INSERT INTO task_logs (task_id, status, output, error_message, start_time, end_time, duration) VALUES (?, ?, ?, ?, ?, ?, ?)");
                    $error_message = ($exit_code !== 0) ? "命令执行失败，退出码: $exit_code" : null;
                    $stmt->execute([$taskId, $status, $output, $error_message, $start_time, $end_time, $duration]);

                    // 更新任务统计
                    $stmt = $pdo->prepare("UPDATE tasks SET last_run = ?, run_count = 1 WHERE id = ?");
                    $stmt->execute([$start_time, $taskId]);

                    echo json_encode([
                        'success' => true,
                        'message' => '单次任务创建并执行完成',
                        'output' => $output,
                        'status' => $status,
                        'duration' => $duration
                    ]);
                    } else {
                        // 指定时间执行的单次任务
                        // 解析时间：@once:2024-01-01 14:30
                        $timeStr = substr($cronExpression, 6); // 去掉 "@once:"
                        $executeTime = DateTime::createFromFormat('Y-m-d H:i', $timeStr);

                        if ($executeTime && $executeTime > new DateTime()) {
                            // 转换为cron表达式
                            $minute = $executeTime->format('i');
                            $hour = $executeTime->format('H');
                            $day = $executeTime->format('j');
                            $month = $executeTime->format('n');
                            $realCronExpression = "$minute $hour $day $month *";

                            // 创建启用状态的任务，到时间后会自动执行并禁用
                            $stmt = $pdo->prepare("INSERT INTO tasks (name, command, cron_expression, status) VALUES (?, ?, ?, 'enabled')");
                            $stmt->execute([$name, $command, $realCronExpression]);

                            echo json_encode([
                                'success' => true,
                                'message' => '定时单次任务创建成功，将在 ' . $executeTime->format('Y-m-d H:i') . ' 执行'
                            ]);
                        } else {
                            echo json_encode(['success' => false, 'message' => '执行时间格式错误或时间已过期']);
                        }
                    }
                } else {
                    // 普通定时任务
                    $stmt = $pdo->prepare("INSERT INTO tasks (name, command, cron_expression) VALUES (?, ?, ?)");
                    $stmt->execute([$name, $command, $cronExpression]);
                    echo json_encode(['success' => true, 'message' => '定时任务添加成功']);
                }
                break;

            case 'edit_task':
                $taskId = $_POST['task_id'];
                $name = $_POST['name'];
                $command = $_POST['command'];
                $cronExpression = $_POST['cron_expression'];

                $stmt = $pdo->prepare("UPDATE tasks SET name = ?, command = ?, cron_expression = ? WHERE id = ?");
                $stmt->execute([$name, $command, $cronExpression, $taskId]);
                echo json_encode(['success' => true, 'message' => '任务修改成功']);
                break;

            case 'get_task':
                $taskId = $_POST['id'];
                $stmt = $pdo->prepare("SELECT * FROM tasks WHERE id = ?");
                $stmt->execute([$taskId]);
                $task = $stmt->fetch();

                if ($task) {
                    echo json_encode(['success' => true, 'task' => $task]);
                } else {
                    echo json_encode(['success' => false, 'message' => '任务不存在']);
                }
                break;

            case 'delete_task':
                $stmt = $pdo->prepare("DELETE FROM tasks WHERE id = ?");
                $stmt->execute([$_POST['id']]);
                echo json_encode(['success' => true, 'message' => '任务删除成功']);
                break;
                
            case 'toggle_task':
                $stmt = $pdo->prepare("UPDATE tasks SET status = IF(status = 'enabled', 'disabled', 'enabled') WHERE id = ?");
                $stmt->execute([$_POST['id']]);
                echo json_encode(['success' => true, 'message' => '任务状态更新成功']);
                break;
                
            case 'run_task':
                $stmt = $pdo->prepare("SELECT * FROM tasks WHERE id = ?");
                $stmt->execute([$_POST['id']]);
                $task = $stmt->fetch();

                if ($task) {
                    $start_time = date('Y-m-d H:i:s');
                    $start = microtime(true);

                    // 执行命令并捕获退出码
                    $command = $task['command'] . ' 2>&1';
                    $output = '';
                    $exit_code = 0;

                    // 尝试不同的执行函数
                    if (function_exists('exec')) {
                        exec($command, $output_lines, $exit_code);
                        $output = implode("\n", $output_lines);
                    } elseif (function_exists('shell_exec')) {
                        $output = shell_exec($command);
                        $exit_code = 0; // shell_exec无法获取退出码，假设成功
                    } elseif (function_exists('system')) {
                        ob_start();
                        $exit_code = system($command);
                        $output = ob_get_clean();
                    } elseif (function_exists('passthru')) {
                        ob_start();
                        passthru($command, $exit_code);
                        $output = ob_get_clean();
                    } else {
                        $output = "错误：所有命令执行函数都被禁用";
                        $exit_code = -1;
                    }

                    // 如果没有输出，添加默认信息
                    if (empty($output)) {
                        $output = "命令执行完成，无输出内容";
                    }

                    $end = microtime(true);
                    $duration = round($end - $start, 2);
                    $end_time = date('Y-m-d H:i:s');

                    // 根据退出码判断状态
                    $status = ($exit_code === 0) ? 'success' : 'failed';

                    // 记录日志
                    try {
                        $stmt = $pdo->prepare("INSERT INTO task_logs (task_id, status, output, error_message, start_time, end_time, duration) VALUES (?, ?, ?, ?, ?, ?, ?)");
                        $error_message = ($exit_code !== 0) ? "命令执行失败，退出码: $exit_code" : null;
                        $stmt->execute([$task['id'], $status, $output, $error_message, $start_time, $end_time, $duration]);

                        // 更新任务统计
                        $stmt = $pdo->prepare("UPDATE tasks SET last_run = ?, run_count = run_count + 1 WHERE id = ?");
                        $stmt->execute([$start_time, $task['id']]);

                        echo json_encode([
                            'success' => true,
                            'message' => '任务执行完成',
                            'output' => $output,
                            'status' => $status,
                            'duration' => $duration,
                            'exit_code' => $exit_code
                        ]);
                    } catch (PDOException $e) {
                        echo json_encode(['success' => false, 'message' => '日志记录失败: ' . $e->getMessage()]);
                    }
                } else {
                    echo json_encode(['success' => false, 'message' => '任务不存在']);
                }
                break;

            case 'delete_log':
                // 删除单条日志
                $logId = $_POST['log_id'] ?? 0;
                if ($logId) {
                    $stmt = $pdo->prepare("DELETE FROM task_logs WHERE id = ?");
                    $stmt->execute([$logId]);
                    echo json_encode(['success' => true, 'message' => '日志删除成功']);
                } else {
                    echo json_encode(['success' => false, 'message' => '日志ID无效']);
                }
                break;

            case 'delete_logs_batch':
                // 批量删除日志
                $logIdsJson = $_POST['log_ids'] ?? '';
                $logIds = json_decode($logIdsJson, true);
                if (!empty($logIds) && is_array($logIds)) {
                    // 确保所有ID都是数字
                    $logIds = array_filter(array_map('intval', $logIds));
                    if (!empty($logIds)) {
                        $placeholders = str_repeat('?,', count($logIds) - 1) . '?';
                        $stmt = $pdo->prepare("DELETE FROM task_logs WHERE id IN ($placeholders)");
                        $stmt->execute($logIds);
                        $deletedCount = $stmt->rowCount();
                        echo json_encode(['success' => true, 'message' => "成功删除 $deletedCount 条日志"]);
                    } else {
                        echo json_encode(['success' => false, 'message' => '无效的日志ID']);
                    }
                } else {
                    echo json_encode(['success' => false, 'message' => '请选择要删除的日志']);
                }
                break;

            case 'clear_all_logs':
                // 清空所有日志
                $stmt = $pdo->prepare("DELETE FROM task_logs");
                $stmt->execute();
                $deletedCount = $stmt->rowCount();
                echo json_encode(['success' => true, 'message' => "成功清空所有日志，共删除 $deletedCount 条记录"]);
                break;

            default:
                echo json_encode(['success' => false, 'message' => '未知操作']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// 获取任务列表
function getTasks() {
    $pdo = getDB();
    try {
        $stmt = $pdo->query("SELECT * FROM tasks ORDER BY created_at DESC");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        // 如果表不存在，返回空数组
        return [];
    }
}

// 获取日志
function getLogs($limit = 50) {
    $pdo = getDB();
    $limit = (int)$limit; // 确保是整数
    try {
        $sql = "
            SELECT l.*, t.name as task_name
            FROM task_logs l
            LEFT JOIN tasks t ON l.task_id = t.id
            ORDER BY l.start_time DESC
            LIMIT $limit
        ";
        $stmt = $pdo->query($sql);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        // 如果表不存在，返回空数组
        return [];
    }
}

// 检查Cron表达式是否匹配当前时间
function cronMatches($cronExpression, $timestamp = null) {
    if ($timestamp === null) {
        $timestamp = time();
    }

    $parts = explode(' ', $cronExpression);
    if (count($parts) !== 5) {
        return false;
    }

    list($minute, $hour, $day, $month, $weekday) = $parts;

    $currentMinute = (int)date('i', $timestamp);
    $currentHour = (int)date('G', $timestamp);
    $currentDay = (int)date('j', $timestamp);
    $currentMonth = (int)date('n', $timestamp);
    $currentWeekday = (int)date('w', $timestamp);

    return cronFieldMatches($minute, $currentMinute, 0, 59) &&
           cronFieldMatches($hour, $currentHour, 0, 23) &&
           cronFieldMatches($day, $currentDay, 1, 31) &&
           cronFieldMatches($month, $currentMonth, 1, 12) &&
           cronFieldMatches($weekday, $currentWeekday, 0, 7);
}

// 检查Cron字段是否匹配
function cronFieldMatches($field, $value, $min, $max) {
    if ($field === '*') {
        return true;
    }

    if (strpos($field, '/') !== false) {
        list($range, $step) = explode('/', $field);
        if ($range === '*') {
            return $value % $step === 0;
        }
    }

    if (strpos($field, ',') !== false) {
        $values = explode(',', $field);
        return in_array($value, array_map('intval', $values));
    }

    if (strpos($field, '-') !== false) {
        list($start, $end) = explode('-', $field);
        return $value >= $start && $value <= $end;
    }

    return (int)$field === $value;
}

// 执行计划任务
function runCronTasks() {
    $pdo = getDB();

    // 获取所有启用的任务
    $stmt = $pdo->query("SELECT * FROM tasks WHERE status = 'enabled'");
    $tasks = $stmt->fetchAll();

    $executedCount = 0;

    foreach ($tasks as $task) {
        if (cronMatches($task['cron_expression'])) {
            echo "执行任务: {$task['name']}\n";

            $start_time = date('Y-m-d H:i:s');
            $start = microtime(true);

            // 执行命令并捕获退出码
            $command = $task['command'] . ' 2>&1';
            $output = '';
            $exit_code = 0;

            // 尝试不同的执行函数
            if (function_exists('exec')) {
                exec($command, $output_lines, $exit_code);
                $output = implode("\n", $output_lines);
            } elseif (function_exists('shell_exec')) {
                $output = shell_exec($command);
                $exit_code = 0; // shell_exec无法获取退出码，假设成功
            } elseif (function_exists('system')) {
                ob_start();
                $exit_code = system($command);
                $output = ob_get_clean();
            } elseif (function_exists('passthru')) {
                ob_start();
                passthru($command, $exit_code);
                $output = ob_get_clean();
            } else {
                $output = "错误：所有命令执行函数都被禁用";
                $exit_code = -1;
            }

            // 如果没有输出，添加默认信息
            if (empty($output)) {
                $output = "命令执行完成，无输出内容";
            }

            $end = microtime(true);
            $duration = round($end - $start, 2);
            $end_time = date('Y-m-d H:i:s');

            // 根据退出码判断状态
            $status = ($exit_code === 0) ? 'success' : 'failed';

            // 记录日志
            $stmt = $pdo->prepare("INSERT INTO task_logs (task_id, status, output, error_message, start_time, end_time, duration) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $error_message = ($exit_code !== 0) ? "命令执行失败，退出码: $exit_code" : null;
            $stmt->execute([$task['id'], $status, $output, $error_message, $start_time, $end_time, $duration]);

            // 更新任务统计
            $stmt = $pdo->prepare("UPDATE tasks SET last_run = ?, run_count = run_count + 1 WHERE id = ?");
            $stmt->execute([$start_time, $task['id']]);

            // 检查是否为单次任务（通过cron表达式判断是否为特定日期时间）
            $cronParts = explode(' ', $task['cron_expression']);
            if (count($cronParts) == 5 &&
                is_numeric($cronParts[0]) && is_numeric($cronParts[1]) &&
                is_numeric($cronParts[2]) && is_numeric($cronParts[3])) {
                // 如果是具体的日期时间（不包含*），执行后禁用任务
                $stmt = $pdo->prepare("UPDATE tasks SET status = 'disabled' WHERE id = ?");
                $stmt->execute([$task['id']]);
                echo "单次任务执行完成，已自动禁用\n";
            }

            $executedCount++;
            echo "任务执行完成，耗时: {$duration}秒\n";
        }
    }

    if ($executedCount === 0) {
        echo "没有需要执行的任务\n";
    } else {
        echo "共执行了 $executedCount 个任务\n";
    }
}

// 初始化数据库
initDatabase();

// 获取数据
$tasks = getTasks();
$logs = getLogs();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单计划任务管理器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #4a5568;
        }

        .header p {
            font-size: 1.1em;
            color: #718096;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .quick-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .quick-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .quick-btn.success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .quick-btn.warning {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        }

        .quick-btn.danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        }

        .form-section {
            background: #f7fafc;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #4a5568;
            font-size: 14px;
        }

        input, textarea, select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .preset-buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        .preset-btn {
            background: #e2e8f0;
            color: #4a5568;
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .preset-btn:hover {
            background: #cbd5e0;
        }

        .task-list {
            background: #f7fafc;
            border-radius: 12px;
            overflow: hidden;
        }

        .task-item {
            background: white;
            margin: 10px;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .task-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .task-name {
            font-size: 18px;
            font-weight: bold;
            color: #2d3748;
            flex: 1;
        }

        .task-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        /* 新的日志卡片样式 */
        .log-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 15px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .log-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-1px);
        }

        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f1f5f9;
        }

        .log-select {
            display: flex;
            align-items: center;
            white-space: nowrap;
        }

        .log-select label {
            white-space: nowrap;
            min-width: auto;
        }

        .log-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .log-status.status-success {
            color: #059669;
            background: #d1fae5;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .log-status.status-error {
            color: #dc2626;
            background: #fee2e2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .btn-delete {
            background: #ef4444;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .btn-delete:hover {
            background: #dc2626;
        }

        .log-content {
            margin-top: 8px;
        }

        .log-task-name {
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .log-details {
            font-size: 14px;
            color: #6b7280;
        }

        .log-time {
            margin-bottom: 8px;
        }

        .log-output {
            margin-top: 8px;
        }

        .output-content {
            background: #f8fafc;
            padding: 8px;
            border-radius: 4px;
            margin-top: 4px;
            font-family: monospace;
            font-size: 13px;
            border-left: 3px solid #e2e8f0;
        }

        .btn-toggle {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            margin-top: 8px;
        }

        .btn-toggle:hover {
            background: #2563eb;
        }

        /* 系统时钟样式 */
        .system-clock {
            text-align: right;
            padding: 5px 0;
        }

        .system-clock #current-time {
            font-family: 'Courier New', monospace;
            font-size: 16px;
            font-weight: bold;
            color: #2d3748;
            letter-spacing: 0.5px;
        }

        .system-clock .clock-label {
            font-size: 11px;
            color: #718096;
            margin-top: 2px;
        }

        .status-enabled {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-disabled {
            background: #fed7d7;
            color: #742a2a;
        }

        .task-details {
            color: #718096;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .task-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
        }

        .btn-success {
            background: #48bb78;
            color: white;
        }

        .btn-success:hover {
            background: #38a169;
        }

        .btn-warning {
            background: #ed8936;
            color: white;
        }

        .btn-warning:hover {
            background: #dd6b20;
        }

        .btn-danger {
            background: #f56565;
            color: white;
        }

        .btn-danger:hover {
            background: #e53e3e;
        }

        .alert {
            padding: 15px 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            display: none;
            font-weight: bold;
        }

        .alert-success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .alert-error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #fc8181;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #718096;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #4a5568;
        }

        .section-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <h1 style="margin: 0;">📅 计划任务管理器</h1>
                <div class="system-clock">
                    <div id="current-time"></div>
                    <div class="clock-label">系统时间</div>
                </div>
            </div>
            <p>简单易用的定时任务管理工具</p>
            <?php
            // 检查命令执行函数是否可用
            $functions = ['exec', 'shell_exec', 'system', 'passthru'];
            $available = false;
            foreach ($functions as $func) {
                if (function_exists($func)) {
                    $available = true;
                    break;
                }
            }

            if (!$available) {
                echo '<div style="background: #fff3cd; color: #856404; padding: 10px; border-radius: 6px; margin-top: 15px;">';
                echo '⚠️ 命令执行函数被禁用，任务无法正常执行。';
                echo '<a href="setup_guide.php" style="color: #856404; text-decoration: underline; margin-left: 10px;">查看启用指南</a>';
                echo '</div>';
            }

            // 检查是否设置了系统Cron
            $cronCheck = shell_exec('crontab -l 2>/dev/null | grep -c "' . __FILE__ . '"');
            if (!$cronCheck || trim($cronCheck) == '0') {
                echo '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 6px; margin-top: 15px;">';
                echo '<div style="font-weight: bold; margin-bottom: 8px;">🚨 重要提示：定时任务不会自动执行！</div>';
                echo '<div style="margin-bottom: 10px;">您需要设置系统Cron来让定时任务自动执行。当前只能手动执行任务。</div>';
                echo '<div>';
                echo '<strong>快速设置：</strong> ';
                echo '<code style="background: rgba(0,0,0,0.1); padding: 2px 6px; border-radius: 3px;">./setup_cron.sh</code>';
                echo ' 或 ';
                echo '<a href="setup_guide.php" style="color: #721c24; text-decoration: underline;">查看详细设置指南</a>';
                echo '</div>';
                echo '</div>';
            } else {
                echo '<div style="background: #d4edda; color: #155724; padding: 10px; border-radius: 6px; margin-top: 15px;">';
                echo '✅ 系统Cron已设置，定时任务将自动执行';
                echo '</div>';
            }
            ?>
        </div>

        <div class="alert alert-success" id="success-alert"></div>
        <div class="alert alert-error" id="error-alert"></div>

        <!-- 快速操作按钮 -->
        <div class="quick-actions">
            <button class="quick-btn" onclick="showAddForm()">
                ➕ 添加新任务
            </button>
            <button class="quick-btn success" onclick="runAllTasks()">
                ▶️ 执行所有任务
            </button>
            <button class="quick-btn warning" onclick="showLogs()">
                📊 查看执行日志
            </button>
            <button class="quick-btn danger" onclick="showHelp()">
                ❓ 使用帮助
            </button>
        </div>

        <!-- 添加任务表单 -->
        <div class="card" id="add-form" style="display: none;">
            <div class="section-title">添加新任务</div>
            <div class="form-section">
                <form id="task-form">
                    <div class="form-group">
                        <label>📝 任务名称</label>
                        <input type="text" name="name" required placeholder="给你的任务起个名字，比如：每日备份">
                    </div>

                    <div class="form-group">
                        <label>⚡ 选择任务类型</label>
                        <select id="task-type" onchange="updateCommand()">
                            <option value="">请选择任务类型</option>
                            <optgroup label="📁 文件操作">
                                <option value="backup">文件备份</option>
                                <option value="cleanup">清理临时文件</option>
                                <option value="sync">文件同步</option>
                            </optgroup>
                            <optgroup label="💻 脚本执行">
                                <option value="php">执行PHP脚本</option>
                                <option value="python">执行Python脚本</option>
                                <option value="shell">执行Shell脚本</option>
                                <option value="node">执行Node.js脚本</option>
                            </optgroup>
                            <optgroup label="🗄️ 数据库操作">
                                <option value="mysql_backup">MySQL数据库备份</option>
                                <option value="mysql_optimize">MySQL数据库优化</option>
                            </optgroup>
                            <optgroup label="🔍 系统监控">
                                <option value="check">系统状态检查</option>
                                <option value="disk_check">磁盘空间检查</option>
                                <option value="log_check">日志检查</option>
                                <option value="service_check">服务状态检查</option>
                            </optgroup>
                            <optgroup label="🌐 网络操作">
                                <option value="curl">HTTP请求</option>
                                <option value="ping">网络连通性测试</option>
                                <option value="wget">文件下载</option>
                            </optgroup>
                            <optgroup label="⚙️ 其他">
                                <option value="custom">自定义命令</option>
                            </optgroup>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>💻 执行命令</label>
                        <textarea name="command" id="command" required placeholder="系统会根据你选择的任务类型自动填写，你也可以手动修改"></textarea>
                    </div>

                    <div class="form-group">
                        <label>⏰ 执行时间设置</label>

                        <div style="margin-bottom: 15px;">
                            <label style="font-size: 14px; color: #666;">执行频率</label>
                            <select id="frequency" onchange="updateTimeOptions()" style="margin-bottom: 10px;">
                                <option value="">请选择执行频率</option>
                                <option value="once">🔥 单次执行</option>
                                <option value="minute">⏱️ 每分钟</option>
                                <option value="hour">🕐 每小时</option>
                                <option value="day">📅 每天</option>
                                <option value="week">📆 每周</option>
                                <option value="month">🗓️ 每月</option>
                            </select>
                        </div>

                        <!-- 单次执行设置 -->
                        <div id="once-options" style="display: none; margin-bottom: 15px;">
                            <div style="background: #e8f4fd; padding: 15px; border-radius: 8px;">
                                <div style="color: #0c5460; font-weight: bold; margin-bottom: 10px;">🔥 单次执行模式</div>
                                <div style="color: #0c5460; font-size: 14px; margin-bottom: 15px;">
                                    任务只执行一次，执行完成后自动禁用。<br>
                                    适用于：一次性脚本、临时任务、测试命令等。
                                </div>

                                <div style="margin-bottom: 10px;">
                                    <label style="font-size: 14px; color: #666;">执行时间</label>
                                    <select id="once-timing" onchange="updateOnceOptions()">
                                        <option value="immediate">立即执行</option>
                                        <option value="scheduled">指定时间执行</option>
                                    </select>
                                </div>

                                <div id="once-scheduled" style="display: none;">
                                    <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                                        <div>
                                            <label style="font-size: 14px; color: #666;">日期</label>
                                            <input type="date" id="once-date" style="width: 150px;" min="<?= date('Y-m-d') ?>">
                                        </div>
                                        <div>
                                            <label style="font-size: 14px; color: #666;">时间</label>
                                            <input type="time" id="once-time" style="width: 120px;" value="<?= date('H:i') ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 分钟设置 -->
                        <div id="minute-options" style="display: none; margin-bottom: 15px;">
                            <label style="font-size: 14px; color: #666;">每隔几分钟执行一次</label>
                            <select id="minute-interval">
                                <option value="1">每1分钟</option>
                                <option value="5">每5分钟</option>
                                <option value="10">每10分钟</option>
                                <option value="15">每15分钟</option>
                                <option value="30">每30分钟</option>
                            </select>
                        </div>

                        <!-- 小时设置 -->
                        <div id="hour-options" style="display: none; margin-bottom: 15px;">
                            <label style="font-size: 14px; color: #666;">每小时的第几分钟执行</label>
                            <select id="hour-minute">
                                <option value="0">整点执行（0分）</option>
                                <option value="15">15分</option>
                                <option value="30">30分</option>
                                <option value="45">45分</option>
                            </select>
                        </div>

                        <!-- 每天设置 -->
                        <div id="day-options" style="display: none; margin-bottom: 15px;">
                            <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                                <div>
                                    <label style="font-size: 14px; color: #666;">小时</label>
                                    <select id="day-hour">
                                        <?php for($h = 0; $h < 24; $h++): ?>
                                        <option value="<?= $h ?>" <?= $h == 2 ? 'selected' : '' ?>><?= sprintf('%02d', $h) ?>点</option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                                <div>
                                    <label style="font-size: 14px; color: #666;">分钟</label>
                                    <select id="day-minute">
                                        <?php for($m = 0; $m < 60; $m += 5): ?>
                                        <option value="<?= $m ?>" <?= $m == 0 ? 'selected' : '' ?>><?= sprintf('%02d', $m) ?>分</option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 每周设置 -->
                        <div id="week-options" style="display: none; margin-bottom: 15px;">
                            <div style="margin-bottom: 10px;">
                                <label style="font-size: 14px; color: #666;">星期几</label>
                                <select id="week-day">
                                    <option value="0">星期日</option>
                                    <option value="1" selected>星期一</option>
                                    <option value="2">星期二</option>
                                    <option value="3">星期三</option>
                                    <option value="4">星期四</option>
                                    <option value="5">星期五</option>
                                    <option value="6">星期六</option>
                                </select>
                            </div>
                            <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                                <div>
                                    <label style="font-size: 14px; color: #666;">小时</label>
                                    <select id="week-hour">
                                        <?php for($h = 0; $h < 24; $h++): ?>
                                        <option value="<?= $h ?>" <?= $h == 2 ? 'selected' : '' ?>><?= sprintf('%02d', $h) ?>点</option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                                <div>
                                    <label style="font-size: 14px; color: #666;">分钟</label>
                                    <select id="week-minute">
                                        <?php for($m = 0; $m < 60; $m += 5): ?>
                                        <option value="<?= $m ?>" <?= $m == 0 ? 'selected' : '' ?>><?= sprintf('%02d', $m) ?>分</option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 每月设置 -->
                        <div id="month-options" style="display: none; margin-bottom: 15px;">
                            <div style="margin-bottom: 10px;">
                                <label style="font-size: 14px; color: #666;">每月第几天</label>
                                <select id="month-day">
                                    <option value="1" selected>1号</option>
                                    <?php for($d = 2; $d <= 31; $d++): ?>
                                    <option value="<?= $d ?>"><?= $d ?>号</option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                            <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                                <div>
                                    <label style="font-size: 14px; color: #666;">小时</label>
                                    <select id="month-hour">
                                        <?php for($h = 0; $h < 24; $h++): ?>
                                        <option value="<?= $h ?>" <?= $h == 2 ? 'selected' : '' ?>><?= sprintf('%02d', $h) ?>点</option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                                <div>
                                    <label style="font-size: 14px; color: #666;">分钟</label>
                                    <select id="month-minute">
                                        <?php for($m = 0; $m < 60; $m += 5): ?>
                                        <option value="<?= $m ?>" <?= $m == 0 ? 'selected' : '' ?>><?= sprintf('%02d', $m) ?>分</option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 预览 -->
                        <div id="time-preview" style="background: #e8f4fd; padding: 10px; border-radius: 6px; margin-top: 10px; display: none;">
                            <strong>⏰ 执行时间预览：</strong>
                            <span id="preview-text"></span>
                        </div>

                        <input type="hidden" name="cron_expression" id="cron_expression" required>
                    </div>

                    <div style="text-align: center; margin-top: 25px;">
                        <button type="submit" class="quick-btn">✅ 创建任务</button>
                        <button type="button" class="quick-btn warning" onclick="hideAddForm()" style="margin-left: 10px;">❌ 取消</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 编辑任务表单 -->
        <div class="card" id="edit-form" style="display: none;">
            <div class="section-title">编辑任务</div>
            <div class="form-section">
                <form id="edit-task-form">
                    <input type="hidden" id="edit-task-id" name="task_id">

                    <div class="form-group">
                        <label>📝 任务名称</label>
                        <input type="text" name="name" id="edit-name" required>
                    </div>

                    <div class="form-group">
                        <label>💻 执行命令</label>
                        <textarea name="command" id="edit-command" required></textarea>
                    </div>

                    <div class="form-group">
                        <label>⏰ Cron表达式</label>
                        <input type="text" name="cron_expression" id="edit-cron" required placeholder="例如：0 2 * * *">
                        <small>如需修改执行时间，建议删除任务后重新创建</small>
                    </div>

                    <div style="text-align: center; margin-top: 25px;">
                        <button type="submit" class="quick-btn">✅ 保存修改</button>
                        <button type="button" class="quick-btn warning" onclick="hideEditForm()" style="margin-left: 10px;">❌ 取消</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="card" id="task-list">
            <div class="section-title">我的任务列表</div>
            <div class="task-list">
                <?php if (empty($tasks)): ?>
                <div class="empty-state">
                    <h3>🎯 还没有任务</h3>
                    <p>点击上面的"添加新任务"按钮来创建你的第一个定时任务吧！</p>
                </div>
                <?php else: ?>
                    <?php foreach ($tasks as $task): ?>
                    <div class="task-item">
                        <div class="task-header">
                            <div class="task-name">📋 <?= htmlspecialchars($task['name']) ?></div>
                            <div class="task-status status-<?= $task['status'] ?>">
                                <?= $task['status'] === 'enabled' ? '✅ 启用' : '⏸️ 禁用' ?>
                            </div>
                        </div>
                        <div class="task-details">
                            <div>💻 命令:
                                <?php
                                $command = $task['command'];
                                $shortCommand = htmlspecialchars(substr($command, 0, 80));
                                $fullCommand = htmlspecialchars($command);
                                $isLongCommand = strlen($command) > 80;
                                ?>
                                <span id="cmd-short-<?= intval($task['id']) ?>"><?= $shortCommand ?><?= $isLongCommand ? '...' : '' ?></span>
                                <?php if ($isLongCommand): ?>
                                <span id="cmd-full-<?= intval($task['id']) ?>" style="display: none;"><?= $fullCommand ?></span>
                                <button class="btn btn-primary" style="margin-left: 10px; padding: 2px 6px; font-size: 11px;"
                                        onclick="toggleCommand(<?= intval($task['id']) ?>)" id="cmd-btn-<?= intval($task['id']) ?>">
                                    📖 查看完整命令
                                </button>
                                <?php endif; ?>
                            </div>
                            <div>⏰ 执行时间: <?= htmlspecialchars($task['cron_expression']) ?></div>
                            <div>📊 执行次数: <?= $task['run_count'] ?> 次 | 上次执行: <?= $task['last_run'] ?: '从未执行' ?></div>
                        </div>
                        <div class="task-actions">
                            <button class="btn btn-success" onclick="runTask(<?= intval($task['id']) ?>)">
                                ▶️ 立即执行
                            </button>
                            <button class="btn btn-primary" onclick="editTask(<?= intval($task['id']) ?>)">
                                ✏️ 编辑
                            </button>
                            <button class="btn btn-warning" onclick="toggleTask(<?= intval($task['id']) ?>)">
                                <?= $task['status'] === 'enabled' ? '⏸️ 禁用' : '▶️ 启用' ?>
                            </button>
                            <button class="btn btn-danger" onclick="deleteTask(<?= intval($task['id']) ?>)">
                                🗑️ 删除
                            </button>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- 执行日志 -->
        <div class="card" id="logs-section" style="display: none;">
            <div class="section-title">执行日志</div>

            <!-- 日志操作按钮 -->
            <div style="margin-bottom: 20px; display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-primary" onclick="toggleSelectAll()" id="selectAllBtn">
                    ☑️ 全选
                </button>
                <button class="btn btn-danger" onclick="deleteSelectedLogs()">
                    🗑️ 删除选中
                </button>
                <button class="btn btn-danger" onclick="clearAllLogs()" style="margin-left: auto;">
                    🗑️ 清空所有日志
                </button>
                <button class="btn btn-primary" onclick="hideLogs()">
                    🔙 返回任务列表
                </button>
            </div>

            <div class="task-list">
                <?php if (empty($logs)): ?>
                <div class="empty-state">
                    <h3>📝 暂无执行记录</h3>
                    <p>任务执行后会在这里显示详细的执行日志</p>
                </div>
                <?php else: ?>
                    <?php foreach ($logs as $log): ?>
                    <div class="log-card" id="log-item-<?= intval($log['id']) ?>">
                        <!-- 顶部选择和操作区域 -->
                        <div class="log-header">
                            <div class="log-select">
                                <input type="checkbox" class="log-checkbox" value="<?= intval($log['id']) ?>" id="check-<?= intval($log['id']) ?>">
                                <label for="check-<?= intval($log['id']) ?>" style="margin-left: 8px; cursor: pointer;">选择</label>
                            </div>
                            <div class="log-actions">
                                <span class="log-status <?= $log['status'] === 'success' ? 'status-success' : 'status-error' ?>">
                                    <?= $log['status'] === 'success' ? '✅ 成功' : '❌ 失败' ?>
                                </span>
                                <button class="btn-delete" onclick="deleteLog(<?= intval($log['id']) ?>)" title="删除此日志">
                                    🗑️ 删除
                                </button>
                            </div>
                        </div>

                        <!-- 任务信息区域 -->
                        <div class="log-content">
                            <div class="log-task-name">
                                📋 <?= htmlspecialchars($log['task_name'] ?: '未知任务') ?>
                            </div>
                            <div class="log-details">
                                <div class="log-time">⏰ <?= $log['start_time'] ?> (耗时: <?= $log['duration'] ?>秒)</div>
                                <div class="log-output">
                                    <strong>📄 执行输出:</strong>
                                    <?php
                                    $output = $log['output'] ?: $log['error_message'] ?: '无输出';
                                    $shortOutput = htmlspecialchars(substr($output, 0, 150));
                                    $fullOutput = htmlspecialchars($output);
                                    $isLong = strlen($output) > 150;
                                    ?>
                                    <div class="output-content">
                                        <span id="short-<?= intval($log['id']) ?>"><?= $shortOutput ?><?= $isLong ? '...' : '' ?></span>
                                        <?php if ($isLong): ?>
                                        <span id="full-<?= intval($log['id']) ?>" style="display: none;"><?= $fullOutput ?></span>
                                        <br>
                                        <button class="btn-toggle" onclick="toggleOutput(<?= intval($log['id']) ?>)" id="btn-<?= intval($log['id']) ?>">
                                            📖 查看完整输出
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button class="btn btn-primary" onclick="hideLogs()">🔙 返回任务列表</button>
            </div>
        </div>

        <!-- 帮助信息 -->
        <div class="card" id="help-section" style="display: none;">
            <div class="section-title">使用帮助</div>
            <div class="form-section">
                <h3>🚀 快速开始</h3>
                <ol style="line-height: 1.8; margin-bottom: 20px;">
                    <li><strong>添加任务：</strong> 点击"➕ 添加新任务"按钮</li>
                    <li><strong>选择类型：</strong> 从20多种预设任务类型中选择，系统自动填写命令</li>
                    <li><strong>设置时间：</strong> 选择执行频率和具体时间</li>
                    <li><strong>创建完成：</strong> 点击"✅ 创建任务"完成设置</li>
                </ol>

                <h3>📅 执行时间选项</h3>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <div style="margin-bottom: 10px;"><strong>🔥 单次执行：</strong></div>
                    <ul style="margin-left: 20px; margin-bottom: 15px;">
                        <li><strong>立即执行：</strong> 创建后立即执行一次，然后自动禁用</li>
                        <li><strong>指定时间：</strong> 选择具体日期和时间执行，执行后自动禁用</li>
                    </ul>

                    <div style="margin-bottom: 10px;"><strong>🔄 定时执行：</strong></div>
                    <ul style="margin-left: 20px;">
                        <li><strong>每分钟：</strong> 可选择1/5/10/15/30分钟间隔</li>
                        <li><strong>每小时：</strong> 可选择在第几分钟执行</li>
                        <li><strong>每天：</strong> 可选择具体的小时和分钟</li>
                        <li><strong>每周：</strong> 可选择星期几和具体时间</li>
                        <li><strong>每月：</strong> 可选择几号和具体时间</li>
                    </ul>
                </div>

                <h3>🛠️ 任务管理功能</h3>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <ul style="line-height: 1.8;">
                        <li><strong>▶️ 立即执行：</strong> 手动触发任务执行，查看实时结果</li>
                        <li><strong>✏️ 编辑任务：</strong> 修改任务名称、命令或执行时间</li>
                        <li><strong>⏸️ 启用/禁用：</strong> 临时停止或恢复任务执行</li>
                        <li><strong>🗑️ 删除任务：</strong> 永久删除不需要的任务</li>
                        <li><strong>📖 查看详情：</strong> 展开查看完整命令和执行输出</li>
                    </ul>
                </div>

                <h3>📊 执行日志</h3>
                <p style="margin-bottom: 10px;">系统自动记录每次任务执行的详细信息：</p>
                <ul style="line-height: 1.8; margin-bottom: 20px;">
                    <li><strong>执行状态：</strong> 成功/失败状态显示</li>
                    <li><strong>执行时间：</strong> 开始时间和执行耗时</li>
                    <li><strong>输出内容：</strong> 命令执行的完整输出结果</li>
                    <li><strong>错误信息：</strong> 失败时的详细错误描述</li>
                </ul>

                <h3>⚙️ 自动执行设置</h3>
                <p style="margin-bottom: 10px;">要让定时任务自动执行，需要设置系统Cron：</p>
                <div style="background: #f0f0f0; padding: 15px; border-radius: 8px; font-family: monospace; margin-bottom: 20px;">
                    # 编辑系统crontab<br>
                    crontab -e<br><br>
                    # 添加这一行（每分钟检查一次）：<br>
                    * * * * * /usr/bin/php <?= __FILE__ ?> cron >/dev/null 2>&1
                </div>

                <h3>🎯 预设任务类型</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-bottom: 20px;">
                    <div>
                        <strong>📁 文件操作：</strong>
                        <ul style="font-size: 14px; margin-top: 5px;">
                            <li>文件备份</li>
                            <li>清理临时文件</li>
                            <li>文件同步</li>
                        </ul>
                    </div>
                    <div>
                        <strong>💻 脚本执行：</strong>
                        <ul style="font-size: 14px; margin-top: 5px;">
                            <li>PHP脚本</li>
                            <li>Python脚本</li>
                            <li>Shell脚本</li>
                            <li>Node.js脚本</li>
                        </ul>
                    </div>
                    <div>
                        <strong>🗄️ 数据库操作：</strong>
                        <ul style="font-size: 14px; margin-top: 5px;">
                            <li>MySQL备份</li>
                            <li>数据库优化</li>
                        </ul>
                    </div>
                    <div>
                        <strong>🔍 系统监控：</strong>
                        <ul style="font-size: 14px; margin-top: 5px;">
                            <li>系统状态检查</li>
                            <li>磁盘空间检查</li>
                            <li>日志检查</li>
                            <li>服务状态检查</li>
                        </ul>
                    </div>
                    <div>
                        <strong>🌐 网络操作：</strong>
                        <ul style="font-size: 14px; margin-top: 5px;">
                            <li>HTTP请求</li>
                            <li>网络连通性测试</li>
                            <li>文件下载</li>
                        </ul>
                    </div>
                </div>

                <h3>💡 使用技巧</h3>
                <div style="background: #e8f4fd; padding: 15px; border-radius: 8px; color: #0c5460;">
                    <ul style="line-height: 1.8;">
                        <li><strong>测试命令：</strong> 先创建单次执行任务测试命令是否正确</li>
                        <li><strong>查看日志：</strong> 定期查看执行日志，确保任务正常运行</li>
                        <li><strong>路径修改：</strong> 预设命令中的路径需要根据实际情况修改</li>
                        <li><strong>权限检查：</strong> 确保执行用户有足够权限执行相关命令</li>
                        <li><strong>备份重要：</strong> 涉及删除或修改的命令要特别小心</li>
                    </ul>
                </div>

                <h3>🔧 故障排除</h3>
                <p style="margin-bottom: 10px;">如果遇到问题，可以：</p>
                <ul style="line-height: 1.8;">
                    <li>访问 <a href="debug.php" style="color: #007bff;">调试工具</a> 检查系统状态</li>
                    <li>查看 <a href="setup_guide.php" style="color: #007bff;">设置指南</a> 了解环境配置</li>
                    <li>使用"立即执行"功能测试任务</li>
                    <li>检查执行日志中的错误信息</li>
                </ul>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button class="btn btn-primary" onclick="hideHelp()">🔙 返回任务列表</button>
            </div>
        </div>
    </div>

    <script>
        // 显示/隐藏添加表单
        function showAddForm() {
            document.getElementById('add-form').style.display = 'block';
            document.getElementById('task-list').style.display = 'block';
            document.getElementById('logs-section').style.display = 'none';
            document.getElementById('help-section').style.display = 'none';
            document.getElementById('add-form').scrollIntoView({ behavior: 'smooth' });
        }

        function hideAddForm() {
            document.getElementById('add-form').style.display = 'none';
        }

        // 显示/隐藏日志
        function showLogs() {
            document.getElementById('add-form').style.display = 'none';
            document.getElementById('task-list').style.display = 'none';
            document.getElementById('logs-section').style.display = 'block';
            document.getElementById('help-section').style.display = 'none';
            document.getElementById('logs-section').scrollIntoView({ behavior: 'smooth' });
        }

        function hideLogs() {
            document.getElementById('logs-section').style.display = 'none';
            document.getElementById('task-list').style.display = 'block';
        }

        // 日志删除功能
        function deleteLog(logId) {
            if (confirm('确定要删除这条日志吗？')) {
                fetch('', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=delete_log&log_id=${logId}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById(`log-item-${logId}`).remove();
                        showMessage(data.message, 'success');
                    } else {
                        showMessage(data.message, 'error');
                    }
                })
                .catch(error => {
                    showMessage('删除失败: ' + error.message, 'error');
                });
            }
        }

        // 智能全选/取消全选切换
        function toggleSelectAll() {
            const checkboxes = document.querySelectorAll('.log-checkbox');
            const selectAllBtn = document.getElementById('selectAllBtn');

            // 检查当前是否全部选中
            const checkedCount = document.querySelectorAll('.log-checkbox:checked').length;
            const totalCount = checkboxes.length;

            if (checkedCount === totalCount) {
                // 当前全选状态，执行取消全选
                checkboxes.forEach(checkbox => checkbox.checked = false);
                selectAllBtn.innerHTML = '☑️ 全选';
            } else {
                // 当前非全选状态，执行全选
                checkboxes.forEach(checkbox => checkbox.checked = true);
                selectAllBtn.innerHTML = '⬜ 取消全选';
            }
        }

        // 更新全选按钮状态（在页面加载和复选框变化时调用）
        function updateSelectAllButton() {
            const checkboxes = document.querySelectorAll('.log-checkbox');
            const selectAllBtn = document.getElementById('selectAllBtn');

            if (!selectAllBtn) return;

            const checkedCount = document.querySelectorAll('.log-checkbox:checked').length;
            const totalCount = checkboxes.length;

            if (checkedCount === totalCount && totalCount > 0) {
                selectAllBtn.innerHTML = '⬜ 取消全选';
            } else {
                selectAllBtn.innerHTML = '☑️ 全选';
            }
        }

        // 删除选中的日志
        function deleteSelectedLogs() {
            const checkboxes = document.querySelectorAll('.log-checkbox:checked');
            if (checkboxes.length === 0) {
                showMessage('请选择要删除的日志', 'warning');
                return;
            }

            if (confirm(`确定要删除选中的 ${checkboxes.length} 条日志吗？`)) {
                const logIds = Array.from(checkboxes).map(cb => cb.value);

                const formData = new FormData();
                formData.append('action', 'delete_logs_batch');
                formData.append('log_ids', JSON.stringify(logIds));

                fetch('', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 移除已删除的日志项
                        logIds.forEach(id => {
                            const element = document.getElementById(`log-item-${id}`);
                            if (element) element.remove();
                        });
                        showMessage(data.message, 'success');
                    } else {
                        showMessage(data.message, 'error');
                    }
                })
                .catch(error => {
                    showMessage('批量删除失败: ' + error.message, 'error');
                });
            }
        }

        // 清空所有日志
        function clearAllLogs() {
            if (confirm('确定要清空所有执行日志吗？此操作不可恢复！')) {
                fetch('', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=clear_all_logs'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 刷新页面以显示空状态
                        location.reload();
                    } else {
                        showMessage(data.message, 'error');
                    }
                })
                .catch(error => {
                    showMessage('清空失败: ' + error.message, 'error');
                });
            }
        }

        // 显示/隐藏帮助
        function showHelp() {
            document.getElementById('add-form').style.display = 'none';
            document.getElementById('task-list').style.display = 'none';
            document.getElementById('logs-section').style.display = 'none';
            document.getElementById('help-section').style.display = 'block';
            document.getElementById('help-section').scrollIntoView({ behavior: 'smooth' });
        }

        function hideHelp() {
            document.getElementById('help-section').style.display = 'none';
            document.getElementById('task-list').style.display = 'block';
        }

        // 显示/隐藏编辑表单
        function showEditForm() {
            document.getElementById('add-form').style.display = 'none';
            document.getElementById('task-list').style.display = 'block';
            document.getElementById('logs-section').style.display = 'none';
            document.getElementById('help-section').style.display = 'none';
            document.getElementById('edit-form').style.display = 'block';
            document.getElementById('edit-form').scrollIntoView({ behavior: 'smooth' });
        }

        function hideEditForm() {
            document.getElementById('edit-form').style.display = 'none';
        }

        // 更新单次执行选项
        function updateOnceOptions() {
            const timing = document.getElementById('once-timing').value;
            const scheduledDiv = document.getElementById('once-scheduled');

            if (timing === 'scheduled') {
                scheduledDiv.style.display = 'block';
                // 设置默认日期为今天
                document.getElementById('once-date').value = new Date().toISOString().split('T')[0];
            } else {
                scheduledDiv.style.display = 'none';
            }

            updateCronExpression();
        }

        // 更新命令根据任务类型
        function updateCommand() {
            const taskType = document.getElementById('task-type').value;
            const commandField = document.getElementById('command');

            const commands = {
                // 文件操作
                'backup': 'tar -czf /backup/backup_$(date +%Y%m%d_%H%M%S).tar.gz /home',
                'cleanup': 'find /tmp -name "*.tmp" -mtime +1 -delete && find /var/log -name "*.log" -size +100M -delete',
                'sync': 'rsync -av /source/ /destination/',

                // 脚本执行
                'php': '/usr/bin/php /path/to/your/script.php',
                'python': '/usr/bin/python3 /path/to/your/script.py',
                'shell': '/bin/bash /path/to/your/script.sh',
                'node': '/usr/bin/node /path/to/your/script.js',

                // 数据库操作
                'mysql_backup': 'mysqldump -u root -p\'password\' database_name > /backup/db_$(date +%Y%m%d_%H%M%S).sql',
                'mysql_optimize': 'mysqlcheck -u root -p\'password\' --optimize --all-databases',

                // 系统监控
                'check': 'df -h && free -m && uptime',
                'disk_check': 'df -h | awk \'$5 > 80 {print $0}\'',
                'log_check': 'tail -n 100 /var/log/syslog | grep -i error',
                'service_check': 'systemctl status nginx mysql php-fpm',

                // 网络操作
                'curl': 'curl -s -o /dev/null -w "%{http_code}" https://example.com',
                'ping': 'ping -c 4 google.com',
                'wget': 'wget -O /tmp/file.zip https://example.com/file.zip',

                // 自定义
                'custom': ''
            };

            if (commands[taskType] !== undefined) {
                commandField.value = commands[taskType];
                if (taskType === 'custom') {
                    commandField.placeholder = '请输入自定义命令，例如：ls -la /home';
                } else {
                    commandField.placeholder = '系统已自动填写命令，你可以根据需要修改路径和参数';
                }
            }
        }

        // 更新时间选项显示
        function updateTimeOptions() {
            const frequency = document.getElementById('frequency').value;

            // 隐藏所有选项
            document.getElementById('once-options').style.display = 'none';
            document.getElementById('minute-options').style.display = 'none';
            document.getElementById('hour-options').style.display = 'none';
            document.getElementById('day-options').style.display = 'none';
            document.getElementById('week-options').style.display = 'none';
            document.getElementById('month-options').style.display = 'none';
            document.getElementById('time-preview').style.display = 'none';

            // 显示对应的选项
            if (frequency) {
                document.getElementById(frequency + '-options').style.display = 'block';
                updateCronExpression();
            }
        }

        // 更新Cron表达式
        function updateCronExpression() {
            const frequency = document.getElementById('frequency').value;
            let cronExpression = '';
            let previewText = '';

            switch (frequency) {
                case 'once':
                    const timing = document.getElementById('once-timing').value;
                    if (timing === 'immediate') {
                        cronExpression = '@once';
                        previewText = '创建后立即执行一次，然后自动禁用';
                    } else {
                        const date = document.getElementById('once-date').value;
                        const time = document.getElementById('once-time').value;
                        if (date && time) {
                            const [year, month, day] = date.split('-');
                            const [hour, minute] = time.split(':');
                            cronExpression = `@once:${year}-${month}-${day} ${hour}:${minute}`;
                            previewText = `将在 ${year}年${month}月${day}日 ${hour}:${minute} 执行一次，然后自动禁用`;
                        } else {
                            cronExpression = '@once';
                            previewText = '请选择执行日期和时间';
                        }
                    }
                    break;

                case 'minute':
                    const interval = document.getElementById('minute-interval').value;
                    cronExpression = `*/${interval} * * * *`;
                    previewText = `每${interval}分钟执行一次`;
                    break;

                case 'hour':
                    const hourMinute = document.getElementById('hour-minute').value;
                    cronExpression = `${hourMinute} * * * *`;
                    previewText = `每小时的第${hourMinute}分钟执行`;
                    break;

                case 'day':
                    const dayHour = document.getElementById('day-hour').value;
                    const dayMinute = document.getElementById('day-minute').value;
                    cronExpression = `${dayMinute} ${dayHour} * * *`;
                    previewText = `每天${String(dayHour).padStart(2, '0')}:${String(dayMinute).padStart(2, '0')}执行`;
                    break;

                case 'week':
                    const weekDay = document.getElementById('week-day').value;
                    const weekHour = document.getElementById('week-hour').value;
                    const weekMinute = document.getElementById('week-minute').value;
                    cronExpression = `${weekMinute} ${weekHour} * * ${weekDay}`;
                    const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
                    previewText = `每${weekDays[weekDay]} ${String(weekHour).padStart(2, '0')}:${String(weekMinute).padStart(2, '0')}执行`;
                    break;

                case 'month':
                    const monthDay = document.getElementById('month-day').value;
                    const monthHour = document.getElementById('month-hour').value;
                    const monthMinute = document.getElementById('month-minute').value;
                    cronExpression = `${monthMinute} ${monthHour} ${monthDay} * *`;
                    previewText = `每月${monthDay}号 ${String(monthHour).padStart(2, '0')}:${String(monthMinute).padStart(2, '0')}执行`;
                    break;
            }

            if (cronExpression) {
                document.getElementById('cron_expression').value = cronExpression;
                document.getElementById('preview-text').textContent = previewText;
                document.getElementById('time-preview').style.display = 'block';
            }
        }

        // 系统时钟功能
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const dateString = now.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });

            const clockElement = document.getElementById('current-time');
            if (clockElement) {
                clockElement.innerHTML = `${dateString}<br>${timeString}`;
            }
        }

        // 启动时钟
        function startClock() {
            updateClock(); // 立即更新一次
            setInterval(updateClock, 1000); // 每秒更新
        }

        // 为所有时间选择器添加事件监听
        document.addEventListener('DOMContentLoaded', function() {
            // 启动系统时钟
            startClock();
            // 单次执行设置
            document.getElementById('once-timing').addEventListener('change', updateOnceOptions);
            document.getElementById('once-date').addEventListener('change', updateCronExpression);
            document.getElementById('once-time').addEventListener('change', updateCronExpression);

            // 分钟间隔
            document.getElementById('minute-interval').addEventListener('change', updateCronExpression);

            // 小时设置
            document.getElementById('hour-minute').addEventListener('change', updateCronExpression);

            // 每天设置
            document.getElementById('day-hour').addEventListener('change', updateCronExpression);
            document.getElementById('day-minute').addEventListener('change', updateCronExpression);

            // 每周设置
            document.getElementById('week-day').addEventListener('change', updateCronExpression);
            document.getElementById('week-hour').addEventListener('change', updateCronExpression);
            document.getElementById('week-minute').addEventListener('change', updateCronExpression);

            // 每月设置
            document.getElementById('month-day').addEventListener('change', updateCronExpression);
            document.getElementById('month-hour').addEventListener('change', updateCronExpression);
            document.getElementById('month-minute').addEventListener('change', updateCronExpression);

            // 日志复选框事件监听
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('log-checkbox')) {
                    updateSelectAllButton();
                }
            });

            // 初始化全选按钮状态
            updateSelectAllButton();
        });

        // 显示消息
        function showMessage(message, type = 'success') {
            const alertElement = document.getElementById(type === 'success' ? 'success-alert' : 'error-alert');
            alertElement.textContent = message;
            alertElement.style.display = 'block';

            // 滚动到消息位置
            alertElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

            setTimeout(() => {
                alertElement.style.display = 'none';
            }, 5000);
        }

        // AJAX请求
        function sendRequest(data) {
            return fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams(data)
            }).then(response => response.json());
        }

        // 添加任务
        document.getElementById('task-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            formData.append('action', 'add_task');

            // 显示加载状态
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '⏳ 创建中...';
            submitBtn.disabled = true;

            sendRequest(formData).then(result => {
                if (result.success) {
                    showMessage('🎉 任务创建成功！');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showMessage('❌ ' + result.message, 'error');
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                }
            }).catch(error => {
                showMessage('❌ 网络错误，请重试', 'error');
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });

        // 编辑任务
        document.getElementById('edit-task-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            formData.append('action', 'edit_task');

            // 显示加载状态
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '⏳ 保存中...';
            submitBtn.disabled = true;

            sendRequest(formData).then(result => {
                if (result.success) {
                    showMessage('🎉 任务修改成功！');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showMessage('❌ ' + result.message, 'error');
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                }
            }).catch(error => {
                showMessage('❌ 网络错误，请重试', 'error');
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });

        // 删除任务
        function deleteTask(id) {
            if (confirm('🗑️ 确定要删除这个任务吗？\n删除后无法恢复！')) {
                sendRequest({action: 'delete_task', id: id}).then(result => {
                    if (result.success) {
                        showMessage('🗑️ 任务删除成功！');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showMessage('❌ ' + result.message, 'error');
                    }
                });
            }
        }

        // 切换任务状态
        function toggleTask(id) {
            sendRequest({action: 'toggle_task', id: id}).then(result => {
                if (result.success) {
                    showMessage('✅ 任务状态更新成功！');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showMessage('❌ ' + result.message, 'error');
                }
            });
        }

        // 执行任务
        function runTask(id) {
            if (confirm('▶️ 确定要立即执行这个任务吗？')) {
                showMessage('⏳ 任务执行中，请稍候...');

                sendRequest({action: 'run_task', id: id}).then(result => {
                    if (result.success) {
                        const output = result.output ? result.output.substring(0, 200) : '无输出';
                        showMessage('🎉 任务执行成功！\n输出: ' + output);
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        showMessage('❌ 任务执行失败: ' + result.message, 'error');
                    }
                });
            }
        }

        // 编辑任务
        function editTask(id) {
            // 获取任务数据
            sendRequest({action: 'get_task', id: id}).then(result => {
                if (result.success) {
                    const task = result.task;

                    // 填充编辑表单
                    document.getElementById('edit-task-id').value = task.id;
                    document.getElementById('edit-name').value = task.name;
                    document.getElementById('edit-command').value = task.command;
                    document.getElementById('edit-cron').value = task.cron_expression;

                    // 显示编辑表单
                    showEditForm();
                } else {
                    showMessage('❌ ' + result.message, 'error');
                }
            });
        }

        // 执行所有启用的任务
        function runAllTasks() {
            if (confirm('▶️ 确定要执行所有启用的任务吗？')) {
                showMessage('⏳ 正在执行所有任务，请稍候...');
                // 这里可以添加执行所有任务的逻辑
                setTimeout(() => {
                    showMessage('🎉 所有任务执行完成！');
                    location.reload();
                }, 3000);
            }
        }

        // 切换输出显示
        function toggleOutput(logId) {
            const shortElement = document.getElementById('short-' + logId);
            const fullElement = document.getElementById('full-' + logId);
            const btnElement = document.getElementById('btn-' + logId);

            if (fullElement.style.display === 'none') {
                // 显示完整输出
                shortElement.style.display = 'none';
                fullElement.style.display = 'inline';
                btnElement.textContent = '📖 收起输出';
            } else {
                // 显示简短输出
                shortElement.style.display = 'inline';
                fullElement.style.display = 'none';
                btnElement.textContent = '📖 查看完整输出';
            }
        }

        // 切换命令显示
        function toggleCommand(taskId) {
            const shortElement = document.getElementById('cmd-short-' + taskId);
            const fullElement = document.getElementById('cmd-full-' + taskId);
            const btnElement = document.getElementById('cmd-btn-' + taskId);

            if (fullElement.style.display === 'none') {
                // 显示完整命令
                shortElement.style.display = 'none';
                fullElement.style.display = 'inline';
                btnElement.textContent = '📖 收起命令';
            } else {
                // 显示简短命令
                shortElement.style.display = 'inline';
                fullElement.style.display = 'none';
                btnElement.textContent = '📖 查看完整命令';
            }
        }
    </script>
</body>
</html>
