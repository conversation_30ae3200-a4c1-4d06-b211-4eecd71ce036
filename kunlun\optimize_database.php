<?php
/**
 * 数据库优化脚本
 * 创建时间: 2025-08-12
 * 功能: 清理image_base64数据以节省存储空间，同时保持系统功能正常
 */

require_once '../conn_waf.php';

$APP_ID = 35; // 预警系统应用ID

// 检查用户权限
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.html');
    exit;
}

// 检查应用权限
function isHasPerm($APP_ID) {
    global $conn;
    
    if (!isset($_SESSION['user_id'])) {
        return false;
    }
    
    // 系统管理员拥有所有权限
    if (isAdmin()) {
        return true;
    }
    
    // 检查应用管理员权限
    if (isAppAdmin($APP_ID)) {
        return true;
    }
    
    return false;
}

if (!isHasPerm($APP_ID)) {
    $errorMessage = urlencode('您没有访问数据库优化功能的权限。');
    header("Location: ../permission_error.html?type=permission&message=" . $errorMessage);
    exit;
}

$user_id = $_SESSION['user_id'];

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'analyzeStorage':
            analyzeStorageUsage();
            break;
        case 'clearBase64Data':
            clearBase64Data();
            break;
        case 'optimizeTables':
            optimizeTables();
            break;
        case 'checkTableStructure':
            checkTableStructure();
            break;
        default:
            echo json_encode(['status' => 0, 'message' => '无效的操作']);
    }
    exit;
}

/**
 * 分析存储使用情况
 */
function analyzeStorageUsage() {
    global $conn;
    
    try {
        $analysis = [];
        
        // 检查表大小
        $sql = "SELECT 
                    table_name,
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'size_mb',
                    table_rows
                FROM information_schema.TABLES 
                WHERE table_schema = DATABASE() 
                AND table_name LIKE 'Kunlun_%'
                ORDER BY (data_length + index_length) DESC";
        
        $result = $conn->query($sql);
        $tables = [];
        while ($row = $result->fetch_assoc()) {
            $tables[] = $row;
        }
        $analysis['tables'] = $tables;
        
        // 检查image_base64字段使用情况
        $sql = "SELECT 
                    COUNT(*) as total_records,
                    COUNT(image_base64) as records_with_base64,
                    COUNT(image_url) as records_with_url,
                    AVG(LENGTH(image_base64)) as avg_base64_size
                FROM Kunlun_alarm_records";
        
        $result = $conn->query($sql);
        $imageStats = $result->fetch_assoc();
        $analysis['image_stats'] = $imageStats;
        
        // 估算可节省的空间
        if ($imageStats['records_with_base64'] > 0) {
            $estimatedSavings = ($imageStats['avg_base64_size'] * $imageStats['records_with_base64']) / 1024 / 1024;
            $analysis['estimated_savings_mb'] = round($estimatedSavings, 2);
        } else {
            $analysis['estimated_savings_mb'] = 0;
        }
        
        // 检查最近的记录
        $sql = "SELECT 
                    DATE(created_time) as date,
                    COUNT(*) as count,
                    COUNT(image_base64) as with_base64
                FROM Kunlun_alarm_records 
                WHERE created_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY DATE(created_time)
                ORDER BY date DESC";
        
        $result = $conn->query($sql);
        $recentStats = [];
        while ($row = $result->fetch_assoc()) {
            $recentStats[] = $row;
        }
        $analysis['recent_stats'] = $recentStats;
        
        echo json_encode([
            'status' => 1,
            'message' => '存储分析完成',
            'data' => $analysis
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'status' => 0,
            'message' => '存储分析失败: ' . $e->getMessage()
        ]);
    }
}

/**
 * 清理Base64数据
 */
function clearBase64Data() {
    global $conn, $user_id;
    
    $keepDays = intval($_POST['keepDays'] ?? 7); // 保留最近几天的数据
    
    try {
        $conn->autocommit(false);
        
        // 统计要清理的数据
        $sql = "SELECT COUNT(*) as count 
                FROM Kunlun_alarm_records 
                WHERE image_base64 IS NOT NULL 
                AND created_time < DATE_SUB(NOW(), INTERVAL ? DAY)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $keepDays);
        $stmt->execute();
        $result = $stmt->get_result();
        $countToClear = $result->fetch_assoc()['count'];
        
        if ($countToClear == 0) {
            echo json_encode([
                'status' => 1,
                'message' => '没有需要清理的Base64数据',
                'data' => ['cleared' => 0]
            ]);
            return;
        }
        
        // 清理Base64数据（保留image_url）
        $sql = "UPDATE Kunlun_alarm_records 
                SET image_base64 = NULL 
                WHERE image_base64 IS NOT NULL 
                AND created_time < DATE_SUB(NOW(), INTERVAL ? DAY)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $keepDays);
        $stmt->execute();
        $clearedCount = $stmt->affected_rows;
        
        $conn->commit();
        
        // 记录操作日志
        global $APP_ID;
        logOperation($conn, $user_id, $APP_ID, "清理Base64图片数据，清理记录数: $clearedCount");
        
        echo json_encode([
            'status' => 1,
            'message' => "Base64数据清理完成，共清理 $clearedCount 条记录",
            'data' => [
                'cleared' => $clearedCount,
                'estimated' => $countToClear
            ]
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        echo json_encode([
            'status' => 0,
            'message' => 'Base64数据清理失败: ' . $e->getMessage()
        ]);
    } finally {
        $conn->autocommit(true);
    }
}

/**
 * 优化表结构
 */
function optimizeTables() {
    global $conn, $user_id;
    
    try {
        $results = [];
        
        // 获取所有Kunlun表
        $sql = "SHOW TABLES LIKE 'Kunlun_%'";
        $result = $conn->query($sql);
        
        while ($row = $result->fetch_array()) {
            $tableName = $row[0];
            
            // 优化表
            $optimizeSql = "OPTIMIZE TABLE `$tableName`";
            $optimizeResult = $conn->query($optimizeSql);
            
            if ($optimizeResult) {
                $results[$tableName] = 'success';
            } else {
                $results[$tableName] = 'failed: ' . $conn->error;
            }
        }
        
        // 记录操作日志
        global $APP_ID;
        logOperation($conn, $user_id, $APP_ID, "执行表优化操作");
        
        echo json_encode([
            'status' => 1,
            'message' => '表优化完成',
            'data' => $results
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'status' => 0,
            'message' => '表优化失败: ' . $e->getMessage()
        ]);
    }
}

/**
 * 检查表结构
 */
function checkTableStructure() {
    global $conn;
    
    try {
        $tables = ['Kunlun_alarm_records', 'Kunlun_sms_alert_config', 'Kunlun_sms_send_log'];
        $structure = [];
        
        foreach ($tables as $table) {
            $sql = "DESCRIBE `$table`";
            $result = $conn->query($sql);
            
            if ($result) {
                $columns = [];
                while ($row = $result->fetch_assoc()) {
                    $columns[] = $row;
                }
                $structure[$table] = $columns;
            } else {
                $structure[$table] = ['error' => $conn->error];
            }
        }
        
        echo json_encode([
            'status' => 1,
            'message' => '表结构检查完成',
            'data' => $structure
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'status' => 0,
            'message' => '表结构检查失败: ' . $e->getMessage()
        ]);
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库优化工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card h3 {
            margin: 0 0 1rem 0;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            margin: 0.25rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        
        .btn:hover { opacity: 0.8; }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        
        .result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .result-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .result-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 4px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #3498db;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .warning-box {
            background: #fff3cd;
            color: #856404;
            padding: 1rem;
            border-radius: 4px;
            border: 1px solid #ffeaa7;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛠️ 数据库优化工具</h1>
            <p>优化数据库存储，提升系统性能</p>
        </div>
        
        <div class="card">
            <h3>📊 存储分析</h3>
            <p>分析当前数据库存储使用情况</p>
            <button class="btn btn-primary" onclick="analyzeStorage()">开始分析</button>
            <div id="analysisResult"></div>
        </div>
        
        <div class="card">
            <h3>🧹 清理Base64数据</h3>
            <div class="warning-box">
                <strong>注意：</strong>此操作将清理数据库中的Base64图片数据以节省存储空间。
                前端仍将通过kafka.php实时获取Base64图片，不影响显示功能。
            </div>
            <div class="form-group">
                <label>保留最近几天的数据:</label>
                <input type="number" class="form-control" id="keepDays" value="7" min="1" max="30">
                <small>建议保留最近7天的数据，以确保最新预警的图片正常显示</small>
            </div>
            <button class="btn btn-warning" onclick="clearBase64Data()">清理Base64数据</button>
            <div id="clearResult"></div>
        </div>
        
        <div class="card">
            <h3>⚡ 表优化</h3>
            <p>优化数据库表结构，回收空间并提升性能</p>
            <button class="btn btn-success" onclick="optimizeTables()">优化表</button>
            <div id="optimizeResult"></div>
        </div>
        
        <div class="card">
            <h3>🔍 表结构检查</h3>
            <p>检查数据库表结构是否正确</p>
            <button class="btn btn-primary" onclick="checkTableStructure()">检查表结构</button>
            <div id="structureResult"></div>
        </div>
    </div>
    
    <script>
        // 分析存储使用情况
        async function analyzeStorage() {
            await executeAction('analyzeStorage', 'analysisResult', '存储分析');
        }
        
        // 清理Base64数据
        async function clearBase64Data() {
            const keepDays = document.getElementById('keepDays').value;
            
            if (!confirm(`确定要清理 ${keepDays} 天前的Base64图片数据吗？此操作不可逆。`)) {
                return;
            }
            
            await executeAction('clearBase64Data', 'clearResult', 'Base64数据清理', { keepDays });
        }
        
        // 优化表
        async function optimizeTables() {
            if (!confirm('确定要优化数据库表吗？此操作可能需要一些时间。')) {
                return;
            }
            
            await executeAction('optimizeTables', 'optimizeResult', '表优化');
        }
        
        // 检查表结构
        async function checkTableStructure() {
            await executeAction('checkTableStructure', 'structureResult', '表结构检查');
        }
        
        // 执行操作
        async function executeAction(action, resultId, description, extraData = {}) {
            const resultDiv = document.getElementById(resultId);
            showLoading(resultDiv, `正在执行${description}...`);
            
            try {
                const formData = new FormData();
                formData.append('action', action);
                
                for (const [key, value] of Object.entries(extraData)) {
                    formData.append(key, value);
                }
                
                const response = await fetch('optimize_database.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.status === 1) {
                    showResult(resultId, 'success', `${description}成功:\n${data.message}\n\n详细结果:\n${JSON.stringify(data.data, null, 2)}`);
                } else {
                    showResult(resultId, 'error', `${description}失败:\n${data.message}`);
                }
            } catch (error) {
                showResult(resultId, 'error', `${description}异常:\n${error.message}`);
            }
        }
        
        // 显示加载状态
        function showLoading(element, message) {
            element.innerHTML = `
                <div style="text-align: center; padding: 1rem; color: #666;">
                    <div style="border: 2px solid #f3f3f3; border-top: 2px solid #3498db; border-radius: 50%; width: 20px; height: 20px; animation: spin 1s linear infinite; display: inline-block; margin-right: 0.5rem;"></div>
                    ${message}
                </div>
                <style>
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            `;
        }
        
        // 显示结果
        function showResult(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result result-${type}">${message}</div>`;
        }
    </script>
</body>
</html>
