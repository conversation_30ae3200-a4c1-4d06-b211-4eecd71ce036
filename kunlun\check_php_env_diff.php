<?php
/**
 * 检查CLI和Web环境的PHP配置差异
 */

header('Content-Type: application/json; charset=utf-8');

try {
    $results = [];
    
    // 1. 当前环境信息（Web环境）
    $results['web_environment'] = [
        'php_version' => PHP_VERSION,
        'php_sapi' => php_sapi_name(),
        'php_binary' => PHP_BINARY,
        'ini_file' => php_ini_loaded_file(),
        'extensions_dir' => ini_get('extension_dir')
    ];
    
    // 2. 检查Web环境的扩展
    $results['web_extensions'] = [
        'mysqli' => extension_loaded('mysqli'),
        'pdo' => extension_loaded('pdo'),
        'pdo_mysql' => extension_loaded('pdo_mysql'),
        'mysql' => extension_loaded('mysql')
    ];
    
    // 3. 检查mysqli函数
    $results['web_functions'] = [
        'mysqli_connect' => function_exists('mysqli_connect'),
        'mysqli_query' => function_exists('mysqli_query'),
        'mysqli_prepare' => function_exists('mysqli_prepare')
    ];
    
    // 4. 测试Web环境数据库连接
    $results['web_database_test'] = [
        'connection_attempt' => false,
        'connection_success' => false,
        'error' => null
    ];
    
    try {
        $results['web_database_test']['connection_attempt'] = true;
        
        // 包含conn_waf.php
        include_once '../conn_waf.php';
        
        if (isset($conn) && $conn) {
            $results['web_database_test']['connection_success'] = true;
            $results['web_database_test']['connection_type'] = get_class($conn);
            
            // 测试查询
            $testQuery = $conn->query("SELECT 1 as test, NOW() as current_time");
            if ($testQuery) {
                $results['web_database_test']['query_success'] = true;
                $row = $testQuery->fetch_assoc();
                $results['web_database_test']['query_result'] = $row;
            } else {
                $results['web_database_test']['query_success'] = false;
            }
        } else {
            $results['web_database_test']['error'] = '数据库连接对象未创建';
        }
    } catch (Exception $e) {
        $results['web_database_test']['error'] = $e->getMessage();
    }
    
    // 5. 检查CLI环境（通过shell_exec）
    $results['cli_test'] = [
        'php_version_check' => null,
        'extensions_check' => null,
        'script_test' => null
    ];
    
    // CLI PHP版本
    $cliVersion = shell_exec('php -v 2>&1');
    $results['cli_test']['php_version_check'] = $cliVersion;
    
    // CLI扩展检查
    $cliExtensions = shell_exec('php -m 2>&1');
    $results['cli_test']['extensions_check'] = $cliExtensions;
    $results['cli_test']['has_mysqli'] = strpos($cliExtensions, 'mysqli') !== false;
    
    // CLI脚本测试
    $scriptPath = __DIR__ . '/background_processor.php';
    if (file_exists($scriptPath)) {
        $cliTest = shell_exec("php $scriptPath --test 2>&1");
        $results['cli_test']['script_test'] = $cliTest;
        $results['cli_test']['script_success'] = strpos($cliTest, 'test mode started') !== false;
        $results['cli_test']['has_mysqli_error'] = strpos($cliTest, 'mysqli_connect') !== false;
    }
    
    // 6. 环境差异分析
    $results['environment_analysis'] = [
        'web_mysqli_available' => $results['web_extensions']['mysqli'],
        'cli_mysqli_available' => $results['cli_test']['has_mysqli'] ?? false,
        'same_php_version' => strpos($results['cli_test']['php_version_check'], PHP_VERSION) !== false,
        'environment_mismatch' => false
    ];
    
    // 检查是否存在环境不匹配
    if ($results['environment_analysis']['web_mysqli_available'] && !$results['environment_analysis']['cli_mysqli_available']) {
        $results['environment_analysis']['environment_mismatch'] = true;
        $results['environment_analysis']['issue'] = 'Web环境有mysqli扩展，但CLI环境没有';
    }
    
    // 7. 解决方案建议
    $solutions = [];
    
    if ($results['environment_analysis']['environment_mismatch']) {
        $solutions[] = [
            'title' => 'CLI和Web环境mysqli扩展不一致',
            'description' => 'Web环境可以使用mysqli，但CLI环境不行',
            'solutions' => [
                '1. 检查CLI PHP配置文件路径',
                '2. 确保CLI PHP加载了mysqli扩展',
                '3. 可能需要为CLI环境单独安装mysqli',
                '4. 检查是否使用了不同版本的PHP'
            ]
        ];
        
        $solutions[] = [
            'title' => '临时解决方案',
            'description' => '如果只是CLI环境缺少mysqli',
            'solutions' => [
                '1. 使用Web界面启动后台处理程序',
                '2. 通过cron任务调用Web接口',
                '3. 修改启动脚本使用Web方式'
            ]
        ];
    }
    
    if ($results['web_database_test']['connection_success']) {
        $solutions[] = [
            'title' => 'Web环境正常',
            'description' => 'Web环境数据库连接正常，可以使用Web界面',
            'solutions' => [
                '访问 /kunlun/sms_admin/ 使用Web界面',
                '通过系统管理器启动后台处理程序',
                'Web环境的所有功能都可以正常使用'
            ]
        ];
    }
    
    $results['solutions'] = $solutions;
    
    echo json_encode([
        'status' => 1,
        'message' => '环境差异检查完成',
        'data' => $results,
        'summary' => [
            'web_mysqli_works' => $results['web_database_test']['connection_success'] ?? false,
            'cli_mysqli_works' => $results['cli_test']['script_success'] ?? false,
            'environment_mismatch' => $results['environment_analysis']['environment_mismatch']
        ]
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 0,
        'message' => '检查失败: ' . $e->getMessage(),
        'error' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
