<?php
/**
 * 短信发送管理API
 * 创建时间: 2025-08-12
 * 功能: 短信发送记录查询、重发、测试等功能
 */

require_once '../../db_connection.php';

$APP_ID = 35; // 预警系统应用ID

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 检查用户权限
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['status' => 0, 'message' => '未登录或登录信息已过期']);
    exit;
}

// 检查应用权限
function isHasPerm($APP_ID) {
    global $conn;
    
    if (!isset($_SESSION['user_id'])) {
        return false;
    }
    
    // 系统管理员拥有所有权限
    if (isAdmin()) {
        return true;
    }
    
    // 检查应用管理员权限
    if (isAppAdmin($APP_ID)) {
        return true;
    }
    
    return false;
}

if (!isHasPerm($APP_ID)) {
    echo json_encode(['status' => 0, 'message' => '权限不足']);
    exit;
}

$action = $_POST['action'] ?? $_GET['action'] ?? '';
$user_id = $_SESSION['user_id'];

switch ($action) {
    case 'list':
        getSmsLogList();
        break;
    case 'retry':
        retrySms();
        break;
    case 'retryFailed':
        retryFailedSms();
        break;
    case 'test':
        testSms();
        break;
    case 'export':
        exportSmsLogs();
        break;
    default:
        echo json_encode(['status' => 0, 'message' => '无效的操作']);
}

/**
 * 获取短信发送记录列表
 */
function getSmsLogList() {
    global $conn;
    
    $page = intval($_POST['page'] ?? 1);
    $pageSize = intval($_POST['pageSize'] ?? 20);
    $offset = ($page - 1) * $pageSize;
    
    // 搜索条件
    $searchStatus = $_POST['searchStatus'] ?? '';
    $searchStartDate = $_POST['searchStartDate'] ?? '';
    $searchEndDate = $_POST['searchEndDate'] ?? '';
    $searchPhone = $_POST['searchPhone'] ?? '';
    
    $whereConditions = [];
    $params = [];
    $types = '';
    
    if (!empty($searchStatus)) {
        $whereConditions[] = "ssl.send_status = ?";
        $params[] = $searchStatus;
        $types .= 's';
    }
    
    if (!empty($searchStartDate)) {
        $whereConditions[] = "DATE(ssl.created_time) >= ?";
        $params[] = $searchStartDate;
        $types .= 's';
    }
    
    if (!empty($searchEndDate)) {
        $whereConditions[] = "DATE(ssl.created_time) <= ?";
        $params[] = $searchEndDate;
        $types .= 's';
    }
    
    if (!empty($searchPhone)) {
        $whereConditions[] = "ssl.recipient_phone LIKE ?";
        $params[] = "%$searchPhone%";
        $types .= 's';
    }
    
    $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);
    
    // 查询总数
    $countSql = "SELECT COUNT(*) as total 
                FROM Kunlun_sms_send_log ssl
                $whereClause";
    
    if (!empty($params)) {
        $countStmt = $conn->prepare($countSql);
        $countStmt->bind_param($types, ...$params);
        $countStmt->execute();
        $countResult = $countStmt->get_result();
    } else {
        $countResult = $conn->query($countSql);
    }
    
    $total = $countResult->fetch_assoc()['total'];
    
    // 查询列表数据
    $listSql = "SELECT 
                    ssl.id,
                    ssl.alarm_record_id,
                    ssl.recipient_name,
                    ssl.recipient_phone,
                    ssl.recipient_unit_name,
                    ssl.sms_content,
                    ssl.send_status,
                    ssl.send_time,
                    ssl.response_message,
                    ssl.retry_count,
                    ssl.created_time,
                    ar.person_name,
                    ar.person_sfz,
                    ar.score,
                    ar.severity,
                    ar.location_name,
                    ar.captured_time
                FROM Kunlun_sms_send_log ssl
                LEFT JOIN Kunlun_alarm_records ar ON ssl.alarm_record_id = ar.id
                $whereClause
                ORDER BY ssl.created_time DESC
                LIMIT $offset, $pageSize";
    
    if (!empty($params)) {
        $listStmt = $conn->prepare($listSql);
        $listStmt->bind_param($types, ...$params);
        $listStmt->execute();
        $listResult = $listStmt->get_result();
    } else {
        $listResult = $conn->query($listSql);
    }
    
    $list = [];
    while ($row = $listResult->fetch_assoc()) {
        $list[] = $row;
    }
    
    echo json_encode([
        'status' => 1,
        'message' => '获取成功',
        'data' => [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'pageSize' => $pageSize,
            'totalPages' => ceil($total / $pageSize)
        ]
    ]);
}

/**
 * 重发单条短信
 */
function retrySms() {
    global $conn, $user_id;
    
    $smsLogId = intval($_POST['id'] ?? 0);
    
    if ($smsLogId <= 0) {
        echo json_encode(['status' => 0, 'message' => '无效的短信记录ID']);
        return;
    }
    
    // 获取短信记录
    $sql = "SELECT * FROM Kunlun_sms_send_log WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $smsLogId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['status' => 0, 'message' => '短信记录不存在']);
        return;
    }
    
    $smsLog = $result->fetch_assoc();
    
    // 检查是否可以重发
    if ($smsLog['send_status'] === 'success') {
        echo json_encode(['status' => 0, 'message' => '该短信已发送成功，无需重发']);
        return;
    }
    
    // 发送短信
    $sendResult = sendSmsMessage($smsLogId);
    
    if ($sendResult['success']) {
        // 记录操作日志
        global $APP_ID;
        logOperation($conn, $user_id, $APP_ID, "手动重发短信，ID: $smsLogId, 接收人: " . $smsLog['recipient_name']);
        
        echo json_encode(['status' => 1, 'message' => '短信重发成功']);
    } else {
        echo json_encode(['status' => 0, 'message' => '短信重发失败: ' . $sendResult['message']]);
    }
}

/**
 * 批量重发失败短信
 */
function retryFailedSms() {
    global $conn, $user_id;
    
    $hours = intval($_POST['hours'] ?? 24); // 默认重发24小时内的失败短信
    
    // 获取失败的短信记录
    $sql = "SELECT id FROM Kunlun_sms_send_log 
            WHERE send_status = 'failed' 
            AND created_time >= DATE_SUB(NOW(), INTERVAL ? HOUR)
            AND retry_count < 3
            ORDER BY created_time DESC
            LIMIT 100"; // 限制一次最多重发100条
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $hours);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $successCount = 0;
    $failedCount = 0;
    $totalCount = $result->num_rows;
    
    while ($row = $result->fetch_assoc()) {
        $sendResult = sendSmsMessage($row['id']);
        if ($sendResult['success']) {
            $successCount++;
        } else {
            $failedCount++;
        }
    }
    
    // 记录操作日志
    global $APP_ID;
    logOperation($conn, $user_id, $APP_ID, "批量重发失败短信，总数: $totalCount, 成功: $successCount, 失败: $failedCount");
    
    echo json_encode([
        'status' => 1,
        'message' => "批量重发完成，总数: $totalCount, 成功: $successCount, 失败: $failedCount",
        'data' => [
            'total' => $totalCount,
            'success' => $successCount,
            'failed' => $failedCount
        ]
    ]);
}

/**
 * 测试短信发送
 */
function testSms() {
    global $conn, $user_id;
    
    $phone = trim($_POST['phone'] ?? '');
    $content = trim($_POST['content'] ?? '');
    
    if (empty($phone) || empty($content)) {
        echo json_encode(['status' => 0, 'message' => '请填写手机号和短信内容']);
        return;
    }
    
    // 验证手机号格式
    if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
        echo json_encode(['status' => 0, 'message' => '手机号格式不正确']);
        return;
    }
    
    try {
        // 引入短信发送接口
        require_once '../../../api/SmsSystem.php';
        
        // 发送测试短信
        $response = SMS_sendmessage($phone, $content, '岳池县公安局');
        
        // 解析响应
        $responseData = json_decode($response, true);
        $success = false;
        $message = $response;
        
        if ($responseData && isset($responseData['renturncode'])) {
            if ($responseData['renturncode'] === '0' || $responseData['renturncode'] === 0) {
                $success = true;
                $message = '测试短信发送成功';
            } else {
                $message = '发送失败，错误码: ' . $responseData['renturncode'] . ', 消息: ' . ($responseData['message'] ?? '');
            }
        }
        
        // 记录操作日志
        global $APP_ID;
        logOperation($conn, $user_id, $APP_ID, "测试短信发送，手机号: $phone, 结果: " . ($success ? '成功' : '失败'));
        
        echo json_encode([
            'status' => $success ? 1 : 0,
            'message' => $message,
            'data' => [
                'response' => $response
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['status' => 0, 'message' => '发送异常: ' . $e->getMessage()]);
    }
}

/**
 * 导出短信记录
 */
function exportSmsLogs() {
    global $conn;
    
    $startDate = $_POST['startDate'] ?? date('Y-m-d', strtotime('-30 days'));
    $endDate = $_POST['endDate'] ?? date('Y-m-d');
    
    // 查询数据
    $sql = "SELECT 
                ssl.id,
                ssl.recipient_name,
                ssl.recipient_phone,
                ssl.recipient_unit_name,
                ssl.sms_content,
                ssl.send_status,
                ssl.send_time,
                ssl.retry_count,
                ssl.created_time,
                ar.person_name,
                ar.person_sfz,
                ar.score,
                ar.severity,
                ar.location_name,
                ar.captured_time
            FROM Kunlun_sms_send_log ssl
            LEFT JOIN Kunlun_alarm_records ar ON ssl.alarm_record_id = ar.id
            WHERE DATE(ssl.created_time) BETWEEN ? AND ?
            ORDER BY ssl.created_time DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ss", $startDate, $endDate);
    $stmt->execute();
    $result = $stmt->get_result();
    
    // 生成CSV内容
    $csvContent = "\xEF\xBB\xBF"; // UTF-8 BOM
    $csvContent .= "记录ID,接收人,手机号,单位,短信内容,发送状态,发送时间,重试次数,创建时间,预警人员,身份证号,相似度,严重级别,位置,抓拍时间\n";
    
    while ($row = $result->fetch_assoc()) {
        $csvContent .= sprintf('"%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s"' . "\n",
            $row['id'],
            $row['recipient_name'] ?? '',
            $row['recipient_phone'] ?? '',
            $row['recipient_unit_name'] ?? '',
            str_replace('"', '""', $row['sms_content'] ?? ''),
            $row['send_status'] ?? '',
            $row['send_time'] ?? '',
            $row['retry_count'] ?? 0,
            $row['created_time'] ?? '',
            $row['person_name'] ?? '',
            $row['person_sfz'] ?? '',
            $row['score'] ?? '',
            $row['severity'] ?? '',
            $row['location_name'] ?? '',
            $row['captured_time'] ?? ''
        );
    }
    
    // 设置下载头
    $filename = "短信发送记录_{$startDate}_to_{$endDate}.csv";
    header('Content-Type: application/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . strlen($csvContent));
    
    echo $csvContent;
    exit;
}

/**
 * 发送短信消息
 */
function sendSmsMessage($smsLogId) {
    global $conn;
    
    try {
        // 获取短信记录
        $sql = "SELECT * FROM Kunlun_sms_send_log WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $smsLogId);
        $stmt->execute();
        $result = $stmt->get_result();
        $smsLog = $result->fetch_assoc();
        
        if (!$smsLog) {
            return ['success' => false, 'message' => '短信记录不存在'];
        }
        
        // 引入短信发送接口
        require_once '../../../api/SmsSystem.php';
        
        // 发送短信
        $response = SMS_sendmessage(
            $smsLog['recipient_phone'],
            $smsLog['sms_content'],
            '岳池县公安局'
        );
        
        // 解析响应
        $responseData = json_decode($response, true);
        $sendStatus = 'failed';
        $responseMessage = $response;
        
        if ($responseData && isset($responseData['renturncode'])) {
            if ($responseData['renturncode'] === '0' || $responseData['renturncode'] === 0) {
                $sendStatus = 'success';
            }
        }
        
        // 更新发送状态
        $updateSql = "UPDATE Kunlun_sms_send_log SET 
            send_status = ?, send_time = NOW(), response_message = ?, retry_count = retry_count + 1
            WHERE id = ?";
        $updateStmt = $conn->prepare($updateSql);
        $updateStmt->bind_param("ssi", $sendStatus, $responseMessage, $smsLogId);
        $updateStmt->execute();
        
        return [
            'success' => $sendStatus === 'success',
            'message' => $sendStatus === 'success' ? '发送成功' : '发送失败: ' . $responseMessage
        ];
        
    } catch (Exception $e) {
        // 更新为失败状态
        $updateSql = "UPDATE Kunlun_sms_send_log SET 
            send_status = 'failed', send_time = NOW(), response_message = ?, retry_count = retry_count + 1
            WHERE id = ?";
        $updateStmt = $conn->prepare($updateSql);
        $errorMsg = "异常: " . $e->getMessage();
        $updateStmt->bind_param("si", $errorMsg, $smsLogId);
        $updateStmt->execute();
        
        return ['success' => false, 'message' => '发送异常: ' . $e->getMessage()];
    }
}
?>
