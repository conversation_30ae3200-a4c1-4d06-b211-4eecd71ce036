<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>昆仑人脸预警系统 API 测试</h1>
    
    <div class="test-section">
        <h3>1. WAF和权限测试</h3>
        <button onclick="testWAF()">测试WAF和权限</button>
        <div id="wafResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 数据库连接测试</h3>
        <button onclick="testDatabase()">测试数据库连接</button>
        <div id="dbResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 用户列表测试</h3>
        <button onclick="testUsers()">获取用户列表</button>
        <div id="usersResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. 图库列表测试</h3>
        <button onclick="testAlbums()">获取图库列表</button>
        <div id="albumsResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>5. 配置列表测试</h3>
        <button onclick="testConfigs()">获取配置列表</button>
        <div id="configsResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>6. 统计数据测试</h3>
        <button onclick="testStats()">获取统计数据</button>
        <div id="statsResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>7. 数据类型测试</h3>
        <button onclick="testDataTypes()">测试数据类型转换</button>
        <div id="dataTypesResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>8. 统计功能修复测试</h3>
        <button onclick="testStatsFix()">测试统计功能修复</button>
        <div id="statsFixResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>9. 编辑ID保持测试</h3>
        <button onclick="testEditIdPreservation()">测试编辑时ID保持不变</button>
        <div id="editIdResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>10. PHP路径和权限测试</h3>
        <button onclick="testPhpPath()">测试PHP路径和权限</button>
        <div id="phpPathResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>11. PHP语法检查</h3>
        <button onclick="testSyntax()">检查PHP文件语法</button>
        <div id="syntaxResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>12. 无mysqli扩展测试</h3>
        <button onclick="testNoMysqli()">测试PDO数据库连接</button>
        <div id="noMysqliResult" class="result"></div>
    </div>

    <script>
        async function testApi(url, data, resultId) {
            const resultDiv = document.getElementById(resultId);
            resultDiv.textContent = '正在测试...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams(data)
                });
                
                const text = await response.text();
                console.log('Raw response:', text);
                
                try {
                    const json = JSON.parse(text);
                    resultDiv.textContent = JSON.stringify(json, null, 2);
                    resultDiv.className = 'result success';
                } catch (e) {
                    resultDiv.textContent = '响应不是有效的JSON:\n' + text;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = '请求失败: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
        
        function testWAF() {
            testApi('api/test_waf.php', {}, 'wafResult');
        }

        function testDatabase() {
            testApi('api/test_db.php', {}, 'dbResult');
        }
        
        function testUsers() {
            testApi('api/config_manage.php', { action: 'getUsers' }, 'usersResult');
        }

        function testAlbums() {
            testApi('api/config_manage.php', { action: 'getAlbums' }, 'albumsResult');
        }

        function testConfigs() {
            testApi('api/config_manage.php', {
                action: 'list',
                page: 1,
                pageSize: 10
            }, 'configsResult');
        }

        function testStats() {
            testApi('api/stats.php', {}, 'statsResult');
        }

        function testDataTypes() {
            testApi('api/test_data_types.php', {}, 'dataTypesResult');
        }

        function testStatsFix() {
            testApi('api/test_stats_fix.php', {}, 'statsFixResult');
        }

        function testEditIdPreservation() {
            testApi('api/test_edit_id_preservation.php', {}, 'editIdResult');
        }

        function testPhpPath() {
            testApi('../test_php_simple.php', {}, 'phpPathResult');
        }

        function testSyntax() {
            testApi('../check_syntax.php', {}, 'syntaxResult');
        }

        function testNoMysqli() {
            testApi('../test_no_mysqli.php', {}, 'noMysqliResult');
        }
    </script>
</body>
</html>
