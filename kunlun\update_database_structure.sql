-- 数据库表结构更新脚本
-- 更新时间: 2025-08-12
-- 说明: 移除image_base64字段，优化数据存储结构

-- 检查表是否存在image_base64字段
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'Kunlun_alarm_records' 
AND COLUMN_NAME = 'image_base64';

-- 如果字段存在，则删除它
-- 注意：在生产环境中执行前请先备份数据
ALTER TABLE `Kunlun_alarm_records` DROP COLUMN IF EXISTS `image_base64`;

-- 确保image_url字段存在且类型正确
ALTER TABLE `Kunlun_alarm_records` 
MODIFY COLUMN `image_url` varchar(500) DEFAULT NULL COMMENT '原始图片URL';

-- 添加索引优化查询性能（如果不存在）
CREATE INDEX IF NOT EXISTS `idx_image_url` ON `Kunlun_alarm_records` (`image_url`);

-- 更新表注释
ALTER TABLE `Kunlun_alarm_records` 
COMMENT = '预警人员记录表（已优化，移除Base64图片存储）';

-- 验证表结构
DESCRIBE `Kunlun_alarm_records`;

-- 显示表的存储引擎和字符集信息
SHOW TABLE STATUS LIKE 'Kunlun_alarm_records';

-- 检查相关索引
SHOW INDEX FROM `Kunlun_alarm_records`;
