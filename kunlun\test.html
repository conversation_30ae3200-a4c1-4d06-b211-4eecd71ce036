<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短信状态查询测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .input-group { margin-bottom: 15px; }
        label { display: inline-block; width: 120px; margin-right: 10px; }
        input, textarea { padding: 5px; width: 200px; }
        button { padding: 5px 15px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #45a049; }
        .result { margin-top: 20px; padding: 10px; border: 1px solid #ddd; }
        .success { background-color: #f0f0f0; }
        .error { background-color: #ffebee; }
        table { width: 100%; margin-top: 20px; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f5f5f5; }
    </style>
</head>
<body>
    <div class="container">
        <h1>短信状态查询测试页面</h1>
        
        <form id="reportForm" method="post" action="report.php">
            <div class="input-group">
                <label for="systemId">系统接入码：</label>
                <input type="text" id="systemId" value="ed47f8d43acec3ee48d7409146bef5c1" required>
            </div>
            
            <div class="input-group">
                <label for="extensionNo">系统扩展号：</label>
                <input type="text" id="extensionNo" value="039" required>
            </div>
            
            <div class="input-group">
                <label for="smId">短信消息ID：</label>
                <input type="text" id="smId" required placeholder="请输入要查询的短信ID">
            </div>
            
            <button type="submit" >查询状态</button>
        </form>

        <div class="result" id="result"></div>
    </div>


</body>
</html>