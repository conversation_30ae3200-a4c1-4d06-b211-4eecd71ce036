#!/usr/bin/env php
<?php
/**
 * 简单计划任务管理器安装脚本
 */

echo "简单计划任务管理器安装程序\n";
echo "========================\n\n";

// 数据库配置
$config = [
    'host' => '127.0.0.1',
    'port' => 3306,
    'database' => 'taskmgr',
    'username' => 'root',
    'password' => 'YC@yc110'
];

echo "数据库配置:\n";
echo "主机: {$config['host']}\n";
echo "端口: {$config['port']}\n";
echo "数据库: {$config['database']}\n";
echo "用户名: {$config['username']}\n";
echo "密码: " . str_repeat('*', strlen($config['password'])) . "\n\n";

try {
    echo "1. 连接数据库...\n";
    
    // 连接MySQL服务器
    $dsn = "mysql:host={$config['host']};port={$config['port']};charset=utf8mb4";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    echo "✓ 数据库连接成功\n\n";
    
    echo "2. 创建数据库...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['database']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE `{$config['database']}`");
    echo "✓ 数据库创建成功\n\n";
    
    echo "3. 创建数据表...\n";
    
    // 创建任务表
    $sql = "CREATE TABLE IF NOT EXISTS tasks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL COMMENT '任务名称',
        command TEXT NOT NULL COMMENT '执行命令',
        cron_expression VARCHAR(100) NOT NULL COMMENT 'Cron表达式',
        status ENUM('enabled', 'disabled') DEFAULT 'enabled' COMMENT '任务状态',
        last_run DATETIME NULL COMMENT '上次执行时间',
        next_run DATETIME NULL COMMENT '下次执行时间',
        run_count INT DEFAULT 0 COMMENT '执行次数',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
    ) ENGINE=InnoDB COMMENT='计划任务表'";
    
    $pdo->exec($sql);
    echo "✓ 任务表创建成功\n";
    
    // 创建日志表
    $sql = "CREATE TABLE IF NOT EXISTS task_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT NOT NULL COMMENT '任务ID',
        status ENUM('success', 'failed') NOT NULL COMMENT '执行状态',
        output TEXT COMMENT '执行输出',
        error_message TEXT COMMENT '错误信息',
        start_time DATETIME NOT NULL COMMENT '开始时间',
        end_time DATETIME NOT NULL COMMENT '结束时间',
        duration DECIMAL(10,2) NOT NULL COMMENT '执行时长（秒）'
    ) ENGINE=InnoDB COMMENT='任务执行日志表'";
    
    $pdo->exec($sql);
    echo "✓ 日志表创建成功\n\n";
    
    echo "4. 插入示例数据...\n";
    
    // 插入示例任务
    $sampleTasks = [
        ['系统状态检查', 'df -h', '*/5 * * * *'],
        ['清理临时文件', 'find /tmp -name "*.tmp" -mtime +1 -delete', '0 2 * * *'],
        ['备份重要文件', 'tar -czf /backup/important_$(date +%Y%m%d).tar.gz /important', '0 3 * * 0']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO tasks (name, command, cron_expression) VALUES (?, ?, ?)");
    
    foreach ($sampleTasks as $task) {
        $stmt->execute($task);
        echo "✓ 示例任务: {$task[0]}\n";
    }
    
    echo "\n5. 验证安装...\n";
    
    // 检查表是否存在
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    $expectedTables = ['tasks', 'task_logs'];
    
    foreach ($expectedTables as $table) {
        if (in_array($table, $tables)) {
            echo "✓ 表 $table 存在\n";
        } else {
            throw new Exception("表 $table 不存在");
        }
    }
    
    // 检查任务数量
    $taskCount = $pdo->query("SELECT COUNT(*) FROM tasks")->fetchColumn();
    echo "✓ 任务数量: $taskCount\n\n";
    
    echo "安装完成！\n";
    echo "=========\n\n";
    
    echo "访问方式:\n";
    echo "1. Web界面: 直接访问 index.php\n";
    echo "2. 命令行: php index.php cron\n\n";
    
    echo "设置自动执行:\n";
    echo "编辑crontab: crontab -e\n";
    echo "添加以下行:\n";
    echo "* * * * * /usr/bin/php " . __DIR__ . "/index.php cron >/dev/null 2>&1\n\n";
    
    echo "常用Cron表达式:\n";
    echo "每分钟: * * * * *\n";
    echo "每小时: 0 * * * *\n";
    echo "每天2点: 0 2 * * *\n";
    echo "每周日: 0 0 * * 0\n";
    echo "每月1号: 0 0 1 * *\n\n";
    
    echo "注意事项:\n";
    echo "- 确保PHP有执行shell命令的权限\n";
    echo "- 谨慎配置任务命令，避免安全风险\n";
    echo "- 定期检查任务执行日志\n\n";
    
} catch (PDOException $e) {
    echo "数据库错误: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "安装错误: " . $e->getMessage() . "\n";
    exit(1);
}

echo "感谢使用简单计划任务管理器！\n";
