<?php
// 禁止缓存
header("Cache-Control: no-cache");
header("Content-Type: text/html; charset=UTF-8");

// 显示错误信息
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 初始化返回结果
$result = '';

// 检查是否有表单提交
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // 接收表单数据
    $types = isset($_POST['types']) ? intval($_POST['types']) : 1;
    $pageNo = isset($_POST['pageNo']) ? intval($_POST['pageNo']) : 1;
    $pageSize = isset($_POST['pageSize']) ? intval($_POST['pageSize']) : 20;

    // 初始化curl句柄
    $ch = curl_init();

    // 设置请求的URL
    curl_setopt($ch, CURLOPT_URL, 'http://80.164.12.151:9100/openapi/kunlun/v1/device/cameras:search');

    // 设置请求方法为POST
    curl_setopt($ch, CURLOPT_POST, 1);

    // 设置请求头
    $headers = [
        'Content-Type: application/json;charset=UTF-8',
        'X-OA-Token: c58d087660b4415faa71496ba2003ae5',
        'Api_Id: device_cameraSearch'
    ];
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    // 设置请求体数据
    $data = json_encode([
        'types' => [$types],
        'pageNo' => $pageNo,
        'pageSize' => $pageSize
    ]);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

    // 执行请求
    $response = curl_exec($ch);

    // 检查执行结果
    if (curl_errno($ch)) {
        $result = 'Error: ' . curl_error($ch);
    } else {
        // 处理响应
        $resultData = json_decode($response, true);
        if (is_array($resultData)) {
            $result = '响应数据：' . PHP_EOL . print_r($resultData, true);
        } else {
            $result = '无效的JSON响应：' . $response;
        }
    }

    // 释放curl句柄
    curl_close($ch);

    // 记录请求日志（可选）
    $log = [
        'types' => $types,
        'pageNo' => $pageNo,
        'pageSize' => $pageSize,
        'response' => $result
    ];
    // 这里可以将$log保存到数据库或其他存储系统
    // 例如，使用file_put_contents记录到日志文件：
    // file_put_contents('request_log.txt', json_encode($log) . PHP_EOL, FILE_APPEND);
}

// 显示HTML表单和结果
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Search</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ccc;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .result {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            min-height: 100px;
        }
        .form-group {
            margin-bottom: 10px;
        }
        label {
            display: inline-block;
            width: 120px;
            margin-right: 10px;
        }
        input, select {
            padding: 5px;
            width: 200px;
        }
        button {
            padding: 5px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <h1>Camera Search</h1>
    <div class="container">
        <form method="POST">
            <div class="form-group">
                <label for="types">设备类型：</label>
                <select id="types" name="types">
                    <option value="1">类型1</option>
                    <option value="2">类型2</option>
                    <option value="3">类型3</option>
                </select>
            </div>
            <div class="form-group">
                <label for="pageNo">页码：</label>
                <input type="number" id="pageNo" name="pageNo" value="1">
            </div>
            <div class="form-group">
                <label for="pageSize">每页大小：</label>
                <input type="number" id="pageSize" name="pageSize" value="20">
            </div>
            <button type="submit">提交请求</button>
        </form>
    </div>
    <div class="result">
        <?php echo $result; ?>
    </div>
</body>
</html>