﻿_jsload&&_jsload('map', 'BMap.register(function(cL){if(cL.config&&cL.config.isOverviewMap){return}if(cL.isLoaded()){bk(cL)}else{cL.addEventListener("load",function(){bk(this)})}cL.cityName="\u4e2d\u56fd";var T={};T.enableRequest=true;T.request=function(){if(T.enableRequest){T.enableRequest=false;setTimeout(function(){T._request()},500)}};T._request=function(){var cN=cL.getBounds(),cP=cL.getZoom(),cM=ba.convertLL2MC(cN.getSouthWest()),cO=ba.convertLL2MC(cN.getNorthEast());bb.request(function(cQ){T.enableRequest=true;if(cQ&&cQ.current_city&&cQ.current_city["name"]){cL.cityName=cQ.current_city["name"];aZ(cL)}},{qt:"cen",b:cM.lng+","+cM.lat+";"+cO.lng+","+cO.lat,l:cP},"","",true)};cL.addEventListener("load",function(cM){T.request()});cL.addEventListener("moveend",function(cM){T.request()});cL.addEventListener("zoomend",function(cM){T.request()})});function bk(cL){if(cL.temp.copyadded){return}cL.temp.copyadded=true;var cN=new aH(81,2);if(aA()){if(cL.highResolutionEnabled()){cN.width=148;fontSize="21px"}else{cN.width=72;cN.height=0}}var cM=new am({offset:cN,printable:true});cL.cpyCtrl=cM;aZ(cL);cL.addEventListener("maptypechange",function(){aZ(cL)});cL.addControl(cM);var T=new b1();T._opts={printable:true};cL.addControl(T);cL.addEventListener("resize",function(){if(this.getSize().width>=220&&cL.getSize().height>=100){T.show();cM.setOffset(cN)}else{T.hide();cM.setOffset(new aH(4,2))}});if(cL.getSize().width>=220&&cL.getSize().height>=100){T.show()}else{T.hide();cM.setOffset(new aH(4,2))}if(cL.highResolutionEnabled()){T.setOffset(new aH(3,2))}}function aZ(T){var cR="11px",cQ=T.cityName||"\u4e2d\u56fd",cN=T.getMapType(),cS=["\u5e38\u5dde\u5e02","\u6210\u90fd\u5e02","\u5927\u8fde\u5e02","\u91cd\u5e86\u5e02","\u5357\u4eac\u5e02","\u5357\u660c\u5e02","\u6b66\u6c49\u5e02"],cM=[],cP,cO="color:#fff;font-size:"+cR+";text-shadow:0 1px 3px black";switch(cN){case BMAP_SATELLITE_MAP:case BMAP_HYBRID_MAP:cM=[\'<span style="\'+cO+\'">&copy; 2013 Baidu - Data &copy; \'];cM.push(\'<a target="_blank" href="http://www.navinfo.com/" style="\'+cO+\'">NavInfo</a> &amp; \');for(var cL in cS){if(cS[cL]==cQ){cP=true;break}}if(cP){cM.push(\'<a target="_blank" href="http://www.yootu.com/" style="\'+cO+\'">yootu</a>\')}else{cM.push(\'<a target="_blank" href="http://www.cennavi.com.cn/" style="\'+cO+\'">CenNavi</a>\')}cM.push(\' &amp; <a target="_blank" href="http://www.365ditu.com/" style="\'+cO+\'">\u9053\u9053\u901a</a>\');cM.push(" , Image &copy; DigitalGlobe & </span>");cM.push(\'<a href="http://www.chinasiwei.com" target="_blank" style="\'+cO+\'">chinasiwei</a>\');break;case BMAP_PERSPECTIVE_MAP:cM=[\'<span style="\'+cO+\'">&copy; 2013 Baidu - Data &copy; </span>\',\'<a href="http://o.cn" target="_blank" style="color:#fff;font-size:\'+cR+\';text-shadow:0 1px 3px black">\u90fd\u5e02\u5708</a>\'];break;default:cM=[\'<span style="font-size:\'+cR+\'">&copy; 2013 Baidu&nbsp;- Data &copy; \'];cM.push(\'<a target="_blank" href="http://www.navinfo.com/">NavInfo</a> &amp; \');for(var cL in cS){if(cS[cL]==cQ){cP=true;break}}if(cP){cM.push(\'<a target="_blank" href="http://www.yootu.com/">yootu</a>\')}else{cM.push(\'<a target="_blank" href="http://www.cennavi.com.cn/">CenNavi</a>\')}cM.push(\' &amp; <a target="_blank" href="http://www.365ditu.com/">\u9053\u9053\u901a</a>\');cM.push("</span>");break}cM=cM.join("");}function b1(T){this.defaultAnchor=BMAP_ANCHOR_BOTTOM_LEFT;this.defaultOffset=new aH(1,0);this.IMG_URL=cc.imgPath+(aA()?"copyright_logo_s.png":"copyright_logo.png")}b1.prototype=new cp();b1.prototype.initialize=function(cL){this._map=cL;var cM=aa("div");cM.style.height="32px";var T=aa("a",{title:"\u5230\u767e\u5ea6\u5730\u56fe\u67e5\u770b\u6b64\u533a\u57df",target:"_blank",href:"http://map.baidu.com/?sr=1"});T.style.outline="none";if(a8.browser.ie==6){T.innerHTML="<div style=\'cursor:pointer;width:77px;height:32px;filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src="+this.IMG_URL+")\'></div>"}else{T.innerHTML="<img style=\'border:none;width:77px;height:32px\' src=\'"+this.IMG_URL+"\' />"}if(aA()){if(this._map.highResolutionEnabled()){cM.style.height="50px";T.href="#";this.IMG_URL=cc.imgPath+"copyright_logo_hd.png";T.innerHTML="<img style=\'border:none;width:136px;height:50px\' src=\'"+this.IMG_URL+"\' />"}else{cM.style.height="25px";T.href="#";T.innerHTML="<img style=\'border:none;width:68px;height:25px\' src=\'"+this.IMG_URL+"\' />"}}cM.appendChild(T);return cM};a8.extend(bz.prototype,{_draw:function(){this._bind()},_bind:function(){var T=this;T._watchSize=function(){var cM=T.getSize();if(T.width!=cM.width||T.height!=cM.height){var cO=new aH(T.width,T.height);var cQ=new bg("onbeforeresize");cQ.size=cO;T.dispatchEvent(cQ);T._updateCenterPoint((cM.width-T.width)/2,(cM.height-T.height)/2);T.maskLayer.style.width=(T.width=cM.width)+"px";T.maskLayer.style.height=(T.height=cM.height)+"px";var cN=new bg("onresize");cN.size=cM;T.dispatchEvent(cN);var cL=parseInt(T.platform.style.left)||0;var cP=parseInt(T.platform.style.top)||0;if(T.currentOperation!=0&&(T.offsetX!=cL||T.offsetY!=cP)){T._setPlatformPosition(cL,cP)}}};a8.on(T.maskLayer,"mouseover",function(cL){T.dispatchEvent(new bg("onmouseover"))});a8.on(T.maskLayer,"mouseout",function(cL){T.dispatchEvent(new bg("onmouseout"))})},_setPlatformPosition:function(T,cN,cL,cM){if(isNaN(T)||isNaN(cN)){return}if(this.offsetX==T&&this.offsetY==cN){return}this._updateCenterPoint(this.offsetX-T,this.offsetY-cN,cL);T=Math.round(T);cN=Math.round(cN);this.offsetX=T;this.offsetY=cN;this.platform.style.left=T+"px";this.platform.style.top=cN+"px";this.maskLayer.style.left=-T+"px";this.maskLayer.style.top=-cN+"px";if(cM!=false){this.dispatchEvent(new bg("onmoving"))}},panTo:function(cL,cN){if(!(cL instanceof cd)){return}var cM=this.pointToPixel(cL);var T=Math.round(this.width/2);var cO=Math.round(this.height/2);cN=cN||{};if(Math.abs(T-cM.x)>this.width||Math.abs(cO-cM.y)>this.height||cN.noAnimation){this._panTo(T-cM.x,cO-cM.y,cL)}else{this._panBy(T-cM.x,cO-cM.y,{duration:cN.duration})}},_panTo:function(cL,T,cN){var cM=this.temp;if(cM.operating==true){return}if(cM.dragAni){cM.dragAni.stop()}this.dispatchEvent(new bg("onmovestart"));this._setPlatformPosition(this.offsetX+cL,this.offsetY+T,cN);this.dispatchEvent(new bg("onmoveend"))},panBy:function(cL,T,cM){cL=Math.round(cL)||0;T=Math.round(T)||0;cM=cM||{};if(Math.abs(cL)<=this.width&&Math.abs(T)<=this.height&&(!cM.noAnimation)){this._panBy(cL,T)}else{this._panTo(cL,T)}},_panBy:function(cL,T,cO){if(this.temp.operating==true){return}cO=cO||{};this.dispatchEvent(new bg("onmovestart"));var cN=this,cM=cN.temp;cM.pl=cN.offsetX;cM.pt=cN.offsetY;if(cM.tlPan){cM.tlPan.cancel()}if(cM.dragAni){cM.dragAni.stop()}cM.tlPan=new g({fps:cO.fps||cN.config.fps,duration:cO.duration||cN.config.actionDuration,transition:cO.transition||aw.easeInOutQuad,render:function(cP){this.terminative=cN.temp.operating;if(cN.temp.operating){return}cN._setPlatformPosition(cM.pl+Math.ceil(cL*cP),cM.pt+Math.ceil(T*cP))},finish:function(cP){cN.dispatchEvent(new bg("onmoveend"));cN.temp.tlPan=false;if(cN.temp.stopArrow==true){cN.temp.stopArrow=false;if(cN.temp.arrow!=0){cN._arrow()}}}})}});');
﻿_jsload&&_jsload('oppc', 'function cH(){this._container=null}BMap.register(function(cL){if(cL.config.isOverviewMap){return}var T=new cH();at(cL.container,T.render(cL.config.defaultCursor));T._container=cL.container.lastChild;cL.temp.zoomer=T});cH.prototype.render=function(cL){var T=[\'<div id=zoomer style="position:absolute;z-index:0;top:0px;left:0px;overflow:hidden;visibility:hidden;cursor:\'+cL+\'">\'];T.push(\'<div class="BMap_zoomer" style="top:0;left:0;"></div>\');T.push(\'<div class="BMap_zoomer" style="top:0;right:0;"></div>\');T.push(\'<div class="BMap_zoomer" style="bottom:0;left:0;"></div>\');T.push(\'<div class="BMap_zoomer" style="bottom:0;right:0;"></div>\');T.push("</div>");return T.join("")};cH.prototype.action=function(cS,cT){if(cH._timeline){return}var cR=this._container;if(!cR){return}var c0=cT;var cM=60;var cZ=120;var cU=4/3,cO=Math.ceil((c0?cM:cZ)/2),cP=Math.max(15,cO/cU),cQ=cR.style;cQ.width=cO*2+"px";cQ.height=cP*2+"px";cQ.visibility="visible";var cW=cR.children;if(c0){cW[0].style.backgroundPosition="0 0";cW[1].style.backgroundPosition="-7px 0";cW[2].style.backgroundPosition="0 -7px";cW[3].style.backgroundPosition="-7px -7px"}else{cW[0].style.backgroundPosition="-7px -7px";cW[1].style.backgroundPosition="0 -7px";cW[2].style.backgroundPosition="-7px 0";cW[3].style.backgroundPosition="0 0"}cW=null;var cY=cS.x-cO;var cX=cS.y-cP;if(isNaN(cY)||isNaN(cX)){return}cQ.left=cY+"px";cQ.top=cX+"px";var cL=Math.ceil((c0?cZ:cM)/2);var cN=Math.max(15,cL/cU);cL=cL-cO;cN=Math.ceil(cN-cP);var cV=this;var T=cV._container.style;if(cH._timeline){cH._timeline.end()}cH._timeline=new g({fps:20,duration:240,transition:aw.easeInQuad,delay:100,render:function(c2){if(c2<0.1){return}var c3=Math.ceil(cL*c2);var c1=Math.ceil(cN*c2);T.width=(cO+c3)*2+"px";T.height=(cP+c1)*2+"px";T.left=cY-c3+"px";T.top=cX-c1+"px"},finish:function(){cH._timeline=false;setTimeout(function(){cQ.visibility="hidden"},300)}})};BMap.register(function(cP){var cO=cP;var cN=cP.platform;function cQ(c0,cW){var cS=c0.srcElement||c0.target,cZ=c0.offsetX||c0.layerX||0,cY=c0.offsetY||c0.layerY||0,cV=null,cU=null;if(cS.nodeType!=1){cS=cS.parentNode}while(cS&&cS!=cO.container){if(cS.guid){if(a8.lang.instance(cS.guid) instanceof Y){cV=a8.lang.instance(cS.guid)}if(a8.lang.instance(cS.guid) instanceof bP){cU=a8.lang.instance(cS.guid)}}if(!(cS.clientWidth==0&&cS.clientHeight==0&&cS.offsetParent&&cS.offsetParent.nodeName=="TD")&&cS.namespaceURI!="http://www.w3.org/2000/svg"){cZ+=cS.offsetLeft||0;cY+=cS.offsetTop||0}else{if(cS.namespaceURI=="http://www.w3.org/2000/svg"&&BMap.DrawerSelector){var cT=BMap.DrawerSelector.getDrawer(cO);var cR=cT.getPalette();if(navigator.userAgent.indexOf("Opera")>-1&&cS.tagName!="svg"){var c1=a8.lang.instance(cS.guid);if(c1){var cX=c1.getBounds();cZ=cZ+cO.pointToPixel(cX.getSouthWest()).x;cY=cY+cO.pointToPixel(cX.getNorthEast()).y}break}if(cR&&(!a8.browser.ie||(a8.browser.ie==9&&cS.nodeName.toLowerCase()=="svg"))){cZ+=parseFloat(cR.style.left)+cO.offsetX;cY+=parseFloat(cR.style.top)+cO.offsetY}if(a8.browser.ie==9&&cS.nodeName.toLowerCase()!="svg"){cZ+=cO.offsetX;cY+=cO.offsetY;break}if(!a8.browser.ie){break}}}cS=cS.offsetParent}cW.offsetX=cZ;cW.offsetY=cY;cW.pixel=new bu(cZ,cY);cW.point=cO.pixelToPoint(cW.pixel);cW.overlay=cV;cW.infoWindow=cU;return cW}cP.container.onselectstart=function(){return false};a8.on(cO.platform,"mousemove",function(cR){if(cO.currentOperation==0){cO.dispatchEvent(cQ(cR,new bg("onmousemove").inherit(cR)))}});a8.on(cO.platform,"mousedown",function(cV){if(!cO.config.enableMouseDown){return}var cV=window.event||cV;if(!a8.browser.ie){cC(cV)}var cT=cO.temp;cT.operating=true;var cS=cV.srcElement||cV.target;if(cT.dragAni){cT.dragAni.stop()}cT.tpx=cV.clientX||cV.pageX||0;cT.tpy=cV.clientY||cV.pageY||0;var cR=cS;while(cR){if(cR==cO.container){break}if(a8.dom.hasClass(cR,"BMap_Marker")){cT.operating=false;var cU=a8.lang.instance(cR.guid);if(cU instanceof ad&&cU._config.clickable==true||cU._config.enableDragging==true){return}}cR=cR.parentNode}if(cS.nodeType!=1){cS=cS.parentNode}if(a8.browser.ie&&cO.maskLayer.setCapture){cO.maskLayer.setCapture()}cO.dispatchEvent(cQ(cV,new bg("onmousedown").inherit(cV)));if(cO.config.enableDragging&&!(cO.currentOperation&bn.drag)&&cV.button!=2){cT.mx=cT.tpx;cT.my=cT.tpy;cT.pl=cO.offsetX;cT.pt=cO.offsetY;cO.currentOperation|=bn.drag;if(cO.temp.curSpots.length==0){cO.platform.style.cursor=cO.config.draggingCursor}else{cO.platform.style.cursor="pointer"}}});a8.on(document,"mousemove",function(cU){var cU=window.event||cU;if(!a8.browser.ie){cC(cU)}var cT=cO.temp;var cR=cU.clientX||cU.pageX||0;var cX=cU.clientY||cU.pageY||0;if(cT.tpx||cT.tpy){cT.mox=cR-cT.tpx;cT.moy=cX-cT.tpy}var cV=aE(),cW=(cV-cT.lastLoadTileTime)>40;if(cV-cT.lastDomMoveTime<18){return}if(cW){cT.lastLoadTileTime=cV}cT.lastDomMoveTime=cV;if(cO.currentOperation&bn.drag&&cO.config.enableDragging){var cS=cO.platform.style;if(cS.cursor.replace(/"|\s/g,"")!=cO.config.draggingCursor){cS.cursor=cO.config.draggingCursor}if(!cT._moved){cO.dispatchEvent(cQ(cU,new bg("ondragstart").inherit(cU)));cO.dispatchEvent(new bg("onmovestart"));cT.dragStartPos=new bu(cR,cX);cT.dragStartTime=cV}if(cT.mx==0&&cT.my==0&&cO.temp.keyboardDrag){cT.mx=cR;cT.my=cX;cT.pl=cO.offsetX;cT.pt=cO.offsetY}if(cR-cT.mx!=0||cX-cT.my!=0){cO.temp._moved=true;cO.dispatchEvent(cQ(cU,new bg("ondragging").inherit(cU)));cO._setPlatformPosition(cT.pl+cR-cT.mx,cT.pt+cX-cT.my,null,cW)}}});a8.on(document,"mouseup",function(cU){if(a8.browser.ie&&cO.maskLayer.releaseCapture){cO.maskLayer.releaseCapture()}var cS=cO.temp;if(cS.preEnableClickPan){cO.enableClickPan(true)}var cU=window.event||cU,cR=cU.clientX||cU.pageX,cV=cU.clientY||cU.pageY;if(cO.currentOperation&bn.drag&&cO.config.enableDragging){cO.currentOperation^=bn.drag;if(cS.curSpots.length==0){cO.platform.style.cursor=cO.config.defaultCursor}else{cO.platform.style.cursor="pointer"}if(cO.temp._moved){var cT=cQ(cU,new bg("ondragend").inherit(cU));cO.dispatchEvent(cT);cO._processInertialDragging(new bu(cR,cV))}cS._moved=false}cS.operating=false;if(cU.button==2){cS.tpx=null;cS.tpy=null;cS.mox=0;cS.moy=0}});function cM(cU){var cT=cO.temp,cS=!cT.mox&&!cT.moy;if(cT.rightDblclickTimer){clearTimeout(cT.rightDblclickTimer);cT.rightDblclickTimer=null;if(cS){cO.dispatchEvent(cQ(cU,new bg("onrightclick").inherit(cU)));cO.currentOperation|=bn.dblclick;cO.dispatchEvent(cQ(cU,new bg("onrightdblclick").inherit(cU)));cO.currentOperation^=bn.dblclick}}else{if(cS){cO.dispatchEvent(cQ(cU,new bg("onrightclick").inherit(cU)))}var cR=cQ(cU,new bg("onrightclickex").inherit(cU));cT.rightDblclickTimer=setTimeout(function(){cT.rightDblclickTimer=null;if(cS){cO.dispatchEvent(cR)}},cO.config.clickInterval)}}if(a8.browser.firefox>=4){a8.on(cO.container,"mouseup",function(cR){if(cR.button==2){cM(cR)}});a8.on(cO.container,"contextmenu",function(cR){co(cR)})}else{a8.on(cO.container,"contextmenu",function(cR){cM(cR);co(cR)})}var T=new Date();function cL(cU){if(cO.config.enableWheelZoom){var cT=cO.temp;if(cT.dragAni){cT.dragAni.stop()}cO.currentOperation|=bn.mousewheel;var cU=window.event||cU;cO.lastLevel=cO.zoomLevel;var cS=new bg("onmousewheel");cS.trend=cU.wheelDelta>=0||cU.detail<0;var cR=new Date();if(cS.trend==true&&cO.zoomLevel==cO.getMapType().getMaxZoom()||cS.trend==false&&cO.zoomLevel==cO.getMapType().getMinZoom()||cR-T<220){cO.currentOperation^=bn.mousewheel;cC(cU);return}T=cR;cQ(cU,cS.inherit(cU));cO.dispatchEvent(cS);cO.currentOperation^=bn.mousewheel;cC(cU)}}a8.on(cO.container,"mousewheel",cL);if(window.addEventListener){cO.container.addEventListener("DOMMouseScroll",cL,false)}a8.on(cO.platform,"click",function(cV){var cT=new bg("onclick"),cU=new bg("onclickex"),cS=cO.temp;cQ(cV,cT.inherit(cV));cQ(cV,cU.inherit(cV));if(!cO.currentOperation){var cR=!cS.mox&&!cS.moy;if(cR){cO.dispatchEvent(cT)}if(!cS._clickTimer){cS._clickTimer=setTimeout(function(){cS._clickTimer=null;if(cR){cO.dispatchEvent(cU)}},cO.config.clickInterval)}}cS.tpx=null;cS.tpy=null;cS.mox=0;cS.moy=0});a8.on(cO.platform,"dblclick",function(cS){if(!cO.currentOperation){cO.currentOperation|=bn.dblclick;if(a8.browser.ie){cO.dispatchEvent(cQ(cS,new bg("onclick").inherit(cS)))}var cR=cO.temp;if(cR._clickTimer){clearTimeout(cR._clickTimer);cR._clickTimer=null}cO.dispatchEvent(cQ(cS,new bg("ondblclick").inherit(cS)));cO.currentOperation^=bn.dblclick}});a8.on(document,"mousedown",function(cT){var cT=window.event||cT;var cS=cT.srcElement||cT.target;var cR=cO.temp;if(cR.canKeyboard){cR.canKeyboard=a8.dom.contains(cP.container,cS)}else{cR.canKeyboard=a8.dom.contains(cP.platform,cS)}})});bz.prototype._processInertialDragging=function(c2){var c5=this;if(!c5.config.enableInertialDragging){c5.dispatchEvent(new bg("onmoveend"));return}var cT=c5.temp;var cL=aE();if(cL-cT.lastDomMoveTime>100){c5.dispatchEvent(new bg("onmoveend"));cT._moved=false;return}else{var c3=cT.dragStartPos,cW=c2,T=ct(c3,cW),c0=[cW.x-c3.x>0?1:-1,cW.y-c3.y>0?1:-1],cN=(cL-cT.dragStartTime)/1000,cR=T/cN/2,cS=0.5,c1=cR/(2*cS),cZ=0.4*c1*cR/1000,cQ=Math.abs(c3.x-cW.x),cP=Math.abs(c3.y-cW.y),cX=0,cU=0;if(cP==0){cX=cQ}else{var cO=Math.abs(c3.x-cW.x)/Math.abs(c3.y-cW.y);cU=Math.round(Math.sqrt(cZ*cZ/(1+cO*cO)));cX=Math.round(cO*cU)}if(c0[0]==-1){cX=-cX}if(c0[1]==-1){cU=-cU}if(cT.dragAni){cT.dragAni.stop()}var cM=cR/1000,cY=c5.offsetX,cV=c5.offsetY,c4=false;cT.dragAni=new g({duration:c1,fps:30,transition:function(c6){var c6=c6*cM/(2*cS);return cM*c6-cS*c6*c6},render:function(c6){c6=c6*(4*cS)/(cM*cM);c4=!c4;c5._setPlatformPosition(cY+Math.round(c6*cX),cV+Math.round(c6*cU),null,c4)},finish:function(){cT.dragAni=null;c5._setPlatformPosition(cY+Math.round(cX),cV+Math.round(cU));c5.dispatchEvent(new bg("onmoveend"))},onStop:function(c6){cT.dragAni=null;c6=c6*(4*cS)/(cM*cM);c5._setPlatformPosition(cY+Math.round(c6*cX),cV+Math.round(c6*cU));setTimeout(function(){c5.dispatchEvent(new bg("onmoveend"))},1)}})}};BMap.register(function(cL){var T=cL;a8.on(document,"keydown",function(cM){if(T.temp.stopArrow==true){T.temp.stopArrow=false}if(T.config.enableKeyboard&&T.temp.canKeyboard){var cM=window.event||cM;switch(cM.keyCode||cM.which){case 43:case 189:case 109:T.temp.operating=true;T.dispatchEvent(new bg("onminuspress"));break;case 43:case 61:case 187:case 107:T.temp.operating=true;T.dispatchEvent(new bg("onpluspress"));break;case 33:T.temp.operating=false;T.temp.stopArrow=true;T.panBy(0,Math.round(T.height*0.8));co(cM);break;case 34:T.temp.operating=false;T.temp.stopArrow=true;T.panBy(0,-Math.round(T.height*0.8));co(cM);break;case 35:T.temp.operating=false;T.temp.stopArrow=true;T.panBy(-Math.round(T.width*0.8),0);co(cM);break;case 36:T.temp.operating=false;T.temp.stopArrow=true;T.panBy(Math.round(T.width*0.8),0);co(cM);break;case 37:T.temp.operating=true;T.temp.arrow|=1;T._arrow();co(cM);break;case 38:T.temp.operating=true;T.temp.arrow|=2;T._arrow();co(cM);break;case 39:T.temp.operating=true;T.temp.arrow|=4;T._arrow();co(cM);break;case 40:T.temp.operating=true;T.temp.arrow|=8;T._arrow();co(cM);break}}});a8.on(document,"keyup",function(cM){if(T.config.enableKeyboard){var cM=window.event||cM;switch(cM.keyCode||cM.which){case 37:T.temp.arrow=T.temp.arrow&~1;if(T.temp.arrow!=0){T._arrow()}break;case 38:T.temp.arrow=T.temp.arrow&~2;if(T.temp.arrow!=0){T._arrow()}break;case 39:T.temp.arrow=T.temp.arrow&~4;if(T.temp.arrow!=0){T._arrow()}break;case 40:T.temp.arrow=T.temp.arrow&~8;if(T.temp.arrow!=0){T._arrow()}break}}T.temp.operating=false});bz.prototype._arrow=function(){if(this._arrow.occurrent&&this._arrow._lastArrow==this.temp.arrow&&this.temp.stopArrow==true){return}var cM=this;var cO=cM.temp.arrow;cM._arrow._lastArrow=cO;cM._arrow.interval=30;cM._arrow.duration=999;cM._arrow.dx=cM._arrow.dy=0;if(cO&1){cM._arrow.dx=1}if(cO&2){cM._arrow.dy=1}if(cO&4){cM._arrow.dx=-1}if(cO&8){cM._arrow.dy=-1}if(cO&1&&cO&4){cM._arrow.dx=0}if(cO&2&&cO&8){cM._arrow.dy=0}if(!cM._arrow.occurrent){cM._arrow.occurrent=true;cM._arrow.time=aE();cM._arrow.beginTime=cM._arrow.time;cM.dispatchEvent(new bg("onmovestart"));var cN=new g({fps:cM._arrow.interval,duration:cM._arrow.duration,transition:aw.linear,render:function(cV){var cQ=cM._arrow;var cT=cM.temp.arrow;if(cM._arrow._lastArrow!=cT){cM._arrow._lastArrow=cT;if(cT&1){cQ.dx=1}if(cT&2){cQ.dy=1}if(cT&4){cQ.dx=-1}if(cT&8){cQ.dy=-1}if(cT&1&&cT&4){cQ.dx=0}if(cT&2&&cT&8){cQ.dy=0}}if(cM.temp.stopArrow==true){cQ.dx=0;cQ.dy=0}var cU=aE();var cS=Math.pow((cU-cQ.beginTime)/cQ.duration,2);if(!cM.temp.arrow){cQ.occurrent=false;cN.terminative=true;cM._arrow.time=aE();setTimeout(function(){cM.dispatchEvent(new bg("onmoveend"))},40)}var cW=(cU-cQ.time);var cR=cQ.dx*cW*cS>=0?Math.ceil(cQ.dx*cW*cS):Math.floor(cQ.dx*cW*cS);var cP=cQ.dy*cW*cS>=0?Math.ceil(cQ.dy*cW*cS):Math.floor(cQ.dy*cW*cS);if(cR!=0&&cP!=0){cR=Math.round(cR*0.7);cP=Math.round(cP*0.7)}cQ.time=cU;cM._setPlatformPosition(cM.offsetX+cR,cM.offsetY+cP)},finish:function(){cM._arrow.time=aE();setTimeout(function(){cM._arrowPan()},cM._arrow.interval)}})}};bz.prototype._arrowPan=function(){var cP=this;var cN=cP._arrow;if(cP.temp.stopArrow){cN.dx=0;cN.dy=0}if(!cP.temp.arrow){cN.occurrent=false;cP.dispatchEvent(new bg("onmoveend"));return}var cQ=aE();var cR=(cQ-cN.time);var cO=Math.ceil(cN.dx*cR);var cM=Math.ceil(cN.dy*cR);cN.time=cQ;cP._setPlatformPosition(cP.offsetX+cO,cP.offsetY+cM);setTimeout(function(){cP._arrowPan()},cN.interval)}});');
﻿_jsload&&_jsload('tile', 'a8.extend(bR.prototype,{initialize:function(){var T=this,cL=T.map;cL.addEventListener("loadcode",function(){T.loadTiles()});cL.addEventListener("addtilelayer",function(cM){T.addTileLayer(cM)});cL.addEventListener("removetilelayer",function(cM){T.removeTileLayer(cM)});cL.addEventListener("setmaptype",function(cM){T.setMapType(cM)});cL.addEventListener("zoomstartcode",function(cM){T._zoom(cM)});T._addOtherEvt(cL)},loadTiles:function(){var T=this;if(a8.browser.ie){try{document.execCommand("BackgroundImageCache",false,true)}catch(cL){}}if(T.zoomsDiv){a8.dom.hide(T.zoomsDiv)}if(!this.loaded){T.initMapTypeTiles();this.loaded=true}T.moveGridTiles()},_asyncLoadTiles:function(){var T=this,cL=T.map;T._addOtherEvt(cL);T.loadTiles()},_addOtherEvt:function(cL){var T=this;cL.addEventListener("mousewheel",function(cM){T.mouseWheel(cM)});cL.addEventListener("dblclick",function(cM){T.dblClick(cM)});cL.addEventListener("rightdblclick",function(cM){T.dblClick(cM)});cL.addEventListener("minuspress",function(cM){T.keypress(cM)});cL.addEventListener("pluspress",function(cM){T.keypress(cM)});cL.addEventListener("moving",function(cM){T.moveGridTiles()});cL.addEventListener("resize",function(cM){T.moveGridTiles()})},addTileLayer:function(cN){var cM=this;var T=cN.target;for(var cL=0;cL<cM.tileLayers.length;cL++){if(cM.tileLayers[cL]==T){return}}cM.tileLayers.push(T);T.initialize(this.map,this._normalLayerContainer);if(cM.map.loaded){cM.moveGridTiles()}},removeTileLayer:function(cQ){var cR=this;var cO=cQ.target;var cM=cO.mapType;var cL=cR.mapTiles;var cT=cR.bufferTiles;for(var T in cT){var cS=T.split("-")[1];if(cS==cO.guid){delete cT[T]}}for(var T in cL){var cS=T.split("-")[1];if(cS==cO.guid){delete cL[T]}}for(var cP=0,cN=cR.tileLayers.length;cP<cN;cP++){if(cO==cR.tileLayers[cP]){cR.tileLayers.splice(cP,1)}}cO.remove();cR.moveGridTiles()},mouseWheel:function(cO){var cN=this.map;if(!cN.config.enableWheelZoom){return}var cP=cN.zoomLevel+(cO.trend==true?1:-1);var cM=cN._getProperZoom(cP);if(cM.exceeded){return}cN.dispatchEvent(new bg("onzoomstart"));cN.lastLevel=cN.zoomLevel;cN.zoomLevel=cM.zoom;var T=cO.pixel;var cL=this._getMercatorCenter(T);zoomUnits=cN.getMapType().getZoomUnits(cN.getZoom());cN.mercatorCenter=new cd(cL.lng+(cN.width/2-T.x)*zoomUnits,cL.lat-(cN.height/2-T.y)*zoomUnits);cN.centerPoint=cN.projection.mercatorToLngLat(cN.mercatorCenter,cN.currentCity);this.zoom(T)},dblClick:function(cR){var cN=this.map;if(!cN.config.enableDblclickZoom){return}var cS=cR.pixel;var cO=1;var cL=cS;var cP=new aH(0,0);if(cR.type=="onrightdblclick"){cO=-1;cL=new bu(cN.width/2,cN.height/2)}var cM=cN.zoomLevel+cO;var cQ=cN._getProperZoom(cM);if(!cQ.exceeded){cN.dispatchEvent(new bg("onzoomstart"));cN.lastLevel=cN.zoomLevel;cN.zoomLevel=cQ.zoom;if(cO==1){cN.mercatorCenter=this._getMercatorCenter(cS);cN.centerPoint=cN.projection.mercatorToLngLat(cN.mercatorCenter,cN.currentCity);var T=cN.getMapType().getZoomUnits(cN.lastLevel)/cN.getMapType().getZoomUnits(cQ.zoom)*0.5;cP.width=cS.x-Math.round(cN.width/2)*T;cP.height=cS.y-Math.round(cN.height/2)*T}this.zoom(cL,cP)}else{if(cO==1){var cT=cN.pixelToPoint(cS);cN.panTo(cT)}}},keypress:function(cO){var cN=this.map;if(!cN.config.enableContinuousZoom){cO.type=="onpluspress"?cN.zoomIn():cN.zoomOut();return}if(this._zTimeLine){return}var cP=cN.zoomLevel+(cO.type=="onpluspress"?1:-1);var cM=cN._getProperZoom(cP);if(cM.exceeded){return}cN.dispatchEvent(new bg("onzoomstart"));var T=new bu(cN.width/2,cN.height/2);cN.lastLevel=cN.zoomLevel;cN.zoomLevel=cM.zoom;if(cN.getInfoWindow()){T=cN.pointToPixel(cN.getInfoWindow().getPosition(),cN.lastLevel);var cL=cN.pixelToPoint(T,cN.lastLevel);cN._updateCenterPoint(cN.width/2-T.x,cN.height/2-T.y,cL,true)}this.zoom(T)},_getMercatorCenter:function(T){var cP=this.map;var cN=cP.mercatorCenter;var cL=cP.getMapType().getZoomUnits(cP.lastLevel);var cM=cN.lng+cL*(T.x-cP.width/2);var cO=cN.lat-cL*(T.y-cP.height/2);return new cd(cM,cO)},zoom:function(cN,cM){var cP=cM?cM.width:0;var cO=cM?cM.height:0;var cZ=this.map;var c1=cZ.config;var c0=this;var cY={x:cN.x-parseInt(c0.tilesDiv.style.left)-cZ.offsetX,y:cN.y-parseInt(c0.tilesDiv.style.top)-cZ.offsetY};if(cZ.overlayDiv){a8.dom.hide(cZ.overlayDiv)}var T;c0._normalLayerContainer.style.visibility="hidden";this._showAvatarDivs();c0._mapTypeLayerContainer.style.visibility="hidden";var cX=[];var cQ=cZ.zoomLevel-cZ.lastLevel;if(!c0._diff){c0._diff=cQ}else{if(cZ.zoomLevel-cZ.lastLevel>0){c0._diff++}else{c0._diff--}}if(this._zTimeLine&&this._zTimeLine.schedule==0){this._zTimeLine.stop();this._zTimeLine=null;cQ=c0._diff}if((!cM||cM.width==0&&cM.height==0)&&c1.enableContinuousZoom){cZ.temp.zoomer.action(cN,cQ>0?true:false)}var cR=Math.pow(2,cQ);var cL=this.zoomsDiv;var cS=cL.style;cS.visibility="";var cW=cL.children.length;for(var cV=cW-1;cV>-1;cV--){var cT={};var cU=cL.children[cV].style;cT.top=parseInt(cU.top)||0;cT.left=parseInt(cU.left)||0;cT.width=parseInt(cU.width);cT.height=parseInt(cU.height);cT.dw=cT.width*cR-cT.width;cT.dh=cT.height*cR-cT.height;cT.dx=(cT.left-cY.x)*cR-(cT.left-cY.x);cT.dy=(cT.top-cY.y)*cR-(cT.top-cY.y);cX[cV]=cT}cL._ol=parseInt(cL.style.left);cL._ot=parseInt(cL.style.top);if(this._zTimeLine){this._zTimeLine.stop();this._zTimeLine=null}this._zTimeLine=new g({fps:20,duration:c1.enableContinuousZoom?c1.zoomerDuration:1,transition:aw.easeInQuad,render:function(c4){if(c4<0.1){return}for(var c3=cX.length-1;c3>-1;c3--){var c5=cX[c3];if(cL.children[c3]){var c2=cL.children[c3].style;c2.top=Math.round(c5.top+c5.dy*c4)+"px";c2.left=Math.round(c5.left+c5.dx*c4)+"px";c2.width=Math.ceil(c5.width+c5.dw*c4)+"px";c2.height=Math.ceil(c5.height+c5.dh*c4)+"px"}}if(cP||cO){cS.left=cL._ol-(cP*c4)+"px";cS.top=cL._ot-(cO*c4)+"px"}},finish:function(){c0.moveGridTiles();if(cZ.overlayDiv){if(a8.browser.ie&&a8.browser.ie<8||document.compatMode=="BackCompat"){a8.dom.show(cZ.overlayDiv)}else{setTimeout(function(){a8.dom.show(cZ.overlayDiv)},100)}}c0._normalLayerContainer.style.visibility="";c0._mapTypeLayerContainer.style.visibility="";delete c0._diff;cZ.dispatchEvent(new bg("onzoomend"));cL=null;c0._zTimeLine=null}})},setMapType:function(cQ){var cN=this;var cP=cN.map;this._showAvatarDivs(true);cP.addEventListener("tilesloaded",function(){setTimeout(function(){cN._removeAvatarDivs()},200);cP.removeEventListener("tilesloaded",arguments.callee)});for(var cM in this.mapTiles){this.hideTile(this.mapTiles[cM])}for(var cM in this.bufferTiles){this.hideTile(this.bufferTiles[cM])}var cO=this.mapTypeLayers;for(var cL=0,T=cO.length;cL<T;cL++){cO[cL].remove()}delete this.tilesDiv;this.mapTypeLayers=[];this.bufferTiles=this.mapTiles={};this.initMapTypeTiles();this.moveGridTiles()},_showAvatarDivs:function(cM){var cL=this.map;cM=cM||false;if(!this.zoomsDiv){if(!cM){this.zoomsDiv=this.tilesDiv.cloneNode(true)}else{this.zoomsDiv=this._mapTypeLayerContainer.cloneNode(true)}}else{if(this.zoomsDiv.parentNode&&!this._zTimeLine){this.zoomsDiv.parentNode.removeChild(this.zoomsDiv);this.zoomsDiv=null;if(!cM){this.zoomsDiv=this.tilesDiv.cloneNode(true)}else{this.zoomsDiv=this._mapTypeLayerContainer.cloneNode(true)}}}var T=this.zoomsDiv,cN=T.style;cN.display="";cN.zIndex=cN.zIndex-100;cL.platform.insertBefore(T,cL.platform.firstChild)},_removeAvatarDivs:function(){var T=this;if(T.zoomsDiv){J(T.zoomsDiv);if(T.zoomsDiv.parentNode){T.zoomsDiv.parentNode.removeChild(T.zoomsDiv);T.zoomsDiv.innerHTML="";T.zoomsDiv=null}}}});');
﻿_jsload&&_jsload('control', 'a8.extend(L.prototype,{_asyncDraw:function(){if(this._map){this._i(this._map)}},initialize:function(T){cp.prototype.initialize.call(this,T);this._initParam();this._setParam();this._render();this._bind();this._setSliderZoomLv(T.getZoom());this._bindMapEvent(T);return this._container},_initParam:function(){if(this._init){return}this._init=true;this._maxTotalZoomLv=19;this._minZoomLevel=-1;this._maxZoomLevel=-1;this._totalZoomLv=-1;this._sliderInterval=6;this._minBarY=1;this._maxBarY=-1;this._curBarY=-1;this._zoomDom=null;this._zoomBtnDom=null;this._sliderDom=null;this._sliderBaseDom=null;this._cZIndex="1100"},_bindMapEvent:function(cL){var T=this;cL.addEventListener("zoomend",function(cM){if(!T._map){return}T._setSliderZoomLv(T._map.getZoom());if(T._msover){return}if(T._isShowLevelHint){T._hideTimerId=setTimeout(function(){T._hideLevelHint()},1000)}});cL.addEventListener("mousewheel",function(){if(!T._map){return}if(T._map.config.enableWheelZoom&&T._isShowLevelHint){if(T._hideTimerId){clearTimeout(T._hideTimerId);T._hideTimerId=null}T._showLevelHint()}});cL.addEventListener("load",function(cM){if(!T._map){return}T._setSliderZoomLv(T._map.getZoom())});cL.addEventListener("maptypechange",function(cM){if(!T._map){return}T.redraw()});cL.addEventListener("zoomspanchange",function(cM){if(!T._map){return}T.redraw()})},redraw:function(){this._setParam();this.setType(this._opts.type);if(this._map){this._setSliderZoomLv(this._map.getZoom())}},_setParam:function(){var T=this._map.getMapType();this._minZoom=this._map.config.minZoom;this._maxZoom=this._map.config.maxZoom;if(T==BMAP_PERSPECTIVE_MAP||this._minZoom!=T.getMinZoom()||this._maxZoom!=T.getMaxZoom()){this._isShowLevelHint=false}else{this._isShowLevelHint=true}if(this._container){this._container.style.width=this._getControlHeight(0).width+"px"}if(!this._opts.showZoomInfo){this._isShowLevelHint=false}this._totalZoomLv=this._maxZoom-this._minZoom+1;this._maxBarY=this._minBarY+(this._totalZoomLv-1)*this._sliderInterval},_render:function(){cp.prototype._render.call(this);var cM=a8.browser.ie==6?" BMap_ie6":"";var cL=" BMap_stdMpType"+this._opts.type;var cN=this._container;a8.dom.addClass(cN,"BMap_stdMpCtrl");a8.dom.addClass(cN,cM);a8.dom.addClass(cN,cL);cN.innerHTML=this._generateHtml(this._opts.type);this._setSliderBarCursor(a8.browser.opera?"pointer":cc.defaultCursor);this._panBtnContainer=a8.g(cN.children[0]);this._zoomDom=a8.g(cN.children[1]);var T=this._zoomDom;this._btnZoomIn=a8.g(T.children[0]);this._btnZoomOut=a8.g(T.children[1]);this._sliderDom=a8.g(T.children[2]);this._sliderBaseDom=a8.g(T.children[2].children[0]);this._sliderBotDom=a8.g(T.children[2].children[1]);this.setType(this._opts.type)},_setSliderHeight:function(){var cS=this._opts.type;var T=this._getControlHeight(cS);var cQ=T.width;var cL=T.height;var cO=T.zoomHeight;var cR=T.zoomWidth;var cM=T.sliderHeight;var cP=T.sliderCHeight;var cN=(this._maxTotalZoomLv-this._totalZoomLv)*this._sliderInterval;if(this._opts.type==BMAP_NAVIGATION_CONTROL_LARGE){cL=cL-cN>=0?cL-cN:0;cO=cO-cN>=0?cO-cN:0;cM=cM-cN>=0?cM-cN:0}this._container.style.width=cQ+"px";this._container.style.height=cL+"px";this._zoomDom.style.height=cO+"px";this._zoomDom.style.width=cQ+"px";this._btnZoomOut.style.top=cO-21+"px";this._sliderDom.style.height=cM+"px";this._sliderBaseDom.style.height=cM+"px";if(this._opts.type==BMAP_NAVIGATION_CONTROL_ZOOM){this._zoomDom.children[0].style.left=this._zoomDom.children[1].style.left="0"}else{this._zoomDom.children[0].style.left=this._zoomDom.children[1].style.left=""}},_getControlHeight:function(cL){var T=62;if(!this._opts.showZoomInfo||this._map.getMapType()==BMAP_PERSPECTIVE_MAP){T=37}var cM=[{width:T,height:204,zoomHeight:159,zoomWidth:37,sliderHeight:120,sliderCHeight:120},{width:37,height:97,zoomHeight:42,zoomWidth:37,sliderHeight:0,sliderCHeight:0},{width:37,height:57,zoomHeight:0,zoomWidth:0,sliderHeight:0,sliderCHeight:0},{width:22,height:42,zoomHeight:42,zoomWidth:18,sliderHeight:0,sliderCHeight:0}];return cM[cL]},_generateHtml:function(){var T=[];T.push(this._generatePanHtml());T.push(this._generateZoomContainerHtml());return T.join("")},_generatePanHtml:function(){var T=\'<div class="BMap_stdMpPan"><div class="BMap_button BMap_panN" title="\u5411\u4e0a\u5e73\u79fb"></div><div class="BMap_button BMap_panW" title="\u5411\u5de6\u5e73\u79fb"></div><div class="BMap_button BMap_panE" title="\u5411\u53f3\u5e73\u79fb"></div><div class="BMap_button BMap_panS" title="\u5411\u4e0b\u5e73\u79fb"></div><div class="BMap_stdMpPanBg BMap_smcbg"></div></div>\';return T},_generateZoomContainerHtml:function(){var T=\'<div class="BMap_stdMpZoom">\'+this._generateZoomHtml()+this._generateSliderHtml()+this._generateZoomBalloonHtml()+"</div>";return T},_generateZoomHtml:function(){var T=\'<div class="BMap_button BMap_stdMpZoomIn" title="\u653e\u5927\u4e00\u7ea7"><div class="BMap_smcbg"></div></div><div class="BMap_button BMap_stdMpZoomOut" title="\u7f29\u5c0f\u4e00\u7ea7"><div class="BMap_smcbg"></div></div>\';return T},_generateSliderHtml:function(){var T=\'<div class="BMap_stdMpSlider"><div class="BMap_stdMpSliderBgTop"><div class="BMap_smcbg"></div></div><div class="BMap_stdMpSliderBgBot"></div><div class="BMap_stdMpSliderMask" title="\u653e\u7f6e\u5230\u6b64\u7ea7\u522b"></div><div class="BMap_stdMpSliderBar" title="\u62d6\u52a8\u7f29\u653e"><div class="BMap_smcbg"></div></div></div>\';return T},_generateZoomBalloonHtml:function(){var T=\'<div class="BMap_zlHolder"><div class="BMap_zlSt"><div class="BMap_smcbg"></div></div><div class="BMap_zlCity"><div class="BMap_smcbg"></div></div><div class="BMap_zlProv"><div class="BMap_smcbg"></div></div><div class="BMap_zlCountry"><div class="BMap_smcbg"></div></div></div>\';return T},_getElementByClassNamePattern:function(cO){var cN=this._getElementByClassNamePattern;if(typeof(cN.cache)==="undefined"){cN.cache=me._container.getElementsByTagName("*")}var cL=cN.cache,cM=null,T=cL.length,cP=null;for(cM=0;cM<T;++cM){cP=cL[cM];if(cP.className.toString().match(cO)){return cP}}throw Error(String(cO)+" Not found!")},setType:function(T){if(aK(T)&&T>=BMAP_NAVIGATION_CONTROL_LARGE&&T<=BMAP_NAVIGATION_CONTROL_ZOOM){this._opts.type=T}else{this._opts.type=BMAP_NAVIGATION_CONTROL_LARGE}if(!this._map||!this._container){return}var cL=this._container;cL.className=cL.className.replace(/BMap_stdMpType[0-3]*/,"BMap_stdMpType"+this._opts.type);this._setSliderHeight();if(T!=BMAP_NAVIGATION_CONTROL_LARGE){a8.dom.removeClass(cL.children[1].children[2],"hvr")}this.setAnchor(this._opts.anchor)},getType:function(){return this._opts.type},_bind:function(){var cR=this;var cQ=cR._container;a8.on(this._zoomDom,"mouseover",function(){if(!cR._isShowLevelHint){return}cR._msover=true;if(cR._hideTimerId){clearTimeout(cR._hideTimerId);cR._hideTimerId=null}cR._showLevelHint()});a8.on(this._zoomDom,"mouseout",function(){if(!cR._isShowLevelHint){return}if(cR._hideTimerId){clearTimeout(cR._hideTimerId)}cR._msover=false;cR._hideTimerId=setTimeout(function(){cR._hideLevelHint();cR._hideTimerId=null},1000)});a8.on(cQ.children[0],"mouseover",function(){cR._hideLevelHint(true)});a8.on(cQ.children[0].children[0],"click",function(){cR._panBy(0,Math.round(cR._map.height/3))});a8.on(cQ.children[0].children[1],"click",function(){cR._panBy(Math.round(cR._map.width/3),0)});a8.on(cQ.children[0].children[2],"click",function(){cR._panBy(-Math.round(cR._map.width/3),0)});a8.on(cQ.children[0].children[3],"click",function(){cR._panBy(0,-Math.round(cR._map.height/3))});a8.on(cQ.children[0].children[0],"mouseover",function(){cR._panBtnContainer.style.backgroundPosition="0 -44px"});a8.on(cQ.children[0].children[1],"mouseover",function(){cR._panBtnContainer.style.backgroundPosition="0 -176px"});a8.on(cQ.children[0].children[2],"mouseover",function(){cR._panBtnContainer.style.backgroundPosition="0 -88px"});a8.on(cQ.children[0].children[3],"mouseover",function(){cR._panBtnContainer.style.backgroundPosition="0 -132px"});a8.on(cR._panBtnContainer,"mouseout",function(){cR._panBtnContainer.style.backgroundPosition="0 0"});var cO=cQ.children[1].children;a8.on(cO[0],"click",function(){cR._zoomIn()});a8.on(cO[1],"click",function(){cR._zoomOut()});for(var cM=0;cM<5;cM++){a8.on(cQ.children[0].children[cM],"mouseup",function(cT){if((cR._map.currentOperation&bn.stdMapCtrlDrag)==0&&(cR._map.currentOperation&bn.drag)==0){aP(cT)}});a8.on(cQ.children[0].children[cM],"contextmenu",function(cT){cC(cT)});a8.on(cQ.children[0].children[cM],"click",function(cT){cC(cT)})}a8.on(cO[0],"mouseup",function(cT){if((cR._map.currentOperation&bn.stdMapCtrlDrag)==0&&(cR._map.currentOperation&bn.drag)==0){aP(cT)}});a8.on(cO[1],"mouseup",function(cT){if((cR._map.currentOperation&bn.stdMapCtrlDrag)==0&&(cR._map.currentOperation&bn.drag)==0){aP(cT)}});a8.on(cO[0],"contextmenu",function(cT){cC(cT)});a8.on(cO[1],"contextmenu",function(cT){cC(cT)});var cS=cQ.children[1].children[2].children[2];a8.on(cS,"mouseup",function(cT){if((cR._map.currentOperation&bn.stdMapCtrlDrag)==0&&(cR._map.currentOperation&bn.drag)==0){aP(cT)}});a8.on(cO[0],"click",function(cT){aP(cT)});a8.on(cO[1],"click",function(cT){aP(cT)});a8.on(cO[0],"mouseover",function(){cO[0].style.backgroundPosition="0 -243px"});a8.on(cO[0],"mouseout",function(){if((cR._map.currentOperation&bn.stdMapCtrlDrag)==0){cO[0].style.backgroundPosition="0 -221px"}});a8.on(cO[1],"mouseover",function(){cO[1].style.backgroundPosition="0 -287px"});a8.on(cO[1],"mouseout",function(){if((cR._map.currentOperation&bn.stdMapCtrlDrag)==0){cO[1].style.backgroundPosition="0 -265px"}});a8.on(cS,"click",function(cT){aP(cT)});var cP=cQ.children[1].children[2].children[3];a8.on(cP,"mouseup",function(cT){if(cT.button==2){aP(cT)}});a8.on(cP,"contextmenu",function(cT){cC(cT)});a8.on(this._zoomDom.children[3].children[0],"click",function(cT){cR._map.zoomTo(18)});a8.on(this._zoomDom.children[3].children[1],"click",function(cT){cR._map.zoomTo(12)});a8.on(this._zoomDom.children[3].children[2],"click",function(cT){cR._map.zoomTo(8)});a8.on(this._zoomDom.children[3].children[3],"click",function(cT){cR._map.zoomTo(4)});a8.on(cS,"mousedown",function(cU){cU=window.event||cU;var cT=cU.layerY||cU.offsetY||0;var cV=0;cV=(cR._maxZoom+1)-Math.round(cR._totalZoomLv*parseFloat(cT/(cR._totalZoomLv*cR._sliderInterval)));cR._map.zoomTo(cV)});a8.on(cP,"mouseover",function(){a8.dom.addClass(cP,"h")});a8.on(cP,"mouseout",function(){if((cR._map.currentOperation&bn.stdMapCtrlDrag)==0){a8.dom.removeClass(cP,"h")}});var cN=function(cT){var cT=window.event||cT;if(cT.button==2){return}if((a8.browser.ie&&cT.button!=1)){return}if(cP.setCapture){cP.setCapture()}cR._map.currentOperation|=bn.stdMapCtrlDrag;cR._bind.my=cT.pageY||cT.clientY||0;if(!a8.browser.opera){cR._setSliderBarCursor(cc.draggingCursor)}a8.on(document,"mousemove",T);a8.on(document,"mouseup",cL);aP(cT);return cC(cT)};var T=function(cU){if((cR._map.currentOperation&bn.stdMapCtrlDrag)!=0){var cU=window.event||cU;var cV=cU.pageY||cU.clientY;var cT=cR._curBarY+cV-cR._bind.my;if(cT<cR._minBarY){cT=cR._minBarY}else{if(cT>cR._maxBarY){cT=cR._maxBarY}}cP.style.top=cT+"px";cR._sliderBotDom.style.top=cT+"px";cR._sliderBotDom.style.height=parseInt(cR._sliderBaseDom.style.height)-cT+4+"px"}};var cL=function(cU){if((cR._map.currentOperation&bn.stdMapCtrlDrag)!=0){var cT=a8.g(cP);cR._curBarY=parseInt(cT.style.top);a8.dom.removeClass(cP,"h");cR._map.currentOperation&=~bn.stdMapCtrlDrag;if(cQ&&cP&&cP.releaseCapture){cP.releaseCapture()}if(!a8.browser.opera){cR._setSliderBarCursor(cc.defaultCursor)}var cV=(cR._maxZoom+1)-Math.round(parseFloat(cR._curBarY-cR._minBarY)/parseFloat(cR._maxBarY-cR._minBarY)*(cR._totalZoomLv-1)+1);cR._map.zoomTo(cV);a8.un(document,"mousemove",T);a8.un(document,"mouseup",cL)}};a8.on(cP,"mousedown",cN)},_setSliderBarCursor:function(T){this._container.children[1].children[2].children[3].style.cursor=T},_panBy:function(cL,T){this._map.panBy(cL,T)},_zoomIn:function(){this._map.zoomIn()},_zoomOut:function(){this._map.zoomOut()},_setSliderZoomLv:function(cL){if(!this._container||this.getType()!=BMAP_NAVIGATION_CONTROL_LARGE){return}var T=(this._maxZoom-cL)*this._sliderInterval+this._minBarY;this._curBarY=T>this._maxBarY?this._maxBarY:T<this._minBarY?this._minBarY:T;this._container.children[1].children[2].children[3].style.top=this._curBarY+"px";this._sliderBotDom.style.top=this._curBarY+"px";this._sliderBotDom.style.height=parseInt(this._sliderBaseDom.style.height)-this._curBarY+4+"px"},_hideLevelHint:function(T){if(this._opts.type==0){a8.dom.removeClass(this._container.children[1].children[3],"hvr")}if(T&&this._hideTimerId){clearTimeout(this._hideTimerId);this._hideTimerId=null}},_showLevelHint:function(){if(this._opts.type==0&&this._opts.showZoomInfo){a8.dom.addClass(this._container.children[1].children[3],"hvr")}},show:function(){cp.prototype.show.call(this);if(a8.browser.ie<8){var T=this;setTimeout(function(){T.setType(T._opts.type)},1)}}});a8.extend(am.prototype,{_asyncDraw:function(){if(this._map){this._i(this._map)}},initialize:function(T){cp.prototype.initialize.call(this,T);this._render();this._updateState();this._bind(T);return this._container},_bind:function(cL){var T=this;cL.addEventListener("load",function(cM){T._updateState()});cL.addEventListener("moveend",function(cM){T._updateState()});cL.addEventListener("zoomend",function(cM){T._updateState()});cL.addEventListener("maptypechange",function(){if(T._container){T._container.style.color=T._map.getMapType().getTextColor()}})},_render:function(){cp.prototype._render.call(this);a8.dom.addClass(this._container,"BMap_cpyCtrl");var T=this._container.style;T.cursor="default";T.whiteSpace="nowrap";T.MozUserSelect="none";T.color=this._map.getMapType().getTextColor();T.background="none";T.font="11px/15px "+cc.fontFamily;cp.prototype._setPosition.call(this)},_updateState:function(){if(!this._map||!this._container||this._copyrightCollection.length==0){return}for(var cO=0,cM=this._copyrightCollection.length;cO<cM;cO++){var cL;var T=this._map.getZoom();var cS=this._map.pixelToPoint({x:0,y:0});var cR=this._map.pixelToPoint({x:this._map.width,y:this._map.height});var cQ=new bN(cS,cR);if(this._copyrightCollection[cO].bounds&&cQ.intersects(this._copyrightCollection[cO].bounds)==null){cL=false}else{cL=true}if(cL){if(this._container){var cT=false;for(var cN=0,cP=this._container.children.length;cN<cP;cN++){if(this._container.children[cN].getAttribute("_cid")==this._copyrightCollection[cO].id){cT=true;this._container.children[cN].style.display="inline";if(this._container.children[cN].innerHTML!=this._copyrightCollection[cO].content){this._container.children[cN].innerHTML=this._copyrightCollection[cO].content}break}}if(!cT){this._generateHTML(this._copyrightCollection[cO])}}}else{if(this._container){for(var cN=0;cN<this._container.children.length;cN++){if(this._container.children[cN].getAttribute("_cid")==this._copyrightCollection[cO].id&&this._container.children[cN].style.display!="none"){this._container.children[cN].style.display="none";return}}}}}},addCopyright:function(cM){if(!cM||!aK(cM.id)||isNaN(cM.id)){return}var T={bounds:null,content:""};for(var cL in cM){T[cL]=cM[cL]}var cN=this.getCopyright(cM.id);if(cN){for(var cO in T){cN[cO]=T[cO]}}else{this._copyrightCollection.push(T)}this._updateState()},getCopyright:function(cM){for(var cL=0,T=this._copyrightCollection.length;cL<T;cL++){if(this._copyrightCollection[cL].id==cM){return this._copyrightCollection[cL]}}},getCopyrightCollection:function(){return this._copyrightCollection},removeCopyright:function(cO){var cM;for(var cL=0,T=this._copyrightCollection.length;cL<T;cL++){if(this._copyrightCollection[cL].id==cO){cM=this._copyrightCollection.splice(cL,1);cL--;T=this._copyrightCollection.length}}var cN=this.getDom(cO);if(cN&&cN.parentNode){cN.parentNode.removeChild(cN);cN=null}this._updateState();return cM},_generateHTML:function(T){if(!this._container){return}this._container.innerHTML+="<span _cid=\'"+T.id+"\'>"+T.content+"</span>"},getDom:function(cN){var cM=cp.prototype.getDom.call(this);if(!ch(cN)){return cM}else{if(cM){for(var cL=0,T=cM.children.length;cL<T;cL++){if(cM.children[cL].getAttribute("_cid")==cN){return cM.children[cL]}}}}}});a8.object.extend(cK.prototype,{_asyncDraw:function(){if(this._map){this._i(this._map)}},initialize:function(T){cp.prototype.initialize.call(this,T);this._initParam();this._render();this._bind();this._initOverviewMap();a8.on(this._container,"click",aP);a8.on(this._container,"dblclick",aP);a8.on(this._container,"mousewheel",aP);a8.on(this._container,"mouseup",function(cL){cL=window.event||cL;if(cL.button==2){aP(cL)}});if(window.addEventListener){this._container.addEventListener("DOMMouseScroll",function(cL){aP(cL)},true)}return this._container},_initParam:function(){if(this._init){return}this._init=true;this._omCanvas;this._omMapContainer;this._omView;this._omViewMv;this._omBtn;this._borderWidth=1;this._quad=4;this._overviewMap=null;this._minZoom=-1;this._maxZoom=-1;this._curOMZoomLevel=-1;this._wRatio=1;this._hRatio=1;this._temp={};this._currentOp="";this._overviewInitialized=false},_getCurOMZoomLevel:function(){if(!this._map){return}var T=this._map.zoomLevel;var cL=T-this._opts.zoomInterval;if(cL<this._minZoom){return this._minZoom}else{if(cL>this._maxZoom){return this._maxZoom}else{return cL}}return -1},_render:function(){cp.prototype._render.call(this);var T=a8.browser.ie!=false?" BMap_ie"+a8.browser.ie:"";var cL=this._container;cL.innerHTML=this._generateHTML();a8.dom.addClass(cL,"BMap_omCtrl"+T);this._omCanvas=cL.children[0].children[0];this._omMapContainer=this._omCanvas.children[0];this._omViewMv=this._omCanvas.children[1];this._omViewMvInn=this._omViewMv.children[0];this._omBtn=cL.children[1];this.setSize(this._opts.size);this._omViewMv.style.cursor=cc.defaultCursor},_generateHTML:function(){var T=[\'<div class="BMap_omOutFrame"><div class="BMap_omInnFrame">\',\'<div class="BMap_omMapContainer"></div>\',\'<div class="BMap_omViewMv"><div class="BMap_omViewInnFrame"><div></div></div></div>\',\'</div></div><div class="BMap_omBtn"></div>\'];return T.join("")},_bind:function(){var T=this;a8.on(this._omBtn,"click",function(){T.changeView()});if(a8.browser.ie){a8.on(this._omBtn,"dblclick",function(){T.changeView()})}if(!a8.browser.ie||a8.browser.ie>6){this._omBtn.onmouseover=function(cL){a8.dom.addClass(T._omBtn,"hover")};this._omBtn.onmouseout=function(cL){a8.dom.removeClass(T._omBtn,"hover")}}a8.on(this._omViewMv,"mousedown",function(cL){if(T._omViewMv&&ch(T._omViewMv._drag)&&T._omViewMv._drag=="true"){return}cL=window.event||cL;if(cL.button==2){return}T._omViewMv._drag="true";var cL=window.event||cL;if(T._omViewMv.setCapture){T._omViewMv.setCapture()}T._bind.iniX=parseInt(aJ(T._omViewMv).left);T._bind.iniY=parseInt(aJ(T._omViewMv).top);T._bind.mx=cL.pageX||cL.clientX;T._bind.my=cL.pageY||cL.clientY;T._bind.i=0;T._bind.j=0;T._setViewMvCursor(cc.draggingCursor);aP(cL);return cC(cL)});a8.on(document,"mousemove",function(cR){if(T._omViewMv&&T._omViewMv._drag=="true"){var cR=window.event||cR;var cL=cR.pageX||cR.clientX;var cS=cR.pageY||cR.clientY;T._bind.curX=T._bind.iniX+cL-T._bind.mx;T._bind.curY=T._bind.iniY+cS-T._bind.my;var cQ=3;T._bind._moveX=0;T._bind._moveY=0;if(T._bind.curX<=0){T._bind._moveX=cQ}if(T._bind.curY<=0){T._bind._moveY=cQ}if(T._bind.curX+T._omViewMv.offsetWidth>=T._overviewMap.width){T._bind._moveX=-cQ}if(T._bind.curY+T._omViewMv.offsetHeight>=T._overviewMap.height){T._bind._moveY=-cQ}T._omViewMv.style.left=T._bind.curX+"px";T._omViewMv.style.top=T._bind.curY+"px";if((T._bind._moveX!=0||T._bind._moveY!=0)&&!T._bind.intervalId){T._bind._mapMoving=true;var cP=T._overviewMap.offsetX;var cN=T._overviewMap.offsetY;var cO=cP+T._bind._moveX;var cM=cN+T._bind._moveY;T._overviewMap._setPlatformPosition(cO,cM);T._bind.intervalId=setInterval(function(){var cU=T._bind._moveX!=0?(T._bind._moveX>0?T._bind.i+=cQ:T._bind.i-=cQ):T._bind.i;var cT=T._bind._moveY!=0?(T._bind._moveY>0?T._bind.j+=cQ:T._bind.j-=cQ):T._bind.j;T._overviewMap._setPlatformPosition(cO+cU,cM+cT)},30)}if(T._bind._moveX==0&&T._bind._moveY==0){clearInterval(T._bind.intervalId);delete T._bind.intervalId;T._bind.i=0;T._bind.j=0}aP(cR);return cC(cR)}});a8.on(document,"mouseup",function(cO){if(T._omViewMv&&T._omViewMv._drag=="true"){T._omViewMv._drag="";T._setViewMvCursor(cc.defaultCursor);if(T._omViewMv.releaseCapture){T._omViewMv.releaseCapture()}if(T._bind.initX==T._bind.curX&&T._bind.initY==T._bind.curY){aP(cO);return cC(cO)}T._currentOp="dragView";T._overviewMap.config.enableMouseDown=false;T._map.temp.operating=true;if(!ch(T._bind.curX)||!ch(T._bind.curY)){return}var cN=T._bind.curX+parseInt(T._omViewMv.style.width)/2+1;var cM=T._bind.curY+parseInt(T._omViewMv.style.height)/2+1;delete T._bind.curX;delete T._bind.curY;var cL=T._overviewMap.pixelToPoint({x:cN,y:cM},T._overviewMap.zoomLevel);T._map.temp.operating=false;if(T._bind._mapMoving==true){clearInterval(T._bind.intervalId);delete T._bind.intervalId;T._bind._mapMoving=false}T._map.temp.operating=true;setTimeout(function(){T._map.temp.operating=false;T._map.panTo(cL)},50);aP(cO);return cC(cO)}})},_initOverviewMap:function(){if(this._overviewInitialized==true){return}var cM=this;var cN=cM._map;cN.addEventListener("resize",function(){if(cM._overviewMap!=null){cM._overviewMap.setCenter(cN.getCenter())}if(cM._omView!=null){cM._omView.setPosition(cN.getCenter());cM._setRatio()}cM.setAnchor(cM._opts.anchor)});if(this._opts.isOpen==false){return}if(!this._binded){cN.addEventListener("loadcode",function(){cM._onMainZoomEnd()});cN.addEventListener("moving",function(){cM._moveView()});cN.addEventListener("moveend",function(cO){cM._onMainMoveEnd(cO)});cN.addEventListener("zoomend",function(cO){cM._onMainZoomEnd(cO)});cN.addEventListener("maptypechange",function(cO){cM._setRatio()});this._binded=true}var T=cN.getCenter();this._minZoom=BMAP_NORMAL_MAP.getMinZoom();this._maxZoom=BMAP_NORMAL_MAP.getMaxZoom();this._curOMZoomLevel=this._getCurOMZoomLevel();this._overviewMap=new bz(this._omMapContainer,{isOverviewMap:true});this._overviewMap.disableDoubleClickZoom();this._overviewMap.centerAndZoom(T,this._curOMZoomLevel);this._omView=new n({point:cN.getCenter(),lineStroke:1,lineColor:"#6688cc"});this._overviewMap.addOverlay(this._omView);this._omView.getContainer().innerHTML=\'<div class="BMap_omViewInnFrame"><div class="BMap_omViewMask"></div></div>\';this._omViewInn=this._omView.getContainer().children[0];var cL=this._omView.getContainer().style;cL.borderLeftColor="#84b0df";cL.borderTopColor="#adcff4";cL.borderRightColor="#274b8b";cL.borderBottomColor="#274b8b";this._setRatio();this._overviewMap.addEventListener("dragend",function(){cM._currentOp="dragMap";cN.panTo(cM._overviewMap.getCenter())});this._overviewMap.addEventListener("moveend",function(){cM._onViewMapMoveEnd()});this._overviewMap.addEventListener("mousedown",function(cO){cM._temp._downX=cO.offsetX;cM._temp._downY=cO.offsetY});this._overviewMap.addEventListener("resize",function(cO){if(cM._map&&cM._overviewMap){cM._overviewMap.setCenter(cM._map.getCenter())}cM._setRatio()});this._overviewInitialized=true},_setViewMvCursor:function(T){this._omViewMv.style.cursor=T},setAnchor:function(cO){cp.prototype.setAnchor.call(this,cO);if(!this._map){return}if(a8.browser.ie){var cR=this._map.width;var cN=this._map.height;var cL=this._opts.size.width;var cP=this._opts.size.height;var cM=this._opts.offset.width;var T=this._opts.offset.height;if(this._opts.isOpen==false){cL=this._btnWidth;cP=this._btnHeight}var cQ=this._container;switch(cO){case BMAP_ANCHOR_TOP_RIGHT:cQ.style.right="auto";cQ.style.left=cR-cL-cM+"px";break;case BMAP_ANCHOR_BOTTOM_LEFT:cQ.style.bottom="auto";cQ.style.top=cN-cP-T+"px";break;case BMAP_ANCHOR_BOTTOM_RIGHT:cQ.style.bottom="auto";cQ.style.right="auto";cQ.style.top=cN-cP-T+"px";cQ.style.left=cR-cL-cM+"px";break;default:break}}this._setQuad();this._redraw()},changeView:function(){this.changeView._running=true;this._opts.isOpen=!this._opts.isOpen;if(!this._container){this.changeView._running=false;return}var cP=this._container;var T=this._opts.size.width;var cM=this._opts.size.height;var cO=this._btnWidth;var cL=this._btnHeight;var cN=this;if(!this._opts.isOpen){this.changeView._preBtnTop=this._omBtn.style.top;this.changeView._preBtnLeft=this._omBtn.style.left;new g({fps:25,duration:120,transition:aw.easeInCubic,render:function(cQ){cP.style.width=(T-Math.ceil((T-cO)*cQ))+"px";cP.style.height=(cM-Math.ceil((cM-cL)*cQ))+"px";if(a8.browser.ie){cN._omBtn.style.top=(cN._quad==3||cN._quad==4)?parseInt(cP.style.height)-cL+"px":"0";cN._omBtn.style.left=(cN._quad==1||cN._quad==4)?parseInt(cP.style.width)-cO+"px":"0";if(cN._opts.anchor>=0&&cN._opts.anchor<=BMAP_ANCHOR_BOTTOM_RIGHT){if(cN._quad==3||cN._quad==4){cP.style.top=cN._map.height-parseInt(cP.style.height)-cN._opts.offset.height+"px"}if(cN._quad==1||cN._quad==4){cP.style.left=cN._map.width-parseInt(cP.style.width)-cN._opts.offset.width+"px"}}}cN.dispatchEvent(new bg("onviewchanging"))},finish:function(){if(a8.browser.ie){cN._omBtn.style.left="0";cN._omBtn.style.top="0";if(cN._opts.anchor>=0&&cN._opts.anchor<=BMAP_ANCHOR_BOTTOM_RIGHT){if(cN._quad==3||cN._quad==4){cP.style.top=cN._map.height-cN._btnHeight-cN._opts.offset.height+"px"}if(cN._quad==1||cN._quad==4){cP.style.left=cN._map.width-cN._btnWidth-cN._opts.offset.width+"px"}}}cN._redraw();a8.dom.addClass(cN._omBtn,"BMap_omBtnClosed");var cQ=new bg("onviewchanged");cQ.isOpen=cN._opts.isOpen;cN.dispatchEvent(cQ);cN.changeView._running=false}})}else{if(this._overviewInitialized==false){this._initOverviewMap()}new g({fps:40,duration:120,transition:aw.easeOutCubic,render:function(cQ){cP.style.width=(Math.ceil(T*cQ))+"px";cP.style.height=(Math.ceil(cM*cQ))+"px";if(a8.browser.ie){cN._omBtn.style.top=(cN._quad==3||cN._quad==4)?parseInt(cP.style.height)-cL+"px":"0";cN._omBtn.style.left=(cN._quad==1||cN._quad==4)?parseInt(cP.style.width)-cO+"px":"0";if(cN._opts.anchor>=0&&cN._opts.anchor<=BMAP_ANCHOR_BOTTOM_RIGHT){if(cN._quad==3||cN._quad==4){cP.style.top=cN._map.height-parseInt(cP.style.height)-cN._opts.offset.height+"px"}if(cN._quad==1||cN._quad==4){cP.style.left=cN._map.width-parseInt(cP.style.width)-cN._opts.offset.width+"px"}}}cN.dispatchEvent(new bg("onviewchanging"))},finish:function(){if(cN._opts.anchor>=0&&cN._opts.anchor<=BMAP_ANCHOR_BOTTOM_RIGHT){if(a8.browser.ie){if(cN._quad==3||cN._quad==4){cP.style.top=cN._map.height-cM-cN._opts.offset.height+"px"}if(cN._quad==1||cN._quad==4){cP.style.left=cN._map.width-T-cN._opts.offset.width+"px"}}}cN._redraw();cN._setBtnPosition();a8.dom.removeClass(cN._omBtn,"BMap_omBtnClosed");var cQ=new bg("onviewchanged");cQ.isOpen=cN._opts.isOpen;cN.dispatchEvent(cQ);cN.changeView._running=false}})}},_setRatio:function(){if(!this._map){return}var cQ=this._map.zoomLevel;var cS=this._map.pixelToPoint({x:0,y:0},cQ);var cN=this._map.pixelToPoint({x:this._map.width,y:this._map.height},cQ);var cL=this._overviewMap.pixelToPoint({x:0,y:0},this._curOMZoomLevel);var cM=this._overviewMap.pixelToPoint({x:this._overviewMap.width,y:this._overviewMap.height},this._curOMZoomLevel);this._wRatio=(cN.lng-cS.lng)/(cM.lng-cL.lng);this._hRatio=(cN.lat-cS.lat)/(cM.lat-cL.lat);if(this._wRatio>=1||this._hRatio>=1){this._omViewMv.style.display="none";this._omView.hide()}else{var cO=parseInt(this._overviewMap.width);var T=parseInt(this._overviewMap.height);var cR=Math.round(cO*this._wRatio);var cP=Math.round(T*this._hRatio);if(this._map.getMapType()==BMAP_PERSPECTIVE_MAP){cP=T*0.35}this._omView.show();this._omView.setDimension(cR,cP);this._omViewMv.style.display=""}this._setOMViewMvPos()},_setOMViewMvPos:function(){if(!this._omView||!this._omView.getContainer()){return}if(this._wRatio>=1||this._hRatio>=1){this._omViewMv.style.display="none";return}var cL=this._omView.getContainer().style;this._omViewMv.style.display="";this._omViewMv.style.width=cL.width;this._omViewMv.style.height=cL.height;var T=parseInt(cL.width)-2;var cM=parseInt(cL.height)-2;T=T<0?0:T;cM=cM<0?0:cM;this._omViewMvInn.style.width=T+"px";this._omViewMvInn.style.height=cM+"px";this._omViewInn.style.width=this._omViewMvInn.style.width;this._omViewInn.style.height=this._omViewMvInn.style.height;this._omViewMv.style.left=parseInt(cL.left)+this._overviewMap.offsetX+"px";this._omViewMv.style.top=parseInt(cL.top)+this._overviewMap.offsetY+"px"},setSize:function(cM){if(!(cM instanceof aH)){cM=new aH(150,150)}var cL=cM.width;var cN=cM.height;cL=cL>0?cL:150;cN=cN>0?cN:150;cM.width=cL;cM.height=cN;this._opts.size=cM;if(!this._container){return}if(this.changeView._running==true){var T=arguments;var cO=this;setTimeout(function(){T.callee.call(cO,cM)},120);return}N(this._container,[cL,cN]);var cO=this;setTimeout(function(){if(cO._overviewMap&&cO._map){cO._overviewMap.setCenter(cO._map.getCenter())}},100);this.setAnchor(this._opts.anchor);this.dispatchEvent(new bg("resize"))},setOffset:function(T){if(!(T instanceof aH)){return}cp.prototype.setOffset.call(this,T);if(!this._container){return}if(T.width!=0||T.height!=0){a8.dom.addClass(this._container,"withOffset")}else{a8.dom.removeClass(this._container,"withOffset")}},_redraw:function(){if(!this._omCanvas){return}var cM=this._opts.size.width;var cP=this._opts.size.height;var cR=this._opts.padding;var T=this._borderWidth;var cN=0;var cQ=0;var cL=0;var cO=0;this._omCanvas.style.left=this._omCanvas.style.top=this._omCanvas.style.right=this._omCanvas.style.bottom="auto";this._omBtn.style.left=this._omBtn.style.top=this._omBtn.style.right=this._omBtn.style.bottom="auto";if(this._opts.offset.width==0&&this._opts.offset.height==0){switch(this._opts.anchor){case BMAP_ANCHOR_TOP_LEFT:this._omCanvas.style.left="0px";this._omCanvas.style.top="0px";break;case BMAP_ANCHOR_TOP_RIGHT:this._omCanvas.style.left=cR+"px";this._omCanvas.style.top="0px";break;case BMAP_ANCHOR_BOTTOM_LEFT:this._omCanvas.style.top=cR+"px";this._omCanvas.style.left="0px";break;case BMAP_ANCHOR_BOTTOM_RIGHT:this._omCanvas.style.top=cR+"px";this._omCanvas.style.left=cR+"px";break;default:break}cN=cM-T;cQ=cP-T;cL=cN-cR-2*T;cO=cQ-cR-2*T;if(a8.browser.ie&&document.compatMode=="BackCompat"){cN=cN+1;cQ=cQ+1;cL=cL+2;cO=cO+2}}else{this._omCanvas.style.left=this._omCanvas.style.top=this._omCanvas.style.right=this._omCanvas.style.bottom=cR+"px";cN=cM-2*T;cQ=cP-2*T;cL=cN-2*cR-2*T;cO=cQ-2*cR-2*T;if(this._opts.offset.width!=0||this._opts.offset.height!=0){a8.dom.addClass(this._container,"withOffset")}if(a8.browser.ie&&document.compatMode=="BackCompat"){cN=cN+2;cQ=cQ+2;cL=cL+2;cO=cO+2}}if(cN>0&&cQ>0){N(this._container.children[0],[cN,cQ])}if(cL>0&&cO>0){N(this._omCanvas,[cL,cO])}this._setBtnPosition();a8.dom.removeClass(this._omBtn,"BMap_omBtnClosed");if(!this._opts.isOpen){this._container.style.width=this._btnWidth+"px";this._container.style.height=this._btnHeight+"px";if(a8.browser.ie){this.changeView._preBtnTop=this._omBtn.style.top;this.changeView._preBtnLeft=this._omBtn.style.left;this._omBtn.style.left="0";this._omBtn.style.top="0"}a8.dom.addClass(this._omBtn,"BMap_omBtnClosed")}},_setQuad:function(){var cL=this._container;if(!cL){return}var T=this._quad;switch(this._opts.anchor){case BMAP_ANCHOR_TOP_LEFT:this._quad=2;break;case BMAP_ANCHOR_TOP_RIGHT:this._quad=1;break;case BMAP_ANCHOR_BOTTOM_LEFT:this._quad=3;break;case BMAP_ANCHOR_BOTTOM_RIGHT:this._quad=4;break;default:break}a8.dom.removeClass(cL,"quad"+T);a8.dom.addClass(cL,"quad"+this._quad)},_setBtnPosition:function(){if(!a8.browser.ie){switch(this._quad){case 2:this._omBtn.style.top="0";this._omBtn.style.left="0";break;case 1:this._omBtn.style.top="0";this._omBtn.style.right="0";break;case 4:this._omBtn.style.bottom="0";this._omBtn.style.right="0";break;case 3:this._omBtn.style.bottom="0";this._omBtn.style.left="0";break;default:break}}else{var cN=this._btnWidth;var cL=this._btnHeight;var T=this._opts.size.width;var cM=this._opts.size.height;this._omBtn.style.left="auto";this._omBtn.style.top="auto";this._omBtn.style.right="auto";this._omBtn.style.bottom="auto";switch(this._quad){case 2:this._omBtn.style.left="0px";this._omBtn.style.top="0px";break;case 1:this._omBtn.style.left=T-cN+"px";this._omBtn.style.top="0px";break;case 4:this._omBtn.style.top=cM-cL+"px";this._omBtn.style.left=T-cN+"px";break;case 3:this._omBtn.style.left="0px";this._omBtn.style.top=cM-cL+"px";break;default:break}}},_moveView:function(){if(this._omView){this._omView.setPosition(this._map.getCenter())}},_onMainMoveEnd:function(T){switch(this._currentOp){case"dragMap":this._setOMViewMvPos();this._currentOp="";break;case"dragView":this._omViewMv.style.display="none";this._overviewMap.panTo(this._map.getCenter(),{duration:90});this._currentOp="";break;default:if(this._overviewMap){this._overviewMap.panTo(this._map.getCenter(),{duration:90})}break}},_onMainZoomEnd:function(){if(!this._overviewMap){return}var T=this;T._curOMZoomLevel=T._getCurOMZoomLevel();setTimeout(function(){T._overviewMap.centerAndZoom(T._map.getCenter(),T._curOMZoomLevel);T._omView.setPosition(T._map.getCenter());T._setRatio()},100)},_onViewMapMoveEnd:function(){if(this._currentOp!="dragMap"){this._setOMViewMvPos();this._overviewMap.config.enableMouseDown=true}},remove:function(){cp.prototype.remove.call(this);this._omCanvas=null;this._omMapContainer=null;this._omView=null;this._omViewInn=null;this._omViewMv=null;this._omViewMvInn=null;this._omBtn=null;this._overviewInitialized=false;this._overviewMap=null},isOpen:function(){if(!this._container){return false}return this._opts.isOpen}});function N(cM,T){var cL=cM.style;cL.width=T[0]+"px";cL.height=T[1]+"px"}a8.object.extend(bK.prototype,{_asyncDraw:function(){if(this._map){this._i(this._map)}},initialize:function(T){cp.prototype.initialize.call(this,T);this._render();this._setParam();this._updateScale();this._bind(T);return this._container},_setParam:function(){this._numberArray={us:[5280*5000,5280*2500,5280*2000,5280*1000,5280*500,5280*200,5280*100,5280*50,5280*25,5280*20,5280*10,5280*5,5280*2,5280,2000,1000,500,200,100,50,20,10,5],metric:[10000000,5000000,2000000,1000000,500000,200000,100000,50000,25000,20000,10000,5000,2000,1000,500,200,100,50,20,10,5,2,1]}},_bind:function(cL){var T=this;cL.addEventListener("zoomend",function(){T._updateScale()});cL.addEventListener("moveend",function(){T._updateScale()});cL.addEventListener("maptypechange",function(){T.setColor(T._map.getMapType().getTextColor())})},_render:function(){cp.prototype._render.call(this);a8.dom.addClass(this._container,"BMap_scaleCtrl");this._container.innerHTML=this._generateHTML();this.setColor(this._map.getMapType().getTextColor());this._scaleText=this._container.children[0];cp.prototype._setPosition.call(this)},_generateHTML:function(){var cL=cc.imgPath+"mapctrls.png";var T=\'<div class="BMap_scaleTxt" unselectable="on"></div><div class="BMap_scaleBar BMap_scaleHBar"><img style="border:none" src="\'+cL+\'"/></div><div class="BMap_scaleBar BMap_scaleLBar"><img style="border:none" src="\'+cL+\'"/></div><div class="BMap_scaleBar BMap_scaleRBar"><img style="border:none" src="\'+cL+\'"/></div>\';return T},setColor:function(T){this._opts.color=T+"";if(!this._container){return}this._container.children[0].style.backgroundColor="transparent";this._container.children[0].style.color=T;for(var cM=1,cL=this._container.children.length;cM<cL;cM++){this._container.children[cM].style.backgroundColor=T}},setUnit:function(T){this._opts.unit=this._units[T]&&this._units[T].name||this._opts.unit;if(!this._map){return}this._updateScale()},_setScaleText:function(cL,T){this._scaleText.innerHTML=cL+"&nbsp;"+T},_updateScale:function(){if(!this._map||!this._container){return}var cL=10;var cO=new cd(this._map.getCenter().lng,this._map.getCenter().lat+cL);var cP=Math.abs(this._map.pointToPixel(this._map.getCenter()).y-this._map.pointToPixel(cO).y);var cT=ba.getDistanceByLL(this._map.getCenter(),cO)/cP;if(cT==0||isNaN(cT)){return}var cR=this._convertUnit(cT,this._opts.unit);var T=0;var cM=this._units[this._opts.unit].incon;var cQ=this._map.getZoom();var cN=this._numberArray[this._opts.unit][this._map.getZoom()-1];T=cN/cR;var cS=cN>=cM?this._units[this._opts.unit].u2:this._units[this._opts.unit].u1;if(cN>=cM){cN=Math.round(cN/cM)}this._setScaleText(cN,cS);if(Math.round(T)%2!=0&&a8.browser.ie==6){T=T+1}this._container.style.width=Math.round(T)+"px";if(this._map.mapType==BMAP_PERSPECTIVE_MAP){this._container.style.width=Math.round(T)*3+"px";this._setScaleText(cN*3,cS)}},_convertUnit:function(T,cL){cL=cL||"metric";if(this._units[cL]){return T*this._units[cL].conv}return T}});a8.extend(aL.prototype,{_asyncDraw:function(){if(this._map){this._i(this._map)}},initialize:function(T){this._initParams();cp.prototype.initialize.call(this,T);cp.prototype._render.call(this);cp.prototype._setPosition.call(this);switch(this._opts.type){case BMAP_MAPTYPE_CONTROL_DROPDOWN:this._createBtnsDropDown();break;default:this._createBtnsDefault()}this._bind();this._draw();return this._container},_initParams:function(){this._mapTypeRel=[[BMAP_HYBRID_MAP,BMAP_SATELLITE_MAP,true]];this._btnDoms=[];this._lblDoms=[]},_createBtnsDefault:function(){var cL=this._opts.mapTypes;for(var cN=0;cN<cL.length;cN++){if(!this._isMapTypeExist(this._getParentMapType(cL[cN]))){this._createNormalBtn(cL[cN],cN)}}for(var cN=0;cN<cL.length;cN++){if(this._isMapTypeExist(this._getParentMapType(cL[cN]))){this._createLabelBtn(cL[cN],cN)}}var cM=this._btnDoms[0];cM.firstChild.style.borderRadius="3px 0 0 3px";var T=this._btnDoms[this._btnDoms.length-1];T.firstChild.style.borderRight="1px solid #8ba4dc";if(cM==T){T.firstChild.style.borderRadius="3px"}else{T.firstChild.style.borderRadius="0 3px 3px 0"}this._container.style.whiteSpace="nowrap";this._container.style.cursor="pointer"},_isMapTypeExist:function(cL){for(var T=0;T<this._opts.mapTypes.length;T++){if(this._opts.mapTypes[T]==cL){return true}}return false},_getParentMapType:function(cL){for(var T=0;T<this._mapTypeRel.length;T++){if(this._mapTypeRel[T][0]==cL){return this._mapTypeRel[T][1]}}return null},_getInputCheckedInfo:function(cL){for(var T=0;T<this._mapTypeRel.length;T++){if(this._mapTypeRel[T][0]==cL){return this._mapTypeRel[T][2]}}return false},_createNormalBtn:function(cQ,cM){var cP=this;var cN=aa("div");cw(cN);var cO=cN.style;cO.boxShadow="2px 2px 3px rgba(0, 0, 0, 0.35)";cO.borderLeft="1px solid #8ba4dc";cO.borderTop="1px solid #8ba4dc";cO.borderBottom="1px solid #8ba4dc";cO.background="white";cO.padding="2px 6px";cO.font="12px "+cc.fontFamily;cO.lineHeight="1.3em";cO.textAlign="center";cO.whiteSpace="nowrap";cN.innerHTML=cQ.getName();cN.title=cQ.getTips();cN.onclick=function(){cP._map.setMapType(cP._getProperMapType(cQ))};var cL=aa("div");cw(cL);var T=cL.style;if(a8.browser.ie){T.styleFloat="left"}else{T.cssFloat="left"}cL.appendChild(cN);this._btnDoms[cM]=cL;if(!this._btnDoms[cM+1]){this._container.appendChild(cL)}else{this._container.insertBefore(cL,this._btnDoms[cM+1])}},_createLabelBtn:function(cM,cQ){var cR=this._getParentMapType(cM);var cO=this._getMapTypeIndex(cR);var cL=this._btnDoms[cO];var cS=this;cL.onmouseover=function(){if((cS._map.getMapType()==cR||cS._map.getMapType()==cM)&&this._labelDom){if(this._hideLabelTimer){clearTimeout(this._hideLabelTimer);this._hideLabelTimer=null}if(this._labelDom){a8.dom.show(this._labelDom)}}};cL.onmouseout=function(){var cV=this;if(this._hideLabelTimer){clearTimeout(this._hideLabelTimer);this._hideLabelTimer=null}this._hideLabelTimer=setTimeout(function(){if(cV._labelDom){a8.dom.hide(cV._labelDom)}},1000)};cL.onmousedown=function(){if(this._hideLabelTimer){clearTimeout(this._hideLabelTimer);this._hideLabelTimer=null}if(this._labelDom){a8.dom.show(this._labelDom)}};cL._childMapType=cM;var cU=aa("div");cw(cU);labelContainerStyle=cU.style;labelContainerStyle.position="absolute";labelContainerStyle.top=this._btnDoms[cO].offsetHeight+"px";var cP=this.getAnchor();if(this._getBtnNum()==1){if(cP==BMAP_ANCHOR_TOP_LEFT||cP==BMAP_ANCHOR_BOTTOM_LEFT){labelContainerStyle.left="0"}else{labelContainerStyle.right="0"}}else{if(cO==0||cO!=this._btnDoms.length-1){var T=0,cT=0;while(cT<cO){if(this._btnDoms[cT]){T+=this._btnDoms[cT].offsetWidth}cT++}labelContainerStyle.left=T+"px"}else{labelContainerStyle.right="0"}}labelContainerStyle.zIndex="-1";labelContainerStyle.display="none";var cN=this._getInputCheckedInfo(cM)?\'checked="checked"\':"";cU.innerHTML=\'<div title="\'+cM.getTips()+\'" style="border-right:1px solid #8ba4dc;border-bottom:1px solid #8ba4dc;border-left:1px solid #8ba4dc;background:white;font:12px \'+cc.fontFamily+\';padding:0 8px 0 6px;line-height:1.6em;box-shadow:2px 2px 3px rgba(0, 0, 0, 0.35)"><span \'+cN+\'" class="BMap_checkbox"></span><label style="vertical-align: middle; cursor: pointer;">\'+(cM.getLabelText()||cM.getName())+"</label></div>";cU.onclick=function(){cS._map.setMapType(cS._getProperMapType(cM))};cU.onmouseover=function(cV){if(cL._hideLabelTimer){clearTimeout(cL._hideLabelTimer);cL._hideLabelTimer=null}a8.dom.show(this);aP(cV)};cU.onmouseout=function(){var cV=this;if(cL._hideLabelTimer){clearTimeout(cL._hideLabelTimer);cL._hideLabelTimer=null}cL._hideLabelTimer=setTimeout(function(){if(cV){a8.dom.hide(cV)}},1000)};cL._lblDom=this._lblDoms[cQ]=cU;cL.appendChild(cU);cL._labelDom=cU},_createBtnsDropDown:function(){var cN=aa("div");cw(cN);cN.title="\u66f4\u6539\u5730\u56fe\u7c7b\u578b";var cP=cN.style;cP.font="bold 12px/1.5em "+cc.fontFamily;cP.background="#fff";cP.boxShadow="2px 2px 3px rgba(0, 0, 0, 0.35)";cP.padding="0 6px";cP.border="1px solid #8ba4dc";cN.innerHTML=\'<span style="float:right;font-family:\'+cc.fontFamily+\'">\u25bc</span>\'+this._map.getMapType().getName();this._mainBtn=cN;var cL=aa("div");cw(cL);dropDownStyle=cL.style;dropDownStyle.position="relative";dropDownStyle.zIndex="-1";dropDownStyle.background="#fff";dropDownStyle.display="none";dropDownStyle.borderLeft=dropDownStyle.borderRight=dropDownStyle.borderBottom="1px solid #8ba4dc";var T=this._opts.mapTypes;for(var cM=0;cM<T.length;cM++){if(!this._isMapTypeExist(this._getParentMapType(T[cM]))){this._createNormalBtnDropDown(T[cM],cM,cL)}}var cO=aa("div");cO.style.borderTop="1px solid rgb(220, 220, 220)";cO.style.margin="1px 4px";cL.appendChild(cO);for(var cM=0;cM<T.length;cM++){if(this._isMapTypeExist(this._getParentMapType(T[cM]))){this._createLabelBtnDropDown(T[cM],cM,cL)}}this._container.style.width="85px";this._container.style.whiteSpace="nowrap";this._container.style.cursor="pointer";this._container.appendChild(cN);this._container.appendChild(cL);cN._dropDownContainer=cL;cN.onclick=function(){if(this._hideDropDownTimer){clearTimeout(this._hideDropDownTimer);this._hideDropDownTimer=null}if(this._dropDownContainer){this._dropDownContainer.style.display=this._dropDownContainer.style.display=="none"?"":"none"}};cN.onmouseout=function(){if(this._hideDropDownTimer){clearTimeout(this._hideDropDownTimer);this._hideDropDownTimer=null}var cQ=this;this._hideDropDownTimer=setTimeout(function(){if(cQ._dropDownContainer){a8.dom.hide(cQ._dropDownContainer)}},1000)};cL.onmouseover=function(){if(cN._hideDropDownTimer){clearTimeout(cN._hideDropDownTimer);cN._hideDropDownTimer=null}a8.dom.show(this)};cL.onmouseout=function(){if(cN._hideDropDownTimer){clearTimeout(cN._hideDropDownTimer);cN._hideDropDownTimer=null}cN._hideDropDownTimer=setTimeout(function(){if(cN._dropDownContainer){a8.dom.hide(cN._dropDownContainer)}},1000)}},_createNormalBtnDropDown:function(cP,cM,T){var cL=aa("div");cw(cL);var cO=cL.style;cO.color="#000";cO.font="12px/1.6em "+cc.fontFamily;cO.background="#fff";cO.padding="1px 6px";if(a8.browser.ie<8){cO.zoom="1"}cL.innerHTML=cP.getName();cL.title=cP.getTips();var cN=this;cL.onclick=function(){cN._map.setMapType(cN._getProperMapType(cP))};T.appendChild(cL);this._btnDoms[cM]=cL},_createLabelBtnDropDown:function(cN,cQ,T){var cM=aa("div");cw(cM);var cT=cM.style;cT.font="12px/1.6em "+cc.fontFamily;cT.padding="1px 0 1px 4px";cT.whiteSpace="nowrap";cM.title=cN.getTips();var cO=this._getInputCheckedInfo(cN)?\'checked="checked"\':"";cM.innerHTML="<span "+cO+\' class="BMap_checkbox"></span><label style="vertical-align:middle;cursor:pointer">\'+(cN.getLabelText()||cN.getName())+"</label>";var cS=this;cM.onclick=function(){cS._map.setMapType(cS._getProperMapType(cN))};T.appendChild(cM);this._lblDoms[cQ]=cM;var cR=this._getParentMapType(cN);var cP=this._getMapTypeIndex(cR);var cL=this._btnDoms[cP];cL._childMapType=cN;cL._lblDom=cM},_getBtnNum:function(){var T=0;for(var cL=0;cL<this._btnDoms.length;cL++){if(this._btnDoms[cL]){T++}}return T},_getMapTypeIndex:function(cL){for(var T=0;T<this._opts.mapTypes.length;T++){if(this._opts.mapTypes[T]==cL){return T}}return -1},_getProperMapType:function(cM){for(var T=0;T<this._mapTypeRel.length;T++){if(this._mapTypeRel[T][0]==cM&&this._map.getMapType()==cM&&this._isMapTypeExist(this._mapTypeRel[T][1])){return this._mapTypeRel[T][1]}if(this._mapTypeRel[T][1]==cM){var cN=this._lblDoms[this._getMapTypeIndex(this._mapTypeRel[T][0])];if(cN){var cL=cN.getElementsByTagName("span")[0];if(cL.checked==""){b2.dom.removeClass(cL,"checked");return cM}else{return this._mapTypeRel[T][0]}}}}return cM},_renderSelBtn:function(cL){var T=cL.style;T.background="#8ea8e0";T.color="#fff";T.fontWeight="bold"},_renderUnSelBtn:function(cL){var T=cL.style;T.background="#fff";T.color="#000";T.fontWeight=""},_bind:function(){var T=this;T._map.addEventListener("onmaptypechange",function(cL){T._draw()})},_draw:function(){switch(this._opts.type){case BMAP_MAPTYPE_CONTROL_DROPDOWN:this._drawDropDown();break;default:this._drawDefault()}},_drawDefault:function(){var cM=this._map.getMapType();for(var T=0;T<this._opts.mapTypes.length;T++){var cL=null;if(!this._btnDoms[T]){continue}if(cM==this._opts.mapTypes[T]){this._renderSelBtn(this._btnDoms[T].children[0]);if(this._btnDoms[T]._childMapType&&this._btnDoms[T]._lblDom){cL=this._btnDoms[T]._lblDom.getElementsByTagName("span")[0];cL.checked="";b2.dom.removeClass(cL,"checked")}}else{if(this._btnDoms[T]._childMapType==cM){this._renderSelBtn(this._btnDoms[T].children[0]);var cN=this._btnDoms[T]._lblDom;cL=cN.getElementsByTagName("span")[0];if(cN){cL.checked="checked";b2.dom.addClass(cL,"checked")}}else{this._renderUnSelBtn(this._btnDoms[T].children[0]);if(this._btnDoms[T]._lblDom){a8.dom.hide(this._btnDoms[T]._lblDom);clearTimeout(this._btnDoms[T]._hideLabelTimer);this._btnDoms[T]._hideLabelTimer=null}}}}},_drawDropDown:function(){var cP=this._map.getMapType();var cM=false;var cL=false;for(var cN=0;cN<this._opts.mapTypes.length;cN++){var cO=null;if(cP==this._opts.mapTypes[cN]&&this._btnDoms[cN]){cM=true;this._mainBtn.innerHTML=\'<span style="float:right;font-family:\'+cc.fontFamily+\'">\u25bc</span>\'+this._map.getMapType().getName();var T=this._btnDoms[cN];if(T&&T._childMapType&&T._lblDom){a8.dom.show(T._lblDom);cO=T._lblDom.getElementsByTagName("span")[0];cO.checked="";b2.dom.removeClass(cO,"checked")}}else{if(this._btnDoms[cN]&&this._btnDoms[cN]._childMapType==cP){cL=true;this._mainBtn.innerHTML=\'<span style="float:right;font-family:\'+cc.fontFamily+\'">\u25bc</span>\'+this._getParentMapType(cP).getName();var cQ=this._btnDoms[cN]._lblDom;if(cQ){a8.dom.show(cQ);cO=cQ.getElementsByTagName("span")[0];cO.checked="checked";b2.dom.addClass(cO,"checked")}}else{if(this._btnDoms[cN]){var cQ=this._btnDoms[cN]._lblDom;if(cQ){a8.dom.hide(cQ)}}}}}if(!cM&&!cL){this._mainBtn.innerHTML=\'<span style="float:right;font-family:\'+cc.fontFamily+\'">\u25bc</span>\'+this._map.getMapType().getName()}},remove:function(){this._btnDoms=this._lblDoms=[];this._mainBtn=null;cp.prototype.remove.call(this)}});');
_jsload&&_jsload('menu', 'a8.object.extend(cz.prototype,{initialize:function(cO,cL){if(this._container){return false}this._map=cO;this._overlay=cL||null;this._render();var cN=this;var cP=cO.config.defaultCursor;if(cL&&cL._config.clickable){cP="pointer"}this._container.style.cursor=cP;if(this._shadow){this._shadow.style.cursor=cP}a8.on(document,"mousedown",function(cR){if(!cN._container){return}cN.hide()});a8.on(this._container,"click",function(cR){cN.hide();aP(cR)});var cQ=this._opts.container;if(!cQ){cQ=cO.container}if(!this._overlay){cO.addEventListener("rightclickex",function(cR){if(!cN._container||cR.overlay||cR.infoWindow){return}cN._showMenu(cR)})}else{this._overlay.addEventListener("rightclick",function(cR){if(!cN._container){return}cN._showMenu(cR)})}for(var cM=0,T=this._items.length;cM<T;cM++){if(this._items[cM]._type=="menuitem"){this._items[cM].initialize(cO,this)}if(this._items[cM]._type=="divider"){this._dividers[this._items[cM]._dIndex].dom=at(this._container,"<div class=\'BMap_cmDivider\'></div>")}}this._updateShadowSize()},_draw:function(){if(this._map){this.initialize(this._map,this._overlay);if(this._isOpen){this._isOpen=false;this.show()}}},remove:function(){if(this._container){this._container.parentNode.removeChild(this._container);this._container=null}if(this._shadow){this._shadow.parentNode.removeChild(this._shadow);this._shadow=null}for(var cL=0,T=this._items.length;cL<T;cL++){if(this._items[cL]._type=="menuitem"){this._items[cL]._container=null}}this._map=this._overlay=null},_render:function(){this._container=at(this._map.container,"<div unselectable=\'on\'></div>");this._container.className="BMap_contextMenu";var T=this._container.style;T.font="12px "+cc.fontFamily;if(a8.browser.ie<9){this._shadow=at(this._map.container,"<div class=\'BMap_cmShadow\'></div>")}else{T.boxShadow=T.MozBoxShadow=T.WebkitBoxShadow="1px 2px 6px #666"}return this._container},addItem:function(cM){if(!cM||cM._type!="menuitem"||cM._text==""||cM._width<=0){return}for(var cL=0,T=this._items.length;cL<T;cL++){if(this._items[cL]===cM){return}}this._items.push(cM);this._rItems.push(cM);if(!this._map){return}cM.initialize(this._map,this);a8.dom.addClass(cM.getDom(),"BMap_cmLstItem");if(this._items.length>1){this._items[this._items.length-2]._type=="menuitem"&&a8.dom.removeClass(this._items[this._items.length-2].getDom(),"BMap_cmLstItem")}else{this._items[0]._type=="menuitem"&&a8.dom.addClass(this._items[0].getDom(),"BMap_cmFstItem")}this._updateShadowSize()},removeItem:function(cM){if(!cM||cM._type!="menuitem"){return}for(var cL=0,T=this._items.length;cL<T;cL++){if(this._items[cL]===cM){this._items[cL].remove();this._items.splice(cL,1);T--}}for(var cL=0,T=this._rItems.length;cL<T;cL++){if(this._rItems[cL]===cM){this._rItems[cL].remove();this._rItems.splice(cL,1);T--}}if(!this._container){return}if(this._items.length>0){this._items[this._items.length-1]._type=="menuitem"&&a8.dom.addClass(this._items[this._items.length-1].getDom(),"BMap_cmLstItem")}this._updateShadowSize()},addSeparator:function(){this._items.push({_type:"divider",_dIndex:this._dividers.length});this._dividers.push({dom:null});if(!this._container){return}var T="<div class=\'BMap_cmDivider\'></div>";this._dividers[this._dividers.length-1].dom=at(this._container,T);this._updateShadowSize()},removeSeparator:function(cL){if(!this._dividers[cL]){return}if(this._dividers[cL].dom&&this._dividers[cL].dom.parentNode){this._dividers[cL].dom.parentNode.removeChild(this._dividers[cL].dom)}for(var cM=0,T=this._items.length;cM<T;cM++){if(this._items[cM]&&this._items[cM]._type=="divider"&&this._items[cM]._dIndex==cL){this._items.splice(cM,1);T--}if(this._items[cM]&&this._items[cM]._type=="divider"&&this._items[cM]._dIndex>cL){this._items[cM]._dIndex--}}this._dividers.splice(cL,1);this._updateShadowSize()},setPosition:function(T,cL){this._left=T;this._top=cL;this._container.style.left=T+"px";this._container.style.top=cL+"px";if(this._shadow){this._shadow.style.left=T+1+"px";this._shadow.style.top=cL+2+"px"}},show:function(){if(this._isOpen==true){return}if(this._rItems.length==0){return}this._isOpen=true;if(this._container){this._container.style.visibility="visible"}if(this._shadow){this._shadow.style.visibility="visible"}var T=new bg("onopen");T.point=this.curPoint;T.pixel=this.curPixel;this.dispatchEvent(T)},hide:function(){if(this._isOpen==false){return}this._isOpen=false;if(this._container){this._container.style.visibility="hidden"}if(this._shadow){this._shadow.style.visibility="hidden"}var T=new bg("onclose");T.point=this.curPoint;T.pixel=this.curPixel;this.dispatchEvent(T)},setCursor:function(T){if(!T){return}this._opts.cursor=T;if(this._container){this._container.style.cursor=this._opts.cursor}if(this._shadow){this._shadow.style.cursor=this._opts.cursor}},_updateShadowSize:function(){if(this._container&&this._shadow){this._shadow.style.width=this._container.offsetWidth+"px";this._shadow.style.height=this._container.offsetHeight+"px"}},_showMenu:function(cP){if(this._rItems.length==0){return}var cM=this;cM.curPixel=cP.pixel;cM.curPoint=cM._map.pixelToPoint(cM.curPixel);var cL=cM.getDom().offsetHeight;var T=cM.getDom().offsetWidth;var cO=cP.pixel.x;var cN=cP.pixel.y;if(cP.pixel.x+T>this._map.width){cO=cP.pixel.x-T}if(cP.pixel.y+cL>this._map.height){cN=cP.pixel.y-cL}cM.setPosition(cO,cN);cM.show()}});a8.object.extend(be.prototype,{initialize:function(T,cL){if(this._container){return false}this._map=T;this._contextmenu=cL;if(cL.getDom()){this._render();this._bind();if(!this._enabled){this._enabled=true;this.disable()}}return true},remove:function(){var T=this;if(this._container){this._container.parentNode.removeChild(this._container);this._container=null}this._contextmenu=null;this._map=null},_draw:function(){if(this._contextmenu&&this._map){this.initialize(this._map,this._contextmenu)}},_render:function(){var T="<div"+(this._opts.id?" id=\'"+this._opts.id+"\'":"")+" unselectable=\'on\'><span>"+this._text+"</span></div>";this._container=at(this._contextmenu.getDom(),T);var cL=this._container.style;cL.padding="2px 6px";cL.margin="0 2px";cL.fontSize="12px";cL.MozUserSelect="none";cL.lineHeight="17px";cL.width=this._opts.width+"px";if(this._enabled){cL.color="#000";cL.cursor="pointer"}else{cL.color="#aaa";cL.cursor=this._map.config.defaultCursor}return this._container},_bind:function(){var T=this;a8.on(this._container,"click",function(cL){if(!T._enabled){aP(cL);return}if(T._callback&&T._callback.call){T._callback.call(T,T._contextmenu.curPoint,T._contextmenu.curPixel,T._contextmenu._overlay)}});a8.on(this._container,"mousedown",function(cL){aP(cL)});a8.on(this._container,"mouseover",function(){if(!T._enabled){return}T._container.style.color="#6688cc"});a8.on(this._container,"mouseout",function(){if(!T._enabled){return}T._container.style.color="#000"})},setText:function(T){if(!T){return}this._text=T+"";if(this._container){this._container.innerHTML="<span>"+this._text+"</span>"}},enable:function(){this._enabled=true;if(this._container){this._container.style.color="#000";this._container.style.cursor="pointer"}},disable:function(){this._enabled=false;if(this._container){this._container.style.color="#aaa";this._container.style.cursor=this._map.config.defaultCursor}}});');
﻿_jsload&&_jsload('marker', 'a8.extend(Y.prototype,{initialize:function(T){this.map=T;this._addDom();this._bind();if(this._menu){this._menu.initialize(this.map,this)}this.domElement.guid=this.guid;a8.lang.Class.call(this,this.guid);if(!this._visible){a8.dom.hide(this.domElement)}return this.domElement},_bind:function(){if(!this.domElement){return}var T=this;var cM=this.map;function cL(cR,cQ){var cP=cR.srcElement||cR.target;var cO=cR.clientX||cR.pageX;var cT=cR.clientY||cR.pageY;if(cR&&cQ&&cO&&cT&&cP){var cS=a8.dom.getPosition(cM.container);var cN=cG();cQ.pixel=new bu(cO-cS.left+cN[1],cT-cS.top+cN[0]);cQ.point=cM.pixelToPoint(cQ.pixel);return cQ}else{return cQ}}a8.on(this.domElement,"mouseover",function(cN){if(!T._dragstarted){T.dispatchEvent(cL(cN,new bg("onmouseover").inherit(cN)))}});a8.on(this.domElement,"mouseout",function(cN){if(!T._dragstarted){T.dispatchEvent(cL(cN,new bg("onmouseout").inherit(cN)))}});if(T._config.clickable){a8.on(this.domElement,"touchstart",function(cN){T._bind._touchStartPos=new bu(cN.changedTouches[0].clientX,cN.changedTouches[0].clientY)});a8.on(this.domElement,"touchend",function(cP){var cO=aE(),cN=new bu(cP.changedTouches[0].clientX,cP.changedTouches[0].clientY);if(Math.abs(cN.x-T._bind._touchStartPos.x)>10||Math.abs(cN.y-T._bind._touchStartPos.y)>10){T._bind._touchStartPos=null;return}T.dispatchEvent(cL(cP,new bg("onclick").inherit(cP)));if(cO-T._dblclickTime<T.map.config.clickInterval){T.dispatchEvent(cL(cP,new bg("ondblclick").inherit(cP)))}T._dblclickTime=cO});a8.on(this.domElement,"click",function(cO){var cN=cO.srcElement||cO.target;while(cN){if(cN==T.domElement){if(!(T instanceof ad)||T instanceof ad&&(!T._lastPt||T._lastPt&&T.getPosition().equals(T._lastPt))){T.dispatchEvent(cL(cO,new bg("onclick").inherit(cO)))}break}else{if(T.map&&T.map.infoWindowDoms&&cN==T.map.infoWindowDoms.popDom){break}}cN=cN.parentNode}});a8.on(this.domElement,"dblclick",function(cN){T.dispatchEvent(cL(cN,new bg("ondblclick").inherit(cN)));co(cN)});if(!a8.browser.firefox||a8.browser.firefox<4){a8.on(this.domElement,"contextmenu",function(cN){T.dispatchEvent(cL(cN,new bg("onrightclick").inherit(cN)))})}}a8.on(this.domElement,"mousedown",function(cN){if(T instanceof ad){T._lastPt=T.getPosition()}T.dispatchEvent(cL(cN,new bg("onmousedown").inherit(cN)))});a8.on(this.domElement,"mouseup",function(cN){T.dispatchEvent(cL(cN,new bg("onmouseup").inherit(cN)));if(a8.browser.firefox>=4&&cN.button==2&&T._config.clickable){T.dispatchEvent(cL(cN,new bg("onrightclick").inherit(cN)))}})},hide:function(){if(this._visible==false){return}this._visible=false;bG.prototype.hide.call(this);if(this.infoWindow&&this.infoWindow.overlay&&this.infoWindow.overlay==this){this.closeInfoWindow()}},show:function(){if(this._visible==true){return}this._visible=true;bG.prototype.show.call(this)},setConfig:function(cL){if(!cL){return}for(var T in cL){if(typeof(this._config[T])==typeof(cL[T])){this._config[T]=cL[T]}}},setZIndex:function(T){var cL=this;cL.zIndex=T;cL._updateDomZIndex()},_updateDomZIndex:function(){var cL=this,T;if(ch(cL.zIndex)){T=cL.zIndex}else{T=0;if(cL.map&&cL.getPosition()){var cM=cL.getPosition()?cL.getPosition().lat:0;T=bG.getZIndex(cM)+(cL._config.baseZIndex||0)}}if(cL.domElement){cL.domElement.style.zIndex=T}},addContextMenu:function(T){this._menu=T;if(this.map){T.initialize(this.map,this)}},removeContextMenu:function(){this._menu.remove();this._menu=null}});var bj=new M(cc.imgPath+"marker_red_hd.png",new aH(47,50),{anchor:new aH(20,50),infoWindowAnchor:new aH(20,0)});var ah=3;var bx=4;ad.TOP_ZINDEX=bG.getZIndex(-90)+1000000;ad.DRAG_ZINDEX=ad.TOP_ZINDEX+1000000;ad._getAnimationName=function(cM){if(ad._cssAniNames[cM]){return ad._cssAniNames[cM]}var cN=ad._cssAniNames[cM]=["BMap_"+Math.round(Math.random()*10000),"BMap_"+Math.round(Math.random()*10000)];var T=b0[cM];var cL=ad._styleElement;if(!cL){cL=ad._styleElement=aa("style",{type:"text/css"});document.getElementsByTagName("head")[0].appendChild(cL)}cL.textContent+=ad._generateAniCSS(T.iconAnis,cN[0])+ad._generateAniCSS(T.shadowAnis,cN[1]);return ad._cssAniNames[cM]};ad._generateAniCSS=function(cM,cL){var T=["@-webkit-keyframes "+cL+" {\\n"];a8.array.each(cM,function(cN){T.push(cN.percent*100,"% { ");T.push("-webkit-transform: translate(",cN.translate[0],"px,",cN.translate[1],"px); ");T.push("-webkit-animation-timing-function: ",cN.timingFunc,"; ");T.push("}\\n")});T.push("}\\n");return T.join("")};ad._addDragCrossImg=function(cL,T){if(!ad._dragCrossImg){ad._dragCrossImg=aa("img",{src:cc.imgPath+"drag_cross.png",width:13,height:9});ad._dragCrossImg.style.position="absolute";if(a8.browser.ie==6){delete ad._dragCrossImg;var cN=ad._dragCrossImg=aa("div");var cM=cN.style;cM.position="absolute";cM.width="13px";cM.height="9px";cM.filter=\'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=image,src="\'+cc.imgPath+\'drag_cross.png")\'}}var cM=ad._dragCrossImg.style;cM.left=cL.width-6+"px";cM.top=cL.height-5+"px";T.appendChild(ad._dragCrossImg)};ad._removeDragCrossImg=function(){if(ad._dragCrossImg&&ad._dragCrossImg.parentNode){ad._dragCrossImg.parentNode.removeChild(ad._dragCrossImg)}};ad._cssAniNames=[];ad._aniEndHandler=function(){this.style.WebkitAnimation=""};a8.extend(ad.prototype,{initialize:function(T){this._initParam();Y.prototype.initialize.call(this,T);if(!this._visible){a8.dom.hide(this.siblingElement)}if(T.highResolutionEnabled()&&this._config.icon==au){this._config.icon=bj}this.setPosition(this.point);this.setIcon(this._config.icon);this.setShadow(this._config.shadow);if(this._config.label&&this._cfgLabelEvent){this._config.label.addEventListener("remove",this._cfgLabelEvent)}this.setLabel(this._config.label);this.setTitle(this._config.title);this._initDrag();return this.domElement},_initParam:function(){if(this._init){return}this._init=true;this.domElement=null;this.shadowElement=null;this.siblingElement=null;this.iconDom=null;this._isDragging=false},_addDom:function(){var T=this.map.getPanes();this.domElement=at(T.markerMouseTarget,this._msTargetRender());this.siblingElement=at(T.markerPane,this._markerRender());this.siblingElement.guid=this.guid},_msTargetRender:function(){var T=[\'<span class="BMap_Marker BMap_noprint" unselectable="on" \'];T.push(this._config.title?\'title="\'+this._config.title+\'"\':\'"\');T.push(\' style="position:absolute;padding:0;margin:0;border:0;width:0;height:0;-moz-user-select:none;\');T.push(this._config.clickable?"cursor:pointer;":"");T.push("background:url("+cc.imgPath+"blank.gif);");T.push("width:"+this._config.icon.size.width+"px;");T.push("height:"+this._config.icon.size.height+"px;");T.push(\'"></span>\');return T.join("")},_markerRender:function(){var T=[\'<span class="BMap_Marker" unselectable="on" \'];T.push(\'style="position:absolute;padding:0;margin:0;border:0;\');T.push(\'width:0;height:0;-moz-user-select:none"></span>\');return T.join("")},_shadowRender:function(){var T=[\'<span unselectable="on" \'];T.push(\'style="position:absolute;padding:0;margin:0;border:0;\');T.push(\'width:0;height:0;-moz-user-select:none"></span>\');return T.join("")},draw:function(){if(!this.domElement){return}var T=this._getPixPos();this.domElement.style.left=T[0].x+"px";this.domElement.style.top=T[0].y+"px";if(this.siblingElement){this.siblingElement.style.left=T[0].x+"px";this.siblingElement.style.top=T[0].y+"px"}if(this.shadowElement){this.shadowElement.style.left=T[1].x+"px";this.shadowElement.style.top=T[1].y+"px"}if(this.infoWindow!=null&&this.infoWindow.isOpen()){this.infoWindow.setPosition()}this._updateDomZIndex()},_getPixPos:function(){var cP=this._config.offset||new aH(0,0);var T=this._config.icon.anchor||new aH(0,0);var cL=this.map.pointToOverlayPixel(this.getPosition());var cO=new bu(cL.x+cP.width-T.width,cL.y+cP.height-T.height);var cN=[cO];if(this._config.shadow){var cQ=this._config.shadow.anchor||new aH(0,0),cM=new bu(cL.x+cP.width-cQ.width,cL.y+cP.height-cQ.height);cN[1]=cM}return cN},_draw:function(){if(this.map){this.domElement=this.initialize(this.map);if(this._animation){this.setAnimation(this._animation);delete this._animation}}else{delete this._animation}},remove:function(){this.setAnimation(null);if(this.siblingElement&&this.siblingElement.parentNode){this.siblingElement.parentNode.removeChild(this.siblingElement)}if(this.shadowElement&&this.shadowElement.parentNode){this.shadowElement.parentNode.removeChild(this.shadowElement)}if(this.infoWindow&&this.infoWindow.overlay&&this.infoWindow.overlay===this){this.closeInfoWindow();this.infoWindow=null}this.siblingElement=null;this.shadowElement=null;this.iconDom=null;this.shadowDom=null;if(this._config.label){var T=this._config.label;T.removeEventListener("remove",this._cfgLabelEvent);a8.lang.decontrol(T.guid);T.setMarker(null);T.domElement=null;T=this._config.label=null}Y.prototype.remove.call(this)},hide:function(){Y.prototype.hide.call(this);if(this.domElement){a8.dom.hide(this.domElement)}if(this.siblingElement){a8.dom.hide(this.siblingElement)}if(this.shadowElement){a8.dom.hide(this.shadowElement)}},show:function(){Y.prototype.show.call(this);if(this.domElement){a8.dom.show(this.domElement)}if(this.siblingElement){a8.dom.show(this.siblingElement)}if(this.shadowElement){a8.dom.show(this.shadowElement)}},setIcon:function(cM){if(!(cM instanceof M)){return}this._config.icon=cM;if(!this.map||!this.domElement||!this.siblingElement){return}var cO=this.map;try{if(this.iconDom){this.siblingElement.removeChild(this.iconDom);this.iconDom=null}this.domElement.style.width=cM.size.width+"px";this.domElement.style.height=cM.size.height+"px"}catch(cN){}if(this._config.icon){var T=this.iconDom=aa("div");var cL=T.style;cL.MozTransform = "rotate("+ this._config.rotation +"deg)";cL.WebkitTransform = "rotate("+this._config.rotation +"deg)";cL.OTransform = "rotate("+ this._config.rotation +"deg)";cL.msTransform = "rotate("+ this._config.rotation +"deg)";cL.transform = "rotate("+ this._config.rotation +"deg)";cL.position="absolute";cL.padding=cL.margin="0";cL.width=cM.size.width+"px";cL.height=cM.size.height+"px";cL.overflow="hidden";T.innerHTML=cM.getHTML(this._config);T.galleryImg=false;if(a8.browser.ie&&(a8.browser.ie<9 )){this.siblingElement.innerHTML=T.innerHTML}else{this.siblingElement.appendChild(this.iconDom)} }this.draw()},setShadow:function(cL){if(!(cL instanceof M)){return}this._config.shadow=cL;if(!this.map||!this.domElement||!this.siblingElement){return}if(!this.shadowElement){this.shadowElement=at(this.map.getPanes().markerShadow,this._shadowRender())}var cN=this.map;try{if(this.shadowDom){this.shadowElement.removeChild(this.shadowDom);this.shadowDom=null}this.shadowElement.style.width=cL.size.width+"px";this.shadowElement.style.height=cL.size.height+"px"}catch(cM){}if(this._config.shadow){var cO=this.shadowDom=aa("div");var T=cO.style;T.position="absolute";T.padding=T.margin="0";T.width=cL.size.width+"px";T.height=cL.size.height+"px";T.overflow="hidden";cO.innerHTML=cL.getHTML(this._config);cO.galleryImg=false;this.shadowElement.appendChild(this.shadowDom)}this.draw()},setLabel:function(T){if(!(T instanceof ag)){return}this._config.label=T;var cM=this;if(!this._config.label._binded){this._config.label._binded=true;this._cfgLabelEvent=function(){cM._config.label=null};this._config.label.addEventListener("remove",this._cfgLabelEvent)}if(!this.map){return}T._i(this.map);if(T.domElement){this.siblingElement.appendChild(T.domElement)}else{T.domElement=at(this.domElement,T.render());T.domElement.guid=T.guid}var cL=T.domElement.style;cL.left=(T._config.offset.width)+"px";cL.top=(T._config.offset.height)+"px";T.setMarker(this)},_initDrag:function(){if(!this.domElement||this.domElement._binded){return}this.domElement._binded=true;var cQ=this.map,cN=this,T=0,cR=0,cM=0,cP={x:0,y:0};function cO(cT,cS){cS.pixel=cT.pixel;cS.point=cT.point;return cS}function cL(cU){var cT=cU.clientX,cS=cU.clientY;if(cU.changedTouches){cT=cU.changedTouches[0].clientX;cS=cU.changedTouches[0].clientY}return new bu(cT,cS)}this.dragStart=function(cU){if(!cN._config.enableDragging){return}if(cU.button==2){return}cN._isDragging=true;var cT=cQ.pointToPixel(cN.point);var cS=cL(cU);T=cS.x-cT.x;cR=cS.y-cT.y;cM=aE();cN.map.temp._draggingMarker=cN;a8.on(document,"mousemove",cN.dragIng);a8.on(document,"mouseup",cN.dragEnd);a8.on(document,"touchmove",cN.dragIng);a8.on(document,"touchend",cN.dragEnd);if(cN.domElement&&cN.domElement.setCapture){cN.domElement.setCapture()}cN.domElement.style.cursor=cN._config.draggingCursor;if(cU.type=="touchstart"){aP(cU)}};this.dragIng=function(cW){if(!cN._isDragging){return}var cT=cL(cW);var cU=new bu((cT.x-T),(cT.y-cR));cP=cU;cN._draggingMovePixel=cU;if((cN._config.restrictDraggingArea&&(cU.x>15&&cU.x<cN.map.width-15)&&(cU.y>30&&cU.y<cN.map.height-15))||!cN._config.restrictDraggingArea){var cS=cN.map.pixelToPoint(cU),cV={pixel:cU,point:cS};cN._panByX=cN._panByY=0;if(cU.x<=20||cU.x>=cN.map.width-20||cU.y<=50||cU.y>=cN.map.height-10){if(cU.x<=20){cN._panByX=8}else{if(cU.x>=cN.map.width-20){cN._panByX=-8}}if(cU.y<=50){cN._panByY=8}else{if(cU.y>=cN.map.height-10){cN._panByY=-8}}if(!cN._draggingMoveTimer){cN._draggingMoveTimer=setInterval(function(){cQ.panBy(cN._panByX,cN._panByY,{noAnimation:true});var cX=cQ.pixelToPoint(cN._draggingMovePixel);cN.setPosition(cX)},30)}}else{if(cN._draggingMoveTimer){clearInterval(cN._draggingMoveTimer);cN._draggingMoveTimer=null}cN.setPosition(cS)}if(!cN._dragstarted){cN.dispatchEvent(cO(cV,new bg("ondragstart")));cN._dragstarted=true;if(cN._config.raiseOnDrag){cN.setAnimation(ah);ad._addDragCrossImg(cN._config.icon.anchor,cN.siblingElement)}}cN.dispatchEvent(cO(cV,new bg("ondragging")))}};this.dragEnd=function(cS){if(cN.domElement&&cN.domElement.releaseCapture){cN.domElement.releaseCapture()}cN._isDragging=false;cN.map.temp._draggingMarker=null;a8.un(document,"mousemove",cN.dragIng);a8.un(document,"mouseup",cN.dragEnd);a8.un(document,"touchmove",cN.dragIng);a8.un(document,"touchend",cN.dragEnd);T=cR=0;if(cN._draggingMoveTimer){clearInterval(cN._draggingMoveTimer);cN._draggingMoveTimer=null}if(aE()-cM>=100&&(cP.x>2||cP.y>2)){cN._dragstarted=false;cN.dispatchEvent(cO({pixel:cN.map.pointToPixel(cN.getPosition()),point:cN.getPosition()},new bg("ondragend")));if(cN._config.raiseOnDrag){cN.setAnimation(bx);ad._removeDragCrossImg()}cP.x=cP.y=0}cN._updateDomZIndex();if(cN.domElement){cN.domElement.style.cursor=cN._config.clickable?"pointer":""}};a8.on(this.domElement,"mousedown",this.dragStart);a8.on(this.domElement,"touchstart",this.dragStart)},setPosition:function(T){if(T instanceof cd){this.point=this._config.point=new cd(T.lng,T.lat);this.draw()}},setRotation:function(T){this._config.rotation=T;this.setIcon(this._config.icon);},_updateDomZIndex:function(){var cL=this,T;if(cL._isDragging==true){T=ad.DRAG_ZINDEX}else{if(cL._config.isTop==true){T=ad.TOP_ZINDEX+(cL._addi||0)}else{if(ch(cL.zIndex)){T=cL.zIndex}else{T=0;if(cL.map&&cL.getPosition()){T=bG.getZIndex(cL.getPosition().lat)+cL._config.baseZIndex}}}}if(cL.domElement){cL.domElement.style.zIndex=T}if(cL.siblingElement){cL.siblingElement.style.zIndex=T}},setTop:function(cL,T){this._config.isTop=!!cL;if(cL){this._addi=T||0}this._updateDomZIndex()},setTitle:function(T){this._config.title=T+"";if(this.domElement){this.domElement.title=this._config.title}},setOffset:function(T){if(!(T instanceof aH)){return}this._config.offset=T;this.setPosition(this.getPosition())},setAnimation:function(cM){if(!this.iconDom){return}this._clearAnimation(cM!=null);var T=b0[cM];if(!T){return}var cL=T?T.options.useJS:false;(!aA()||cL)?this._execJSAnimation(cM):this._execCSSAnimation(cM)},_clearAnimation:function(cL){this._clearCSSAni(this.iconDom);this._clearCSSAni(this.shadowDom);if(cL){if(this._aniObj){this._aniObj.stop();this._aniObj=null}this.iconDom.style.top=this.iconDom.style.left="0px";if(this.shadowDom){this.shadowDom.style.top=this.shadowDom.style.left="0px"}}else{if(this._aniObj){var T=this;this._aniObj.setFinishCallback(function(){T._aniObj=null})}}},_execCSSAnimation:function(cL){var T=b0[cL];var cM=ad._getAnimationName(cL);this._setCSSAniStyle(this.iconDom,cM[0],T);this._setCSSAniStyle(this.shadowDom,cM[1],T)},_clearCSSAni:function(T){if(T){T.style.WebkitAnimation="";a8.un(T,"webkitAnimationEnd",ad._aniEndHandler)}},_setCSSAniStyle:function(cM,cL,T){if(cM){a8.on(cM,"webkitAnimationEnd",ad._aniEndHandler);cM.style.WebkitAnimation=cL+" "+T.options.duration+"ms"+(T.options.loop==g.INFINITE?" infinite":"")}},_execJSAnimation:function(cO){var cQ=this.iconDom.style;var T=false;var cN;if(this.shadowDom){T=true;cN=this.shadowDom.style}var cL=b0[cO];var cS=this;var cR=cL.iconAnis.length,cV=cL.options.duration,cU=cS._aniObj=new g({duration:0,delay:g.INFINITE});var cT=cL.iconAnis;var cM=cL.shadowAnis;cQ.top=cT[0].translate[1]+"px";if(T){cN.left=cM[0].translate[0]+"px";cN.top=cM[0].translate[1]+"px"}for(var cP=1;cP<cR;cP++){(function(){var c1=[cT[cP].translate[0]-cT[cP-1].translate[0],cT[cP].translate[1]-cT[cP-1].translate[1]];var c0=[cT[cP-1].translate[0],cT[cP-1].translate[1]];if(T){var cY=[cM[cP].translate[0]-cM[cP-1].translate[0],cM[cP].translate[1]-cM[cP-1].translate[1]];var cX=[cM[cP-1].translate[0],cM[cP-1].translate[1]]}var cZ=aw[cT[cP-1].timingFunc];var cW=function(){};if(cP==cR-1){if(cL.options.loop!=g.INFINITE){cW=function(){cS._aniObj=null}}else{cW=function(){cS._aniObj.start()}}}cU.add(new g({duration:(cL.iconAnis[cP].percent-cT[cP-1].percent)*cV,fps:40,delay:g.INFINITE,transition:cZ,render:function(c2){if(cS.iconDom){cS.iconDom.style.top=c0[1]+Math.round(c2*c1[1])+"px"}if(T&&cS.shadowDom){cS.shadowDom.style.left=cX[0]+Math.round(c2*cY[0])+"px";cS.shadowDom.style.top=cX[1]+Math.round(c2*cY[1])+"px"}},finish:cW}))})()}cU.start()}});var b0={};b0[1]={options:{duration:400},iconAnis:[{percent:0,translate:[0,-500],timingFunc:"ease-in"},{percent:0.5,translate:[0,0],timingFunc:"ease-out"},{percent:0.75,translate:[0,-20],timingFunc:"ease-in"},{percent:1,translate:[0,0],timingFunc:"ease-out"}],shadowAnis:[{percent:0,translate:[375,-375],timingFunc:"ease-in"},{percent:0.5,translate:[0,0],timingFunc:"ease-out"},{percent:0.75,translate:[15,-15],timingFunc:"ease-in"},{percent:1,translate:[0,0],timingFunc:"ease-out"}]};b0[2]={options:{duration:700,loop:g.INFINITE},iconAnis:[{percent:0,translate:[0,0],timingFunc:"ease-out"},{percent:0.5,translate:[0,-20],timingFunc:"ease-in"},{percent:1,translate:[0,0],timingFunc:"ease-out"}],shadowAnis:[{percent:0,translate:[0,0],timingFunc:"ease-out"},{percent:0.5,translate:[15,-15],timingFunc:"ease-in"},{percent:1,translate:[0,0],timingFunc:"ease-out"}]};b0[3]={options:{duration:200,useJS:true},iconAnis:[{percent:0,translate:[0,0],timingFunc:"ease-in"},{percent:1,translate:[0,-20],timingFunc:"ease-out"}],shadowAnis:[{percent:0,translate:[0,0],timingFunc:"ease-in"},{percent:1,translate:[15,-15],timingFunc:"ease-out"}]};b0[4]={options:{duration:500,useJS:true},iconAnis:[{percent:0,translate:[0,-20],timingFunc:"ease-in"},{percent:0.5,translate:[0,0],timingFunc:"ease-out"},{percent:0.75,translate:[0,-10],timingFunc:"ease-in"},{percent:1,translate:[0,-0],timingFunc:"ease-out"}],shadowAnis:[{percent:0,translate:[15,-15],timingFunc:"ease-in"},{percent:0.5,translate:[0,0],timingFunc:"ease-out"},{percent:0.75,translate:[8,-8],timingFunc:"ease-in"},{percent:1,translate:[0,0],timingFunc:"ease-out"}]};M.prototype.getHTML=function(cc){if(a8.browser.ie&&(a8.browser.ie<9 )){return[\'<v:image src="\',this.imageUrl,\'" style="position:relative;left:\'+this.imageOffset.width+"px;","; top:"+this.imageOffset.height+"px;"," width:"+this.size.width+"px;"," height:"+this.size.height+"px;","rotation:"+cc.rotation+"deg;",\'; position:absolute;" />\'].join("");}else{return[\'<img src="\',this.imageUrl,\'" style="border:none;left:\'+this.imageOffset.width+"px","; top:"+this.imageOffset.height+"px;"," width:"+this.size.width+"px;"," height:"+this.size.height+"px;",\'; position:absolute;" />\'].join("")}};a8.extend(ag.prototype,{_addDom:function(){var cL=this._config;var cN=this.content;var T=aa("label",{"class":"BMapLabel",unselectable:"on"});if(cL.title){T.title=cL.title}var cM=T.style;cM.position="absolute";cM.MozUserSelect="none";if(cL.width==0||cL.width=="auto"){cM.display="inline"}else{cM.width=cL.width+"px";cM.display="block";cM.overflow="hidden"}if(cL.clickable=="true"){cM.cursor="pointer"}else{if(!a8.browser.ie){cM.cursor="inherit"}}T.innerHTML=cN;this.map.getPanes().labelPane.appendChild(T);this.domElement=T;this.setStyle(cL.styles);return T},setPosition:function(T){if(T instanceof cd&&!this.getMarker()){this.point=this._config.position=new cd(T.lng,T.lat);this.draw()}},setRotation:function(T){this._config.rotation=T;this.setIcon(this._config.icon);},draw:function(){if(this.domElement&&this.getPosition()&&!this.getMarker()){var cL=this._config.offset||new aH(0,0);var T=this.map.pointToOverlayPixel(this.getPosition());this.domElement.style.left=(T.x+cL.width)+"px";this.domElement.style.top=(T.y+cL.height)+"px";this._updateDomZIndex()}},_draw:function(){if(this.map&&!this._marker){this.domElement=this.initialize(this.map);this.draw()}},setContent:function(T){this.content=T;if(this.domElement){this.domElement.innerHTML=T}},setOpacity:function(cL){if(cL>=0&&cL<=1){this._config.opacity=cL}if(this.domElement){var T=this.domElement.style;T.opacity=cL;T.filter="alpha(opacity="+(cL*100)+")"}},setOffset:function(T){if(!(T instanceof aH)){return}this._config.offset=new aH(T.width,T.height);if(this.getMarker()&&this.domElement){this.domElement.style.left=T.width+"px";this.domElement.style.top=T.height+"px"}else{this.draw()}},setStyle:function(T){T=T||{};this._config.styles=a8.extend(this._config.styles,T);if(this.domElement){for(var cM in T){try{this.domElement.style[cM]=T[cM]}catch(cL){}}}},setTitle:function(T){this._config.title=T+"";if(this.domElement){this.domElement.title=this._config.title}}});');
_jsload&&_jsload('infowindow', 'a8.extend(bP.prototype,{initialize:function(cL){var cM=this.map=cL.map;this.overlay=cL;this.render();this._bind();if(this._config.enableMaximize){this.enableMaximize()}else{this.disableMaximize()}this.setTitle(this._config.title);this.setContent(this.content,true);if(this._config.ifMaxScene){this.setMaxScene(true)}this.redraw(null,true);var T=cM.infoWindowDoms;if(T){if(cL instanceof ad){T.marker=cL}else{T.marker=null}}},render:function(){var cQ=this.map,cN=this,cL=cQ.infoWindowDoms,cR=cN.IMG_PATH;if(!cL){cL=cQ.infoWindowDoms={};cQ.infoWindow=cQ.infoWindowDoms;var cM=[\'<div class="BMap_shadow" style="position: absolute;display:none" type="infowindow_shadow">\'];cM.push(\'<div><img onmousedown="return false" style="left: -323px; top: 0px;" src="\'+cR+\'iws3.png"/></div>\');cM.push(\'<div><img onmousedown="return false" style="left: -393px; top: 0px;" src="\'+cR+\'iws3.png"/></div>\');cM.push(\'<div><img onmousedown="return false" style="left: -1033px; top: 0px;" src="\'+cR+\'iws3.png"/></div>\');cM.push(\'<div><img onmousedown="return false" style="top: -30px;" src="\'+cR+\'iws3.png"/></div>\');cM.push(\'<div><img onmousedown="return false" style="left: -360px; top: -30px;" src="\'+cR+\'iws3.png"/></div>\');cM.push(\'<div><img onmousedown="return false" style="top: -30px;" src="\'+cR+\'iws3.png"/></div>\');cM.push(\'<div><img onmousedown="return false" style="left: -14px; top: -310px;" src="\'+cR+\'iws3.png"/></div>\');cM.push(\'<div><img onmousedown="return false" style="left: -255px; top: -310px;" src="\'+cR+\'iws3.png"/></div>\');cM.push(\'<div><img onmousedown="return false" style="left: -440px; top: -310px;" src="\'+cR+\'iws3.png"/></div>\');cM.push(\'<div><img onmousedown="return false" style="left: -255px; top: -310px;" src="\'+cR+\'iws3.png"/></div>\');cM.push(\'<div><img onmousedown="return false" style="left: -754px; top: -310px;" src="\'+cR+\'iws3.png"/></div>\');cM.push("</div>");cM.push(\'<div class="BMap_pop" style="position:absolute;display:none;cursor:default">\');cM.push(\'<div><div style="background:#fff;border-top:1px solid #ababab;border-left:1px solid #ababab;width:30px;height:30px;"></div></div>\');cM.push(\'<div class="BMap_top"></div>\');cM.push(\'<div><div style="position:absolute;top:0;left:-6px;background:#fff;border-top:1px solid #ababab;border-right:1px solid #ababab;width:30px;height:30px;"></div></div>\');cM.push(\'<div class="BMap_center"></div>\');cM.push(\'<div><div style="position:absolute;top:-6px;left:0;background:#fff;border-bottom:1px solid #ababab;border-left:1px solid #ababab;width:30px;height:30px;"></div></div>\');cM.push(\'<div class="BMap_bottom"></div>\');cM.push(\'<div><div style="position:absolute;top:-6px;left:-6px;background:#fff;border-right:1px solid #ababab;border-bottom:1px solid #ababab;width:30px;height:30px;"></div></div>\');cM.push(\'<div><img style="border:none;margin:0px;padding:0px;position:absolute;left:-186px;top:-691px;width:690px;height:786px;" src="\'+cR+\'iw3.png"/></div>\');cM.push(\'<div style="overflow-y:hidde;overflow-x:hidde;width:auto;height:auto;position:absolute;left:16px; top:16px;z-index:10;"></div>\');cM.push("</div>");cL.popDom=at(cQ.platform,cM.join(""));cL.shadowDom=cL.popDom.previousSibling;cL.popDivs=cL.popDom.children;cL.shadowDivs=cL.shadowDom.getElementsByTagName("div");cL.contentMain=cL.popDivs[8];cL.titleDiv=at(cL.popDivs[8],\'<div class="BMap_bubble_title" style="display:block;overflow:hidden;height:24px;line-height:24px;white-space:nowrap"></div>\');cL.contentDiv=at(cL.popDivs[8],\'<div class="BMap_bubble_content" style="display:block"></div>\');cL.maxContentDiv=at(cL.popDivs[8],\'<div class="BMap_bubble_max_content" style="display:none;position:relative"></div>\');var T=10;if(a8.platform.isIphone||a8.platform.isAndroid){T=20}var cO=\'<img style="position:absolute;top:12px;width:\'+T+"px;height:"+T+\'px;-moz-user-select:none;cursor:pointer;z-index:10000;" src="\'+cR+\'iw_close1d3.gif"/>\';cL.closeButton=at(cL.popDom,cO);var cP=\'<img style="position:absolute;top:12px;width:\'+T+"px;height:"+T+\'px;-moz-user-select:none;cursor:pointer;z-index:10000;display:none" src="\'+cR+\'iw_plus1d3.gif"/>\';cL.maxButton=at(cL.popDom,cP);this._mendIE6(cL)}cL.guid=cL.popDom.guid=this.guid},_mendIE6:function(cN){if(!a8.browser.ie||a8.browser.ie>6){return}var cM=cN.popDom.getElementsByTagName("IMG");for(var cL=0;cL<cM.length;cL++){if(cM[cL].src.indexOf(".png")<0){continue}cM[cL].style.cssText+=";FILTER: progid:DXImageTransform.Microsoft.AlphaImageLoader(src="+cM[cL].src+",sizingMethod=crop)";cM[cL].src=this.IMG_PATH+"blank.gif"}var T=cN.shadowDom.getElementsByTagName("IMG");for(var cL=0;cL<T.length;cL++){T[cL].style.cssText+=";FILTER: progid:DXImageTransform.Microsoft.AlphaImageLoader(src="+T[cL].src+",sizingMethod=crop)";T[cL].src=this.IMG_PATH+"blank.gif"}},_bind:function(){var cQ=this,cR=cQ.map,cN=cR.infoWindowDoms,cM=cN.closeButton,cO=cN.popDom;var cL=function(cS){cQ.dispatchEvent(new bg("onclickclose"));cQ.overlay&&cQ.overlay.closeInfoWindow();co(cS)};cM.onclick=cL;var cP=["touchstart","touchmove","gesturestart","gesturechange","mousedown","mouseout","mouseover","click","mousewheel","keydown","selectstart"];a8.array.each(cP,function(cS){a8.on(cO,cS,aP)});a8.on(cO,"dblclick",co);a8.on(cO,"contextmenu",co);if(a8.browser.firefox>=4){a8.on(cO,"mouseup",function(cS){if(cS.button==2){co(cS)}})}if(window.addEventListener){cO.addEventListener("DOMMouseScroll",aP,false)}var T=function(cT){var cS=cQ.map.infoWindowDoms.maxButton;if(!cQ.isWinMax){cQ.maximize()}else{cQ.restore()}co(cT)};cN.maxButton.onclick=T;if(aA()){a8.on(cM,"touchend",cL);a8.on(cN.maxButton,"touchend",T)}cO=cM=cN=null},_setWinSize:function(cT,cS){var c3=this._config;cT=cT||c3.width;cS=cS||c3.height;if(cS<0){cS=0}var cN=c3.offset.width;var cR=c3.offset.height;var c2=[25,-1,25,-1,25,-1,25,34];var cM=[25,-1,25,-1,25,-1,25,50];c2[1]=cT-c2[0]-c2[2];if(a8.browser.ie&&document.compatMode!="CSS1Compat"){c2[3]=cT}else{c2[3]=cT-2}c2[5]=cT-c2[4]-c2[6];cM[1]=cM[0];cM[3]=cS-cM[0]-cM[4];if(a8.browser.ie&&document.compatMode!="CSS1Compat"){cM[5]=cM[4]}else{cM[5]=cM[4]-1}var c4=[0,c2[0],cT-c2[2],0,0,c2[4],cT-c2[6],Math.ceil((cT-c2[7])/2)];var cY=[0,0,0,cM[0],cS-c2[4],cS-c2[4],cS-c2[4],cS-c2[4]];var cL=this._allPopLeft=cN-Math.round((cT-c2[7])/2);var cZ=this._allPopTop=cR-cS-24;var c5=Math.floor((cS+cM[7])/2.03)+30;var cP=[70,-1,70,-1,-1,-1,50,-1,140,-1,70];var cX=[30,30,30,25,25,25,60,60,60,60,60];cP[7]=Math.round((cT+80-(cP[6]+cP[8]+cP[10])-50)/2);cP[9]=cP[7]+50;var c1=cP[6]+cP[7]+cP[8]+cP[9]+cP[10];cP[1]=c1-cP[0]-cP[2]-29;cP[5]=cP[3]=c5-cX[0]-cX[6]+70;cX[3]=cX[4]=cX[5]=c5-cX[0]-cX[6];cP[4]=(cP[0]+cP[1]+cP[2]+cX[3]+29)-cP[5]-cP[3];var T=[c5-60-1,c5-60-1+cP[0],c5-60-1+cP[0]+cP[1],29,29+cP[3],29+cP[3]+cP[4],0,cP[6],cP[6]+cP[7],cP[6]+cP[7]+cP[8],cP[6]+cP[7]+cP[8]+cP[9]];var cW=[0,0,0,cX[0],cX[0],cX[0],cX[0]+cX[3],cX[0]+cX[3],cX[0]+cX[3],cX[0]+cX[3],cX[0]+cX[3]];this._allShadowLeft=cN-cP[6]-cP[7]-70;this._allShadowTop=cR-c5+30;var cO=323-c5+90;shadowRightImageLeft=740+cO;var c0=function(c6){return"overflow: hidden; z-index: 1; position: absolute;              left:"+c4[c6]+"px;              top:"+cY[c6]+"px;              width:"+c2[c6]+"px;              height:"+cM[c6]+"px"};var cU=function(c6){return"overflow: hidden; z-index: 1; position: absolute;              left:"+T[c6]+"px;              top:"+cW[c6]+"px;              width:"+cP[c6]+"px;              height:"+cX[c6]+"px;"};var cQ=this.map.infoWindowDoms;if(cQ&&cQ.popDivs){for(var cV=0;cV<8;cV++){cQ.popDivs[cV].style.cssText=c0(cV)}}if(cQ&&cQ.shadowDivs){for(var cV=0;cV<cQ.shadowDivs.length;cV++){cQ.shadowDivs[cV].style.cssText=cU(cV)}cQ.shadowDivs[3].firstChild.style.left="-"+cO+"px";cQ.shadowDivs[5].firstChild.style.left="-"+shadowRightImageLeft+"px"}this.setPosition()},setWidth:function(T){T=T*1;if(!T&&T!=0||isNaN(T)||T<0){return}if(T!=0){if(T<220){T=220}if(T>730){T=730}}this._config.width=T;if(this._isMyDom()&&this.isOpen()){var cL=this;this.redraw(function(){cL.setPanToWithDelay()})}},setHeight:function(T){T=T*1;if(!T&&T!=0||isNaN(T)||T<0){return}if(T!=0){if(T<60){T=60}if(T>650){T=650}}this._config.height=T;var cM=this.map;if(this._isMyDom()&&this.isOpen()){if(this._config.width!=0){cM.infoWindowDoms.contentDiv.style.width=this._config.width+"px"}var cL=this;this.redraw(function(){cL.setPanToWithDelay()})}},setMaxWidth:function(T){T=T*1;if(!T&&T!=0||isNaN(T)||T<0){return}if(T!=0){if(T<220){T=220}if(T>730){T=730}}this._config.maxWidth=T;if(this.isWinMax){this.redraw()}},setTitle:function(cN){this._config.title=cN;if(!this._isMyDom()){return}var cM=this.map;var cL=cM.infoWindowDoms.titleDiv;if(!cN){a8.dom.hide(cL)}else{if(b3(cN)){cL.innerHTML=cN}else{cL.innerHTML="";cL.appendChild(cN)}a8.dom.show(cL)}var T=this;this.redraw(function(){T.setPanToWithDelay()})},setContent:function(cN,cP){this.content=cN;if(!this._isMyDom()){return}if(this.isWinMax){return}var cO=this.map;var cL=cO.infoWindowDoms.contentDiv;var T=cO.infoWindowDoms.maxContentDiv;if(b3(cN)){cL.innerHTML=cN}else{cL.innerHTML="";cL.appendChild(cN)}if(this._config.width!=0){cL.style.width=this._config.width+"px"}T.style.display="none";cL.style.display="";if(!cP){var cM=this;this.redraw(function(){cM.setPanToWithDelay()})}},setMaxContent:function(cL){if(!cL){cL=this._config.maxContent}else{this._config.maxContent=cL}var cM=this.map;if(!this._isMyDom()){return}var T=cM.infoWindowDoms;T.maxContentDiv.innerHTML=cL;if(!this.isWinMax){return}T.contentDiv.style.display="none";T.maxContentDiv.style.display=""},redraw:function(cU,cQ){if(!this._isMyDom()){return}if(!cQ&&!this.isOpen()){return}var cM=this,cP=1,cN=cM.map.infoWindowDoms,cL=0;cU=cU||function(){};if(cN.titleDiv.style.display!="none"){cL=24}var cO=7,cT=20;if(a8.platform.isIphone||a8.platform.isAndroid){cO=-1;cT=25}if(this.isWinMax){cS=cM._config.maxWidth;setTimeout(function(){var cW=cL+cN.maxContentDiv.scrollHeight;cW=cW>cM.map.height?cM.map.height-60:cW;cS=cS<220?220:cS;cS=cS>600?600:cS;cW=cW<55?55:cW;cW=cW>440?440:cW;cM._setWinSize(cS+32,cW+32);cN.contentMain.style.width=cS+"px";cN.contentMain.style.height=cW+"px";cN.closeButton.style.left=cS+cO+"px";cN.maxButton.style.left=cS-cT+cO+"px";cN.contentMain.style.overflow="hidden";cM.dispatchEvent(new bg("onresize"));cU()},cP)}else{var cV=cN.contentDiv.style,cR=cN.titleDiv.style,T=cN.contentMain.style;cV.width=T.width=cR.width="auto";cV.height=T.height=cR.height="auto";cV.whiteSpace="nowrap";if(cN.popDom.style.display=="none"){this.show()}cN.popDom.style.visibility="hidden";cN.shadowDom.style.visibility="hidden";var cS=cN.contentMain.clientWidth||0;cS=cM._config.width==0?cS:cM._config.width;cS=cS<220?220:cS;cS=cS>600?600:cS;T.width=cS+"px";h=cN.contentMain.scrollHeight||0;h=cM._config.height==0?h:cM._config.height;cM._setWinSize(cS+32,h+32);setTimeout(function(){cV.whiteSpace="";if(cM._config.width==0){T.overflowX="hidden"}else{T.overflowX="auto"}if(cM._config.height==0){T.overflowY="hidden"}else{T.overflowY="auto"}h=cN.contentMain.scrollHeight||0;h=cM._config.height==0?h:cM._config.height;h=h<55?55:h;h=h>440?440:h;cM._setWinSize(cS+32,h+32);cN.popDom.style.visibility="";cN.shadowDom.style.visibility="";T.height=h+"px";cN.closeButton.style.left=cS+cO+"px";cN.maxButton.style.left=cS-cT+cO+"px";cM.dispatchEvent(new bg("onresize"));cU()},cP)}},setPosition:function(){if(!this._isMyDom()){return}var T=this.map.infoWindowDoms,cL=this.overlay,cN=this.map.pointToOverlayPixel(cL.getPosition()),cM=cL.getIcon(),cO=new bu(cN.x-cM.anchor.width+cM.infoWindowAnchor.width+cL.getOffset().width,cN.y-cM.anchor.height+cM.infoWindowAnchor.height+cL.getOffset().height);T.popDom.style.left=this._allPopLeft+cO.x+"px";T.popDom.style.top=this._allPopTop+cO.y+"px";T.shadowDom.style.left=this._allShadowLeft+cO.x+"px";T.shadowDom.style.top=this._allShadowTop+cO.y+"px"},setPanToWithDelay:function(T){var cL=this;setTimeout(function(){cL.setPanTo()},T||200)},setPanTo:function(){if(!this.overlay||!this.overlay.getPosition()||!this._config.enableAutoPan||!this._isMyDom()){return}var c0=this.map;var cS=c0.infoWindowDoms;var cM=cS.popDivs;var c1=cS.popDom;if(!cM||!c1){return}var cN=parseInt(cM[3].style.width)+2;var cX=parseInt(cM[1].style.height)+parseInt(cM[3].style.height)+parseInt(cM[7].style.height);var cV=parseInt(c1.style.left)+this.map.offsetX;var cP=parseInt(c1.style.top)+this.map.offsetY;var cR=new bu(cV,cP);var cQ=new bu(cN+cV,cX+cP);if(this._config.height!=0&&document.all){if(!c0.temp.infoKey){c0.temp.infoKey=-1}var cY=-c0.temp.infoKey;c0.temp.infoKey=-c0.temp.infoKey}var cY=0;var cW=0;var T=10;var cL=this._config.margin[0];var cO=this._config.margin[1];var cZ=this._config.margin[2];var cT=this._config.margin[3];if(cR.x<cT){cY=-cR.x+cT}if(cR.y<cL){cW=-cR.y+cL}if(cQ.x>c0.width-cO){cY=c0.width-cQ.x-cO}if(cQ.y>c0.height-cZ){cW=c0.height-cQ.y-cZ}this._loadCollisions();var cU=this._config.collisions;if(cR.x<cU[0][0]&&cR.y<cU[0][1]){if(Math.abs(-cR.x+cU[0][0])<Math.abs(-cR.y+cU[0][1])){cY=-cR.x+cU[0][0]}else{if(c0.height-cU[0][1]-cU[3][1]<cX){cY=-cR.x+cU[0][0]}else{cW=-cR.y+cU[0][1]}}if(c0.width-cU[0][0]-cU[1][0]<cN&&cR.y<cU[1][1]){cW=-cR.y+cU[1][1]}}if(cQ.x>c0.width-cU[1][0]&&cR.y<cU[1][1]){if(Math.abs(-cQ.x+c0.width-cU[1][0])<Math.abs(-cR.y+cU[1][1])&&c0.width-cU[0][0]-cU[1][0]>=cN){cY=-cQ.x+c0.width-cU[1][0]}else{cW=-cR.y+cU[1][1];if(c0.width-cU[0][0]-cU[1][0]<cN&&c0.width-cU[0][0]<cN){cY=-cR.x+cU[0][0]}}}if(cR.x<cU[3][0]&&cQ.y>c0.height-cU[3][1]){if(Math.abs(-cR.x+cU[3][0])<Math.abs(-cQ.y+c0.height-cU[3][1])&&(Math.abs(-cR.x+cU[3][0])<Math.abs(cW)&&cW!=0||cW==0)&&c0.width-cU[3][0]>=cN){cY=-cR.x+cU[3][0]}else{cW=-cQ.y+c0.height-cU[3][1]}if(c0.height-cU[0][1]-cU[3][1]<cX&&cR.x<cU[0][0]){cY=-cR.x+cU[0][0]}}if(cQ.x>c0.width-cU[2][0]&&cQ.y>c0.height-cU[2][1]){if(Math.abs(-cQ.x+c0.width-cU[2][0])<Math.abs(-cQ.y+c0.height-cU[2][1])&&(Math.abs(-cQ.x+c0.width-cU[2][0])<Math.abs(cW)&&cW!=0||cW==0)&&c0.width-cU[0][0]-cU[1][0]>=cN){cY=-cQ.x+c0.width-cU[2][0]}else{if(c0.height-cU[1][1]-cU[2][1]>=cX){cW=-cQ.y+c0.height-cU[2][1]}else{cW=-cR.y+cU[1][1]}if(c0.width-cU[0][0]-cU[2][0]<cN){cY=-cR.x+cU[0][0]}}}if(cY!=0||cW!=0){c0.panBy(cY,cW)}},_loadCollisions:function(){if(!this.map){return}var cL=this.map;this._config.collisions=[[10,10],[10,10],[10,10],[10,10]];var cU=this.map.container;for(var cS=0,cM=cU.children.length;cS<cM;cS++){var cP,cV,cY=!!(ch(cU.children[cS]._anchor)&&cU.children[cS]._offset);if(cU.children[cS]._jsobj&&cU.children[cS]._jsobj instanceof cp&&cU.children[cS]._jsobj.blockInfoWindow==true){cP=cU.children[cS]}else{if(cY){cP=cU.children[cS]}else{continue}}var cW=cP.offsetWidth,cT=cP.offsetHeight,T=cP._jsobj,cO,cR;if(!T||cY){if(ch(cP._anchor)&&cP._offset&&aJ(cP).display!="none"&&aJ(cP).visibility!="hidden"){cO=cP._offset;cR=cP._anchor}else{continue}}else{if(T.isVisible()==false){continue}cO=T.getOffset();cR=T.getAnchor()}switch(cR){case BMAP_ANCHOR_TOP_LEFT:cV=0;break;case BMAP_ANCHOR_TOP_RIGHT:cV=1;break;case BMAP_ANCHOR_BOTTOM_LEFT:cV=3;break;case BMAP_ANCHOR_BOTTOM_RIGHT:cV=2;break;default:break}var cQ=cW+cO.width+10,cN=cT+cO.height+10,cX=this._config.collisions[cV];this._config.collisions[cV]=[cQ>cX[0]?cQ:cX[0],cN>cX[1]?cN:cX[1]]}},enableMaximize:function(){this._config.enableMaximize=true;if(this._isMyDom()){this.map.infoWindowDoms.maxButton.style.display="block"}},disableMaximize:function(){this._config.enableMaximize=false;if(this._isMyDom()){this.map.infoWindowDoms.maxButton.style.display="none"}},show:function(){if(!this._isMyDom()){return}var cL=this.map.infoWindowDoms;if(cL.popDom.style.display!="none"){return}if(cJ(this.content)){cL.contentDiv.appendChild(this.content)}if(cJ(this._config.title)){cL.titleDiv.appendChild(this._config.title)}a8.dom.show(cL.popDom);a8.dom.show(cL.shadowDom);var T=new bg("onopen");T.point=this.getPosition();this.dispatchEvent(T);this.redraw()},hide:function(){if(!this._isMyDom()){return false}var cL=this.map.infoWindowDoms;if(cL.popDom.style.display=="none"){return false}if(this._config.onClosing()==false){return false}if(cJ(this.content)){cL.contentDiv.removeChild(this.content)}if(cJ(this._config.title)){cL.titleDiv.removeChild(this._config.title)}a8.dom.hide(cL.popDom);a8.dom.hide(cL.shadowDom);if(this.isWinMax){this.isWinMax=false;cL.maxContentDiv.style.display="none";cL.contentDiv.style.display="";cL.maxButton.src=this.IMG_PATH+"iw_plus1d3.gif"}var T=new bg("onclose");T.point=this.getPosition();this.dispatchEvent(T);if(this.map.temp._clickCloseBindTimer){clearTimeout(this.map.temp._clickCloseBindTimer);this.map.temp._clickCloseBindTimer=null}else{this.map.removeEventListener("click",this.map.temp._clickCloseHandler);this.map.temp._clickCloseBinded=false}a8.lang.decontrol(this.guid);return true},maximize:function(){if(!this.map||!this.isOpen()||!this._config.enableMaximize||this.isWinMax){return}if(!this._isMyDom()){return}var cL=this.map.infoWindowDoms.maxButton;var T=this;T.isWinMax=true;cL.src=T.IMG_PATH+"iw_minus1d3.gif";T.setMaxContent();T.map.infoWindowDoms.maxContentDiv.style.display="block";T.redraw();T.dispatchEvent(new bg("onmaximize"));T.setPanToWithDelay()},restore:function(){if(!this.map||!this.isOpen()||!this.isWinMax){return}if(!this._isMyDom()){return}this.isWinMax=false;var T=this;var cL=T.map.infoWindowDoms.maxButton;cL.src=T.IMG_PATH+"iw_plus1d3.gif";T.setContent(this.content,true);T.map.infoWindowDoms.maxContentDiv.style.display="none";T.redraw();T.dispatchEvent(new bg("onrestore"));T.setPanToWithDelay()},_revert:function(){if(!this._isMyDom()){return}this.isWinMax=false;var T=this.map.infoWindowDoms;T.titleDiv.innerHTML="";T.contentDiv.innerHTML="";T.maxContentDiv.innerHTML="";T.maxButton.src=this.IMG_PATH+"iw_plus1d3.gif"},_setOverflow:function(){var cM=this.map;if(!this._isMyDom()){return}var cL=cM.infoWindowDoms,T=cL.contentMain.style;cL._overflowX=T.overflowX;cL._overflowY=T.overflowY;T.overflowX="hidden";T.overflowY="hidden"},_resetOverflow:function(){var cM=this.map;if(!this._isMyDom()||!cM.infoWindowDoms._overflowX||!cM.infoWindowDoms._overflowY){return}var cL=cM.infoWindowDoms,T=cL.contentMain.style;T.overflowX=cL._overflowX;T.overflowY=cL._overflowY;delete cL._overflowX;delete cL._overflowY},isOpen:function(){if(!this.map){return false}var T=this.map.temp.infoWin;if(!T){return false}if(!this._isMyDom()){return false}if(T&&T.overlay===this.overlay&&this.map.infoWindowDoms&&this.map.infoWindowDoms.popDom.style.display=="none"){return false}else{return true}},setMaxScene:function(cL){var cM=this;if(!this._isMyDom()){return}var T=cM.map.infoWindowDoms;T.maxButton.style.display="block";var cN=T.maxButton;if(!!cL==!!this.isWinMax){return}if(cL){cM.isWinMax=true;cN.src=cM.IMG_PATH+"iw_minus1d3.gif";cM.setMaxContent();T.maxContentDiv.style.display="block"}else{cM.isWinMax=false;cN.src=cM.IMG_PATH+"iw_plus1d3.gif";cM.setContent(this.content,true);T.maxContentDiv.style.display="none"}this.redraw()},_draw:function(){if(this._visible==true&&this.overlay){this.overlay.openInfoWindow(this)}},_isMyDom:function(){return(this.map&&this.map.infoWindowDoms&&this.map.infoWindowDoms.guid==this.guid)}});Y.prototype.openInfoWindow=function(cN){var cM=this.map;if(!cM||!this.domElement||this.isVisible()==false||!cN instanceof bP){return}var cL=cM.temp;if(cL.infoWin&&cL.infoWin.overlay&&cL.infoWin.overlay._fromMap){cM.closeInfoWindow()}if(cL.infoWin===cN&&cL.infoWin.isOpen()&&cL.infoWin.overlay===this){cN.setPanToWithDelay();return}cM.closeInfoWindow();this.infoWindow=cN;if(cL.infoWin==null||cL.infoWin!=cN){if(cM.infoWindowDoms){cM.infoWindowDoms.closeButton.onclick=null;cM.infoWindowDoms.maxButton.onclick=null}cL.infoWin=cN;cN.initialize(this)}else{cN.redraw(null,true)}a8.lang.Class.call(cN,cN.guid);if(!cL._clickCloseHandler){cL._clickCloseHandler=function(cO){if(!cO.overlay){if(cM.temp.infoWin&&cM.temp.infoWin._config.enableCloseOnClick){cM.closeInfoWindow();cM.removeEventListener("click",arguments.callee);cL._clickCloseBinded=false}}}}if(!cL._clickCloseBinded){cL._clickCloseBindTimer=setTimeout(function(){cM.addEventListener("click",cL._clickCloseHandler);cL._clickCloseBinded=true;cL._clickCloseBindTimer=null},200)}if(cL._infoWin){delete cL._infoWin}cN.overlay=this;var T=cM.infoWindowDoms;this.map.getPanes().floatPane.appendChild(T.popDom);this.map.getPanes().floatShadow.appendChild(T.shadowDom);cN.setPanToWithDelay();this.dispatchEvent(new bg("oninfowindowopen"))};Y.prototype.closeInfoWindow=function(){if(!this.map||!this.map.infoWindowDoms){return}var cL=this;if(cL.infoWindow&&cL.infoWindow.guid==cL.map.infoWindowDoms.guid){try{if(cL.infoWindow.hide()==true){cL.dispatchEvent(new bg("oninfowindowclose"));cL.map.temp.infoWin=cL.infoWindow=null}}catch(T){}}};');