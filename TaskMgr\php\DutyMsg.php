<?php
require_once '../../conn_waf.php';
/**
 * 向值班中心发送值班信息短信
 * @return string 发送结果
 */
function sendMsgToZCS() {
    global $conn;
    $retmsg = '';
    $phone = '15982699656,13982631895';
    $fromUserName = "岳池县公安局";
    
    // 计算明日日期
    $tomorrow = date('Y-m-d', strtotime('+1 day'));
    $tomorrowFormatted = date('n月j日', strtotime('+1 day'));
    
    // 查询明日值班信息
    $sql = "SELECT a_dir, b_dir, a_com, b_com, sub FROM 6_duty_sched WHERE sched_date = '$tomorrow'";
    $result = mysqli_query($conn, $sql);
    if (!$result) {
        $retmsg .= "值班信息查询失败: " . mysqli_error($conn) . "\n";
        return $retmsg;
    }
    
    $dutyInfo = mysqli_fetch_assoc($result);
    if (!$dutyInfo) {
        $retmsg .= "未找到明日的值班安排记录\n";
        return $retmsg;
    }
    
    // 获取各岗位人员姓名
    $a_dir_name = getUserNameById($dutyInfo['a_dir']);
    $b_dir_name = getUserNameById($dutyInfo['b_dir']);
    $a_com_name = getUserNameById($dutyInfo['a_com']);
    $b_com_name = getUserNameById($dutyInfo['b_com']);
    $sub_name = getUserNameById($dutyInfo['sub']);
    
    // 准备短信内容
    $content = "明日（{$tomorrowFormatted}）值班局领导：{$a_dir_name}、{$b_dir_name}，指挥长：{$a_com_name}、{$b_com_name}，值班长：{$sub_name}，请知晓。";
    
    // 发送短信
    $postData = [
        'toUserMobile' => $phone,
        'content' => $content,
        'fromUserName' => $fromUserName
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://10.152.110.168/api/SmsSystem.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        $retmsg .= "短信发送失败：" . curl_error($ch) . "\n";
    } else {
        $retmsg .= "已成功发送短信到{$phone}\n";
        $retmsg .= "响应：$response\n";
    }
    
    curl_close($ch);
    return $retmsg;
}

/**
 * 根据用户ID获取用户姓名
 * @param int $userId 用户ID
 * @return string 用户姓名
 */
function getUserNameById($userId) {
    global $conn;
    $userId = intval($userId);
    if ($userId <= 0) {
        return '未知用户';
    }
    
    $sql = "SELECT name FROM 3_user WHERE id = $userId";
    $result = mysqli_query($conn, $sql);
    if (!$result) {
        return '未知用户';
    }
    
    $user = mysqli_fetch_assoc($result);
    return $user ? $user['name'] : '未知用户';
}

/**
 * 发送值班通知短信给局领导
 * @return string 发送结果信息
 */
function sendMsgToDir() {
    global $conn;
    $retmsg = '';
    
    // 计算明日日期
    $tomorrow = date('Y-m-d', strtotime('+1 day'));
    $tomorrowFormatted = date('n月j日', strtotime('+1 day'));
    
    // 查询明日值班安排
    $sql = "SELECT a_dir, b_dir, a_com, b_com FROM 6_duty_sched WHERE sched_date = '$tomorrow'";
    $result = mysqli_query($conn, $sql);
    $dutySchedule = mysqli_fetch_assoc($result);
    
    if (!$dutySchedule) {
        $retmsg .= "未找到明日值班安排记录\n";
        return $retmsg;
    }
    
    // 获取指挥长姓名
    $comUserIds = array_filter([$dutySchedule['a_com'], $dutySchedule['b_com']]);
    if (empty($comUserIds)) {
        $retmsg .= "值班安排中没有指定指挥长\n";
        return $retmsg;
    }
    $comUserIdList = implode(',', $comUserIds);
    $sql = "SELECT name FROM 3_user WHERE id IN ($comUserIdList)";
    $result = mysqli_query($conn, $sql);
    $comNames = [];
    while ($user = mysqli_fetch_assoc($result)) {
            $comNames[] = $user['name'];
    }
    $comNamesStr = implode('和', $comNames);
    
    // 处理a_dir人员
    if (!empty($dutySchedule['a_dir'])) {
        $aDirId = intval($dutySchedule['a_dir']);
        $sql = "SELECT name, phone FROM 3_user WHERE id = $aDirId";
        $result = mysqli_query($conn, $sql);
        $user = mysqli_fetch_assoc($result);
        
        if ($user && !empty($user['phone'])) {
            $content = "你为明日（{$tomorrowFormatted}）A岗值班局领导，{$comNamesStr}为值班指挥长，请知晓。";
            $fromUserName = "岳池县公安局";
            
            $postData = [
                'toUserMobile' => $user['phone'],
                'content' => $content,
                'fromUserName' => $fromUserName
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://10.152.110.168/api/SmsSystem.php');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($ch);
            
            if (curl_errno($ch)) {
                $retmsg .= "发送A岗局领导短信失败：" . curl_error($ch) . "\n";
            } else {
                $retmsg .= "已成功通知A岗局领导{$user['name']}({$user['phone']})\n";
                $retmsg .= "响应：$response\n";
            }
            
            curl_close($ch);
        } else {
            $retmsg .= "未找到A岗局领导有效的用户信息或电话号码 (用户ID: $aDirId)\n";
        }
    } else {
        $retmsg .= "值班安排中没有指定A岗局领导\n";
    }
    
    // 处理b_dir人员
    if (!empty($dutySchedule['b_dir'])) {
        $bDirId = intval($dutySchedule['b_dir']);
        $sql = "SELECT name, phone FROM 3_user WHERE id = $bDirId";
        $result = mysqli_query($conn, $sql);
        $user = mysqli_fetch_assoc($result);
        
        if ($user && !empty($user['phone'])) {
            $content = "你为明日（{$tomorrowFormatted}）B岗值班局领导，{$comNamesStr}为值班指挥长，请知晓。";
            $fromUserName = "岳池县公安局";
            
            $postData = [
                'toUserMobile' => $user['phone'],
                'content' => $content,
                'fromUserName' => $fromUserName
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://10.152.110.168/api/SmsSystem.php');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($ch);
            
            if (curl_errno($ch)) {
                $retmsg .= "发送B岗局领导短信失败：" . curl_error($ch) . "\n";
            } else {
                $retmsg .= "已成功通知B岗局领导{$user['name']}({$user['phone']})\n";
                $retmsg .= "响应：$response\n";
            }
            
            curl_close($ch);
        } else {
            $retmsg .= "未找到B岗局领导有效的用户信息或电话号码 (用户ID: $bDirId)\n";
        }
    } else {
        $retmsg .= "值班安排中没有指定B岗局领导\n";
    }
    
    return $retmsg;
}
/**
 * 发送值班通知短信给指挥中心人员
 * @return void
 */
function sendMsgToCom() {
    global $conn;
    $retmsg = '';
    // 计算明日日期
    $tomorrow = date('Y-m-d', strtotime('+1 day'));
    $tomorrowFormatted = date('n月j日', strtotime('+1 day'));
    // 获取明日是星期几 (1=周一, 7=周日)
    $dayOfWeek = date('N', strtotime('+1 day'));
    // 查询明日值班安排
    $sql = "SELECT a_com, b_com FROM 6_duty_sched WHERE sched_date = '$tomorrow'";
    $result = mysqli_query($conn, $sql);
    $dutySchedule = mysqli_fetch_assoc($result);

    if (!$dutySchedule) {
        return "未找到明日值班安排记录\n";
    }
    // 根据星期几决定是否包含b_com用户
    $userIds = [$dutySchedule['a_com']];
    // 周一至周五(1-5)包含b_com，周末(6-7)不包含
    if ($dayOfWeek >= 1 && $dayOfWeek <= 5) {
        $userIds[] = $dutySchedule['b_com'];
    }
    $userIds = array_filter($userIds);
    if (empty($userIds)) {
        return "值班安排中没有指定人员\n";
    }

    // 查询用户信息
    $userIdList = implode(',', $userIds);
    $sql = "SELECT name, phone FROM 3_user WHERE id IN ($userIdList)";
    $result = mysqli_query($conn, $sql);

    $mobileNumbers = [];
    while ($user = mysqli_fetch_assoc($result)) {
        if (!empty($user['phone'])) {
            $mobileNumbers[] = $user['phone'];
            $retmsg .= "通知用户：{$user['name']} - {$user['phone']}\n";
        }
    }

    if (empty($mobileNumbers)) {
        return "没有需要发送短信的用户\n";
    }

    // 发送短信
    $allMobiles = implode(',', $mobileNumbers);
    $content = "您明日（{$tomorrowFormatted}）需要前往110指挥中心值班（指挥长），请准时到岗。";
    $fromUserName = "岳池县公安局";

    $postData = [
        'toUserMobile' => $allMobiles,
        'content' => $content,
        'fromUserName' => $fromUserName
    ];

    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://10.152.110.168/api/SmsSystem.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    curl_close($ch);

    $retmsg .= "短信发送完成，通知了 " . count($mobileNumbers) . " 个用户\n";
    $retmsg .= "响应：$response\n";
    return $retmsg;
}

/**
 * 发送安全值守通知短信给指定人员
 * @return string 发送结果信息
 */
function sendMsgToSec() {
    global $conn;
    $retmsg = '';
    
    // 计算明日日期
    $tomorrow = date('Y-m-d', strtotime('+1 day'));
    $tomorrowFormatted = date('n月j日', strtotime('+1 day'));
    // 查询明日值班安排中的安全值守人员
    $sql = "SELECT sec FROM 6_duty_sched WHERE sched_date = '$tomorrow'";
    $result = mysqli_query($conn, $sql);
    $dutySchedule = mysqli_fetch_assoc($result);
    
    if (!$dutySchedule) {
        $retmsg .= "未找到明日值班安排记录\n";
        return $retmsg;
    }
    
    // 解析sec字段中的人员ID数组（文本型，逗号分隔）
    $secIds = explode(',', $dutySchedule['sec']);
    if (!is_array($secIds) || empty($secIds)) {
        $retmsg .= "值班安排中没有指定安全值守人员\n";
        return $retmsg;
    }
    
    // 过滤并转义人员ID
    $userIds = array_filter(array_map('intval', $secIds));
    if (empty($userIds)) {
        $retmsg .= "没有有效的安全值守人员ID\n";
        return $retmsg;
    }
    
    // 查询用户信息
    $userIdList = implode(',', $userIds);
    $sql = "SELECT name, phone FROM 3_user WHERE id IN ($userIdList)";
    $result = mysqli_query($conn, $sql);
    
    if (!$result) {
        $retmsg .= "数据库查询错误：" . mysqli_error($conn) . "\n";
        return $retmsg;
    }
    
    $mobileNumbers = [];
    $userInfo = []; // 新增：收集用户姓名和电话
    while ($user = mysqli_fetch_assoc($result)) {
        if (!empty($user['phone'])) {
            $mobileNumbers[] = $user['phone'];
            $userInfo[$user['name']] = $user['phone']; // 新增：存储用户信息
            $retmsg .= "通知用户：{$user['name']} - {$user['phone']}\n";
        } else {
            $retmsg .= "警告：用户{$user['name']}没有有效的电话号码，跳过发送\n";
        }
    }
    
    if (empty($mobileNumbers)) {
        $retmsg .= "没有需要发送短信的用户（所有用户均无有效电话）\n";
        return $retmsg;
    }
    
    // 准备包含人员信息的短信内容
    $personList = [];
    foreach ($userInfo as $name => $phone) {
        $personList[] = "{$name}";
    }
    $personStr = implode('、', $personList);
    
    $content = "根据县局工作安排，明日（{$tomorrowFormatted}）局机关安全值守人员：{$personStr}。请准时到岗。";
    $fromUserName = "岳池县公安局";
    
    // 发送短信
    $allMobiles = implode(',', $mobileNumbers);
    $postData = [
        'toUserMobile' => $allMobiles,
        'content' => $content,
        'fromUserName' => $fromUserName
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://10.152.110.168/api/SmsSystem.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        $retmsg .= "短信发送失败：" . curl_error($ch) . "\n";
    } else {
        $retmsg .= "短信发送完成，通知了 " . count($mobileNumbers) . " 个用户\n";
        $retmsg .= "响应：$response\n";
    }
    
    curl_close($ch);
    return $retmsg;
}

/**
 * 发送通知给今日值班的sub用户，告知明日值班通知已发送
 * @return string 发送结果信息
 */
function sendMsgToTodaySub() {
    global $conn;
    $retmsg = '';
    
    // 计算今日和明日日期
    $today = date('Y-m-d');
    $tomorrow = date('Y-m-d', strtotime('+1 day'));
    $tomorrowFormatted = date('n月j日', strtotime('+1 day'));
    // 一次查询获取今日和明日的所有值班信息
    $sql = "SELECT 
                today.sub, 
                tomorrow.a_dir, tomorrow.b_dir, tomorrow.a_com, tomorrow.b_com, tomorrow.sec 
            FROM 
                6_duty_sched AS today,
                6_duty_sched AS tomorrow 
            WHERE 
                today.sched_date = '$today' 
                AND tomorrow.sched_date = '$tomorrow'";
    $result = mysqli_query($conn, $sql);
    if (!$result) {
        $retmsg .= "值班信息查询失败: " . mysqli_error($conn) . "\n";
        return $retmsg;
    }
    
    $dutyInfo = mysqli_fetch_assoc($result);
    if (!$dutyInfo) {
        $retmsg .= "未找到今日或明日的值班安排记录\n";
        return $retmsg;
    }
    
    // 检查今日sub用户
    if (empty($dutyInfo['sub'])) {
        $retmsg .= "未找到今日值班安排中的sub用户\n";
        return $retmsg;
    }
    
    // 获取用户ID (使用sub字段而不是a_com)
    $userId = intval($dutyInfo['sub']);
    if ($userId <= 0) {
        $retmsg .= "无效的sub用户ID\n";
        return $retmsg;
    }
    
    // 查询用户信息
    $sql = "SELECT name, phone FROM 3_user WHERE id = $userId";
    $result = mysqli_query($conn, $sql);
    if (!$result) {
        $retmsg .= "用户查询失败: " . mysqli_error($conn) . "\n";
        return $retmsg;
    }
    $user = mysqli_fetch_assoc($result);

    if (!is_array($user) || empty($user['phone'])) {
        $retmsg .= "未找到有效的用户信息或电话号码 (用户ID: $userId)\n";
        return $retmsg;
    }

    // 确保name字段存在
    if (!isset($user['name']) || empty($user['name'])) {
        $user['name'] = '未知用户';
    }
    
    // 准备短信内容
    $content ="明日（{$tomorrowFormatted}）值班人员已发送短信进行通知，具体名单如下:\n";
    
    // 处理局领导信息
    $dirUsers = [];
    $dirIds = array_filter([$dutyInfo['a_dir'], $dutyInfo['b_dir']]);
    if (!empty($dirIds)) {
        $dirIdList = implode(',', $dirIds);
        $sql = "SELECT id, name FROM 3_user WHERE id IN ($dirIdList)";
        $result = mysqli_query($conn, $sql);
        $dirUserData = [];
        while ($dirUser = mysqli_fetch_assoc($result)) {
            $dirUserData[$dirUser['id']] = $dirUser['name'];
        }
        
        if (isset($dirUserData[$dutyInfo['a_dir']])) {
            $dirUsers[] = "A岗：" . $dirUserData[$dutyInfo['a_dir']];
        }
        if (isset($dirUserData[$dutyInfo['b_dir']])) {
            $dirUsers[] = "B岗：" . $dirUserData[$dutyInfo['b_dir']];
        }
    }
    $content .= "局领导：" . implode('、', $dirUsers) . "\n";
    
    // 处理指挥长信息
    $comUsers = [];
    $comIds = array_filter([$dutyInfo['a_com'], $dutyInfo['b_com']]);
    if (!empty($comIds)) {
        $comIdList = implode(',', $comIds);
        $sql = "SELECT name FROM 3_user WHERE id IN ($comIdList)";
        $result = mysqli_query($conn, $sql);
        while ($comUser = mysqli_fetch_assoc($result)) {
                $comUsers[] = $comUser['name'];
        }
    }
    $content .= "指挥长：" . implode('、', $comUsers) . "\n";
    
    // 处理安全值守人员信息
    $secUsers = [];
    if (!empty($dutyInfo['sec'])) {
        $secIds = explode(',', $dutyInfo['sec']);
        $secIds = array_filter(array_map('intval', $secIds));
        if (!empty($secIds)) {
            $secIdList = implode(',', $secIds);
            $sql = "SELECT name FROM 3_user WHERE id IN ($secIdList)";
            $result = mysqli_query($conn, $sql);
            while ($secUser = mysqli_fetch_assoc($result)) {
                $secUsers[] = $secUser['name'];
            }
        }
    }
    $content .= "安全值守人员：" . implode('、', $secUsers) . "\n";
    
    // 后续代码保持不变...
    $fromUserName = "岳池县公安局";
    
    // 发送短信
    $postData = [
        'toUserMobile' => $user['phone'],
        'content' => $content,
        'fromUserName' => $fromUserName
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://10.152.110.168/api/SmsSystem.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        $retmsg .= "短信发送失败：" . curl_error($ch) . "\n";
    } else {
        $retmsg .= "已成功通知今日值班人员{$user['name']}({$user['phone']})\n";
        $retmsg .= "响应：$response\n";
    }
    
    curl_close($ch);
    return $retmsg;
}



// 调用函数
$retmsg = "局领导短信通知：\n";
$retmsg .= sendMsgToDir();
echo $retmsg;
$retmsg = "指挥长短信通知：\n";
$retmsg .= sendMsgToCom();
echo $retmsg;
$retmsg = "安全值守短信通知：\n";
$retmsg .= sendMsgToSec();
echo $retmsg;
$retmsg = "今日值班人员通知：\n";
$retmsg .= sendMsgToTodaySub();
echo $retmsg;

$retmsg = "钟朝生短信通知：\n";
$retmsg .= sendMsgToZCS();
echo $retmsg;
?>