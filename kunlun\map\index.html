﻿<html>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>百度离线版DEMO</title>
<script type="text/javascript" src="js/apiv1.3.min.js"></script>
<script type="text/javascript" src="js/TextIconOverlay_min.js"></script>
<script type="text/javascript" src="js/MarkerClusterer_min.js"></script>
<link rel="stylesheet" type="text/css" href="bmap.css"/>
</head>
<body>
<div style="width:100%;height:100%;border:1px solid gray;position:absolute;" id="container"></div>
<div id="position" style="width:220px;height:10%;border:1px solid gray;position:absolute;background-color:#cacfd2;filter: Alpha(opacity=60);opacity:0.6;  right:1px;bottom:1px;font-size:15px">
<div id="bu" style="width:100%;height:100%;position:absolute;background-color:#cacfd2;filter: Alpha(opacity=90);opacity:0.6;  right:0px;bottom:0px;font-size:15px">	
</div>

</body>
</html>
<script type="text/javascript">
	lng=106.44307;
	lat=30.5371;	
	var mapOptions = {
		//minZoom: 12, 地图最小层级
		mapType: BMAP_NORMAL_MAP
	}
	var map = new BMap.Map("container", mapOptions);      //设置卫星图为底图BMAP_PERSPECTIVE_MAP
	//map.addControl(new BMap.MapTypeControl({mapTypes: [BMAP_NORMAL_MAP,BMAP_SATELLITE_MAP,BMAP_HYBRID_MAP]})); //负责切换地图类型的控件
	var initPoint = new BMap.Point(lng, lat);    // 创建点坐标
	
	map.centerAndZoom(initPoint,17);                    // 初始化地图,设置中心点坐标和地图级别。
	map.enableScrollWheelZoom();                  // 启用滚轮放大缩小。
	map.enableKeyboard();                         // 启用键盘操作。  
	//map.enableContinuousZoom();										//启用连续缩放效果
	
	// ----- control -----
	map.addControl(new BMap.NavigationControl()); //地图平移缩放控件
	map.addControl(new BMap.ScaleControl()); //显示比例尺在右下角
	//map.addControl(new BMap.OverviewMapControl({anchor: BMAP_ANCHOR_TOP_RIGHT, isOpen: true})); //缩略图控件
	
	// ----- maker -----
	label="预警点";
	addCabinMarker(lng,lat,label);//设置标注点
	function addCabinMarker(lng,lat,label) { 
		point=new BMap.Point(lng,lat);
		var cabinIcon = new BMap.Icon("images/marker.png", new BMap.Size(32, 32));    
		var cabinMarkerOptions = {
			icon: cabinIcon,
			//enableDragging: true,
			//draggingCursor: "move",
			title: label,
			//rotation: 0
		}
 		var cabinMarker = new BMap.Marker(point, cabinMarkerOptions);  
 		cabinMarker.setAnimation(BMAP_ANIMATION_DROP);
 		
 		map.addOverlay(cabinMarker);
		cabinMarker.setRotation(0);
 		cabinMarker.addEventListener("dragging", function(e) {
			document.getElementById("position").innerHTML = "坐标像素</br>x :" + e.pixel.x + " y :" + e.pixel.y + "<br>坐标经纬度</br>经度: " + e.point.lng + " 纬度: " + e.point.lat;//获取经纬度
		}); 
	}
	/*
                var points=[[106.3,30.4,"point1"],[106.4,30.5,"point2"]];
	var MAX = 10;
	var markers = [];
	var pt = null;
	//var i = 0;
	for (i=0; i < points.length; i++) {
	  // pt = new BMap.Point(Math.random() * 1 + 106, Math.random() * 1 + 30);
	   pt = new BMap.Point(points[i][0], points[i][1]);
		var cabinIcon = new BMap.Icon("images/marker.png", new BMap.Size(32, 32));    
		var cabinMarkerOptions = {
			icon: cabinIcon,
			//enableDragging: true,
			//draggingCursor: "move",
			title: points[i][2],
			//rotation: 0
		}
	   markers.push(new BMap.Marker(pt,cabinMarkerOptions));
	}
	//最简单的用法，生成一个marker数组，然后调用markerClusterer类即可。
	var markerClusterer = new BMapLib.MarkerClusterer(map, {markers:markers});*/
	
//
 	
</script>
