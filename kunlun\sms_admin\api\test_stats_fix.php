<?php
/**
 * 测试统计功能修复
 */

require_once '../../db_connection.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // 测试各个统计查询
    $tests = [];
    
    // 1. 测试配置统计
    $sql = "SELECT COUNT(*) as total FROM Kunlun_sms_alert_config";
    $result = $conn->query($sql);
    $tests['config_count'] = [
        'sql' => $sql,
        'success' => $result !== false,
        'result' => $result ? $result->fetch_assoc()['total'] : 'Error: ' . $conn->error
    ];
    
    // 2. 测试预警类型分布
    $sql = "SELECT alarm_type, COUNT(*) as count FROM Kunlun_sms_alert_config WHERE is_active = 1 GROUP BY alarm_type";
    $result = $conn->query($sql);
    $tests['type_distribution'] = [
        'sql' => $sql,
        'success' => $result !== false,
        'result' => $result ? $result->fetch_all(MYSQLI_ASSOC) : 'Error: ' . $conn->error
    ];
    
    // 3. 测试短信发送日志表（可能不存在）
    $sql = "SELECT COUNT(*) as total FROM Kunlun_sms_send_log WHERE DATE(created_time) = CURDATE()";
    $result = $conn->query($sql);
    $tests['sms_log_count'] = [
        'sql' => $sql,
        'success' => $result !== false,
        'result' => $result ? $result->fetch_assoc()['total'] : 'Error: ' . $conn->error,
        'note' => 'This table may not exist in development environment'
    ];
    
    // 4. 测试预警记录表（可能不存在）
    $sql = "SELECT COUNT(*) as total FROM Kunlun_alarm_records WHERE captured_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)";
    $result = $conn->query($sql);
    $tests['alarm_records_count'] = [
        'sql' => $sql,
        'success' => $result !== false,
        'result' => $result ? $result->fetch_assoc()['total'] : 'Error: ' . $conn->error,
        'note' => 'This table may not exist in development environment'
    ];
    
    // 5. 测试表存在性
    $tables = ['Kunlun_sms_alert_config', 'Kunlun_sms_send_log', 'Kunlun_alarm_records'];
    $table_status = [];
    
    foreach ($tables as $table) {
        $sql = "SHOW TABLES LIKE '$table'";
        $result = $conn->query($sql);
        $table_status[$table] = [
            'exists' => $result && $result->num_rows > 0,
            'note' => $result && $result->num_rows > 0 ? 'Table exists' : 'Table does not exist'
        ];
    }
    
    // 6. 测试关键的统计查询修复
    $critical_fixes = [];

    // 测试预警类型分布（这是主要修复的查询）
    $sql = "SELECT
                alarm_type,
                COUNT(*) as count,
                CASE
                    WHEN alarm_type = 'sfz' THEN '身份证预警'
                    WHEN alarm_type = 'album' THEN '图库预警'
                    ELSE '未知类型'
                END as type_name
            FROM Kunlun_sms_alert_config
            WHERE is_active = 1
            GROUP BY alarm_type";
    $result = $conn->query($sql);
    $critical_fixes['type_distribution_fix'] = [
        'sql' => $sql,
        'success' => $result !== false,
        'result' => $result ? $result->fetch_all(MYSQLI_ASSOC) : 'Error: ' . $conn->error,
        'note' => 'This replaces the old alert_level query that was causing errors'
    ];

    // 测试数据类型转换
    $sql = "SELECT id, is_active, recipient_user_id FROM Kunlun_sms_alert_config LIMIT 1";
    $result = $conn->query($sql);
    if ($result && $row = $result->fetch_assoc()) {
        $critical_fixes['data_type_conversion'] = [
            'original_types' => [
                'id' => gettype($row['id']),
                'is_active' => gettype($row['is_active']),
                'recipient_user_id' => gettype($row['recipient_user_id'])
            ],
            'converted_types' => [
                'id' => gettype(intval($row['id'])),
                'is_active' => gettype(intval($row['is_active'])),
                'recipient_user_id' => gettype(intval($row['recipient_user_id']))
            ],
            'note' => 'Data type conversion ensures proper JavaScript handling'
        ];
    }
    
    echo json_encode([
        'status' => 1,
        'message' => '统计功能测试完成',
        'data' => [
            'individual_tests' => $tests,
            'table_status' => $table_status,
            'critical_fixes' => $critical_fixes,
            'recommendations' => [
                'If Kunlun_sms_send_log table does not exist, SMS-related statistics will return 0',
                'If Kunlun_alarm_records table does not exist, alarm-related statistics will return empty arrays',
                'The main configuration statistics should work as long as Kunlun_sms_alert_config table exists',
                'All SQL queries now have proper error handling to prevent fatal errors'
            ]
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 0,
        'message' => '测试失败: ' . $e->getMessage(),
        'error' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
}
?>
