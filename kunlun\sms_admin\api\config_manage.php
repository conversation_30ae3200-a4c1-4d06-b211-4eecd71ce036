<?php
/**
 * 短信预警配置管理API
 * 创建时间: 2025-08-12
 * 功能: 提供短信配置的增删改查接口
 */

require_once '../../../conn_waf.php';

$APP_ID = 36; // 昆仑人脸预警系统应用ID

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 检查用户权限
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['status' => 0, 'message' => '未登录或登录信息已过期']);
    exit;
}

// 检查应用权限
function isHasPerm($APP_ID) {
    global $conn;
    
    if (!isset($_SESSION['user_id'])) {
        return false;
    }
    
    // 系统管理员拥有所有权限
    if (isAdmin()) {
        return true;
    }
    
    // 检查应用管理员权限
    if (isAppAdmin($APP_ID)) {
        return true;
    }
    
    return false;
}

if (!isHasPerm($APP_ID)) {
    echo json_encode(['status' => 0, 'message' => '权限不足']);
    exit;
}

$action = $_POST['action'] ?? $_GET['action'] ?? '';
$user_id = $_SESSION['user_id'];

switch ($action) {
    case 'list':
        getConfigList();
        break;
    case 'get':
        getConfig();
        break;
    case 'add':
        addConfig();
        break;
    case 'update':
        updateConfig();
        break;
    case 'delete':
        deleteConfig();
        break;
    case 'getUsers':
        getUsers();
        break;
    default:
        echo json_encode(['status' => 0, 'message' => '无效的操作']);
}

/**
 * 获取配置列表
 */
function getConfigList() {
    global $conn;
    
    $page = intval($_POST['page'] ?? 1);
    $pageSize = intval($_POST['pageSize'] ?? 20);
    $offset = ($page - 1) * $pageSize;
    
    // 搜索条件
    $searchSfz = $_POST['searchSfz'] ?? '';
    $searchRecipient = $_POST['searchRecipient'] ?? '';

    $whereConditions = [];
    $params = [];
    $types = '';

    if (!empty($searchSfz)) {
        $whereConditions[] = "sac.alarm_person_sfz LIKE ?";
        $params[] = "%$searchSfz%";
        $types .= 's';
    }

    if (!empty($searchRecipient)) {
        $whereConditions[] = "u.name LIKE ?";
        $params[] = "%$searchRecipient%";
        $types .= 's';
    }
    
    $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);
    
    // 查询总数
    $countSql = "SELECT COUNT(*) as total 
                FROM Kunlun_sms_alert_config sac
                JOIN 3_user u ON sac.recipient_user_id = u.id
                LEFT JOIN 2_unit unit ON u.organization_unit = unit.id
                $whereClause";
    
    if (!empty($params)) {
        $countStmt = $conn->prepare($countSql);
        $countStmt->bind_param($types, ...$params);
        $countStmt->execute();
        $countResult = $countStmt->get_result();
    } else {
        $countResult = $conn->query($countSql);
    }
    
    $total = $countResult->fetch_assoc()['total'];
    
    // 首先检查表结构是否包含新字段
    $checkSql = "SHOW COLUMNS FROM Kunlun_sms_alert_config LIKE 'alarm_type'";
    $checkResult = $conn->query($checkSql);
    $hasNewFields = $checkResult->num_rows > 0;

    // 根据表结构选择相应的查询语句
    if ($hasNewFields) {
        $listSql = "SELECT
                        sac.id,
                        sac.alarm_type,
                        sac.alarm_person_sfz,
                        sac.alarm_person_name,
                        sac.album_name,
                        sac.recipient_user_id,
                        sac.is_active,
                        sac.valid_start_time,
                        sac.valid_end_time,
                        sac.created_time,
                        u.name as recipient_name,
                        u.phone as recipient_phone,
                        unit.unit_name as recipient_unit_name
                    FROM Kunlun_sms_alert_config sac
                    JOIN 3_user u ON sac.recipient_user_id = u.id
                    LEFT JOIN 2_unit unit ON u.organization_unit = unit.id
                    $whereClause
                    ORDER BY sac.created_time DESC
                    LIMIT $offset, $pageSize";
    } else {
        // 兼容旧表结构
        $listSql = "SELECT
                        sac.id,
                        'sfz' as alarm_type,
                        sac.alarm_person_sfz,
                        sac.alarm_person_name,
                        NULL as album_name,
                        sac.recipient_user_id,
                        sac.is_active,
                        sac.valid_start_time,
                        sac.valid_end_time,
                        sac.created_time,
                        u.name as recipient_name,
                        u.phone as recipient_phone,
                        unit.unit_name as recipient_unit_name
                    FROM Kunlun_sms_alert_config sac
                    JOIN 3_user u ON sac.recipient_user_id = u.id
                    LEFT JOIN 2_unit unit ON u.organization_unit = unit.id
                    $whereClause
                    ORDER BY sac.created_time DESC
                    LIMIT $offset, $pageSize";
    }
    
    if (!empty($params)) {
        $listStmt = $conn->prepare($listSql);
        $listStmt->bind_param($types, ...$params);
        $listStmt->execute();
        $listResult = $listStmt->get_result();
    } else {
        $listResult = $conn->query($listSql);
    }
    
    $list = [];
    while ($row = $listResult->fetch_assoc()) {
        $list[] = $row;
    }
    
    echo json_encode([
        'status' => 1,
        'message' => '获取成功',
        'data' => [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'pageSize' => $pageSize,
            'totalPages' => ceil($total / $pageSize)
        ]
    ]);
}

/**
 * 获取单个配置
 */
function getConfig() {
    global $conn;
    
    $id = intval($_POST['id'] ?? 0);
    
    if ($id <= 0) {
        echo json_encode(['status' => 0, 'message' => '无效的配置ID']);
        return;
    }
    
    $sql = "SELECT * FROM Kunlun_sms_alert_config WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['status' => 0, 'message' => '配置不存在']);
        return;
    }
    
    $config = $result->fetch_assoc();
    
    echo json_encode([
        'status' => 1,
        'message' => '获取成功',
        'data' => $config
    ]);
}

/**
 * 添加配置
 */
function addConfig() {
    global $conn, $user_id;

    // 检查表结构是否支持新功能
    $checkSql = "SHOW COLUMNS FROM Kunlun_sms_alert_config LIKE 'alarm_type'";
    $checkResult = $conn->query($checkSql);
    $hasNewFields = $checkResult->num_rows > 0;

    $alarmType = $_POST['alarm_type'] ?? 'sfz';
    $alarmPersonSfz = trim($_POST['alarm_person_sfz'] ?? '');
    $alarmPersonName = trim($_POST['alarm_person_name'] ?? '');
    $albumName = trim($_POST['album_name'] ?? '');
    $recipientsJson = $_POST['recipients'] ?? '';
    $validStartTime = $_POST['valid_start_time'] ?? null;
    $validEndTime = $_POST['valid_end_time'] ?? null;
    $isActive = intval($_POST['is_active'] ?? 1);

    // 如果表结构不支持新功能，强制使用旧模式
    if (!$hasNewFields) {
        $alarmType = 'sfz';
        if (empty($alarmPersonSfz)) {
            echo json_encode(['status' => 0, 'message' => '请填写预警人员身份证号']);
            return;
        }
    }

    // 验证必填字段
    if (empty($recipientsJson)) {
        echo json_encode(['status' => 0, 'message' => '请选择接收人员']);
        return;
    }

    if ($alarmType === 'sfz' && empty($alarmPersonSfz)) {
        echo json_encode(['status' => 0, 'message' => '请填写预警人员身份证号']);
        return;
    }

    if ($alarmType === 'album' && empty($albumName)) {
        echo json_encode(['status' => 0, 'message' => '请填写图库名称']);
        return;
    }

    // 解析接收人员列表
    $recipientIds = json_decode($recipientsJson, true);
    if (!is_array($recipientIds) || empty($recipientIds)) {
        echo json_encode(['status' => 0, 'message' => '请至少选择一个接收人员']);
        return;
    }
    
    // 验证身份证号格式（仅在单身份证预警时验证）
    if ($alarmType === 'sfz' && !preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $alarmPersonSfz)) {
        echo json_encode(['status' => 0, 'message' => '身份证号格式不正确']);
        return;
    }

    // 验证接收人员是否存在
    $placeholders = str_repeat('?,', count($recipientIds) - 1) . '?';
    $userCheckSql = "SELECT id FROM 3_user WHERE id IN ($placeholders)";
    $userCheckStmt = $conn->prepare($userCheckSql);
    $userCheckStmt->bind_param(str_repeat('i', count($recipientIds)), ...$recipientIds);
    $userCheckStmt->execute();
    $validUsers = $userCheckStmt->get_result()->num_rows;

    if ($validUsers !== count($recipientIds)) {
        echo json_encode(['status' => 0, 'message' => '部分接收人员不存在']);
        return;
    }
    
    // 处理时间格式
    $validStartTime = empty($validStartTime) ? null : date('Y-m-d H:i:s', strtotime($validStartTime));
    $validEndTime = empty($validEndTime) ? null : date('Y-m-d H:i:s', strtotime($validEndTime));

    try {
        $conn->autocommit(false);

        $insertedIds = [];

        // 为每个接收人员创建配置
        if ($hasNewFields) {
            $sql = "INSERT INTO Kunlun_sms_alert_config (
                        alarm_type, alarm_person_sfz, alarm_person_name, album_name,
                        recipient_user_id, is_active, valid_start_time, valid_end_time,
                        created_by, created_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        } else {
            // 兼容旧表结构
            $sql = "INSERT INTO Kunlun_sms_alert_config (
                        alarm_person_sfz, alarm_person_name,
                        recipient_user_id, is_active, valid_start_time, valid_end_time,
                        created_by, created_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
        }

        $stmt = $conn->prepare($sql);

        foreach ($recipientIds as $recipientId) {
            // 检查是否已存在相同配置
            if ($alarmType === 'sfz') {
                $checkSql = "SELECT id FROM Kunlun_sms_alert_config
                            WHERE alarm_type = 'sfz' AND alarm_person_sfz = ? AND recipient_user_id = ?";
                $checkStmt = $conn->prepare($checkSql);
                $checkStmt->bind_param("si", $alarmPersonSfz, $recipientId);
            } else {
                $checkSql = "SELECT id FROM Kunlun_sms_alert_config
                            WHERE alarm_type = 'album' AND album_name = ? AND recipient_user_id = ?";
                $checkStmt = $conn->prepare($checkSql);
                $checkStmt->bind_param("si", $albumName, $recipientId);
            }

            $checkStmt->execute();
            if ($checkStmt->get_result()->num_rows > 0) {
                continue; // 跳过已存在的配置
            }

            if ($hasNewFields) {
                $stmt->bind_param("ssssisssi",
                    $alarmType,
                    $alarmType === 'sfz' ? $alarmPersonSfz : null,
                    $alarmPersonName,
                    $alarmType === 'album' ? $albumName : null,
                    $recipientId,
                    $isActive, $validStartTime, $validEndTime, $user_id
                );
            } else {
                // 兼容旧表结构
                $stmt->bind_param("sisssi",
                    $alarmPersonSfz,
                    $alarmPersonName,
                    $recipientId,
                    $isActive, $validStartTime, $validEndTime, $user_id
                );
            }

            if ($stmt->execute()) {
                $insertedIds[] = $stmt->insert_id;
            }
        }

        $conn->commit();

        if (!empty($insertedIds)) {
            // 记录操作日志
            global $APP_ID;
            $count = count($insertedIds);
            $target = $alarmType === 'sfz' ? "预警人员: $alarmPersonSfz" : "图库: $albumName";
            logOperation($conn, $user_id, $APP_ID, "新增短信预警配置，类型: $alarmType, $target, 接收人员数: $count");

            echo json_encode([
                'status' => 1,
                'message' => "配置添加成功，共创建 $count 条配置",
                'data' => ['ids' => $insertedIds]
            ]);
        } else {
            echo json_encode(['status' => 0, 'message' => '所有配置都已存在，未创建新配置']);
        }

    } catch (Exception $e) {
        $conn->rollback();
        echo json_encode(['status' => 0, 'message' => '配置添加失败: ' . $e->getMessage()]);
    } finally {
        $conn->autocommit(true);
    }
}

/**
 * 更新配置
 */
function updateConfig() {
    global $conn, $user_id;
    
    $id = intval($_POST['id'] ?? 0);
    $alarmPersonSfz = trim($_POST['alarm_person_sfz'] ?? '');
    $alarmPersonName = trim($_POST['alarm_person_name'] ?? '');
    $recipientUserId = intval($_POST['recipient_user_id'] ?? 0);
    $alertLevel = $_POST['alert_level'] ?? '';
    $validStartTime = $_POST['valid_start_time'] ?? null;
    $validEndTime = $_POST['valid_end_time'] ?? null;
    $isActive = intval($_POST['is_active'] ?? 1);
    
    if ($id <= 0) {
        echo json_encode(['status' => 0, 'message' => '无效的配置ID']);
        return;
    }
    
    // 验证必填字段
    if (empty($alarmPersonSfz) || $recipientUserId <= 0 || empty($alertLevel)) {
        echo json_encode(['status' => 0, 'message' => '请填写必填字段']);
        return;
    }
    
    // 验证身份证号格式
    if (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $alarmPersonSfz)) {
        echo json_encode(['status' => 0, 'message' => '身份证号格式不正确']);
        return;
    }
    
    // 检查配置是否存在
    $checkSql = "SELECT id FROM Kunlun_sms_alert_config WHERE id = ?";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bind_param("i", $id);
    $checkStmt->execute();
    if ($checkStmt->get_result()->num_rows === 0) {
        echo json_encode(['status' => 0, 'message' => '配置不存在']);
        return;
    }
    
    // 处理时间格式
    $validStartTime = empty($validStartTime) ? null : date('Y-m-d H:i:s', strtotime($validStartTime));
    $validEndTime = empty($validEndTime) ? null : date('Y-m-d H:i:s', strtotime($validEndTime));
    
    // 更新配置
    $sql = "UPDATE Kunlun_sms_alert_config SET 
                alarm_person_sfz = ?, alarm_person_name = ?, recipient_user_id = ?,
                alert_level = ?, is_active = ?, valid_start_time = ?, valid_end_time = ?,
                updated_by = ?, updated_time = NOW()
            WHERE id = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssissssii", 
        $alarmPersonSfz, $alarmPersonName, $recipientUserId,
        $alertLevel, $isActive, $validStartTime, $validEndTime, $user_id, $id
    );
    
    if ($stmt->execute()) {
        // 记录操作日志
        global $APP_ID;
        logOperation($conn, $user_id, $APP_ID, "更新短信预警配置，ID: $id, 预警人员: $alarmPersonSfz");
        
        echo json_encode(['status' => 1, 'message' => '配置更新成功']);
    } else {
        echo json_encode(['status' => 0, 'message' => '配置更新失败: ' . $stmt->error]);
    }
}

/**
 * 删除配置
 */
function deleteConfig() {
    global $conn, $user_id;
    
    $id = intval($_POST['id'] ?? 0);
    
    if ($id <= 0) {
        echo json_encode(['status' => 0, 'message' => '无效的配置ID']);
        return;
    }
    
    // 检查配置是否存在
    $checkSql = "SELECT alarm_person_sfz FROM Kunlun_sms_alert_config WHERE id = ?";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bind_param("i", $id);
    $checkStmt->execute();
    $result = $checkStmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['status' => 0, 'message' => '配置不存在']);
        return;
    }
    
    $config = $result->fetch_assoc();
    
    // 删除配置
    $sql = "DELETE FROM Kunlun_sms_alert_config WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        // 记录操作日志
        global $APP_ID;
        logOperation($conn, $user_id, $APP_ID, "删除短信预警配置，ID: $id, 预警人员: " . $config['alarm_person_sfz']);
        
        echo json_encode(['status' => 1, 'message' => '配置删除成功']);
    } else {
        echo json_encode(['status' => 0, 'message' => '配置删除失败: ' . $stmt->error]);
    }
}

/**
 * 获取用户列表
 */
function getUsers() {
    global $conn;
    
    $sql = "SELECT 
                u.id, u.name, u.phone, u.organization_unit,
                unit.unit_name
            FROM 3_user u
            LEFT JOIN 2_unit unit ON u.organization_unit = unit.id
            WHERE u.phone IS NOT NULL AND u.phone != ''
            ORDER BY unit.unit_name, u.name";
    
    $result = $conn->query($sql);
    $users = [];
    
    while ($row = $result->fetch_assoc()) {
        $users[] = $row;
    }
    
    echo json_encode([
        'status' => 1,
        'message' => '获取成功',
        'data' => $users
    ]);
}
?>
