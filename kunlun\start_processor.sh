#!/bin/bash
# 启动昆仑预警数据处理程序
# 创建时间: 2025-08-12
# 适用系统: Linux

echo "正在启动昆仑预警数据处理程序..."

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 设置文件路径
PHP_SCRIPT="$SCRIPT_DIR/background_processor.php"
LOG_DIR="$SCRIPT_DIR/logs"
LOG_FILE="$LOG_DIR/background_processor.log"
PID_FILE="$LOG_DIR/processor.pid"

# 创建日志目录
if [ ! -d "$LOG_DIR" ]; then
    mkdir -p "$LOG_DIR"
    echo "创建日志目录: $LOG_DIR"
fi

# 检查PHP脚本是否存在
if [ ! -f "$PHP_SCRIPT" ]; then
    echo "错误: 找不到处理程序文件 $PHP_SCRIPT"
    exit 1
fi

# 检查是否已经在运行
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p "$PID" > /dev/null 2>&1; then
        echo "后台处理程序已在运行 (PID: $PID)"
        echo "如果需要重启，请先运行: kill $PID"
        exit 1
    else
        echo "删除无效的PID文件"
        rm -f "$PID_FILE"
    fi
fi

# 检查PHP是否可用
if ! command -v php > /dev/null 2>&1; then
    echo "错误: 未找到PHP命令"
    exit 1
fi

# 测试脚本是否能正常执行
echo "测试脚本执行..."
TEST_OUTPUT=$(php "$PHP_SCRIPT" --test 2>&1)

# 检查是否有致命错误（忽略警告）
if [[ "$TEST_OUTPUT" =~ "Fatal error" ]] || [[ "$TEST_OUTPUT" =~ "Parse error" ]]; then
    echo "错误: 脚本有致命错误"
    echo "测试输出: $TEST_OUTPUT"
    exit 1
elif [[ "$TEST_OUTPUT" =~ "test mode started" ]]; then
    echo "脚本测试通过（忽略警告）"
    if [[ "$TEST_OUTPUT" =~ "Warning" ]]; then
        echo "注意: 检测到警告信息，但不影响运行"
    fi
else
    echo "警告: 脚本测试可能有问题"
    echo "测试输出: $TEST_OUTPUT"
    echo "尝试继续启动..."
fi

echo "脚本测试通过"

# 启动后台处理程序
echo "启动后台处理程序..."
echo "日志文件: $LOG_FILE"
echo "PID文件: $PID_FILE"

# 使用nohup启动后台进程
nohup php "$PHP_SCRIPT" > "$LOG_FILE" 2>&1 & 
PID=$!

# 保存PID
echo "$PID" > "$PID_FILE"

# 等待一下让程序启动
sleep 3

# 检查进程是否在运行
if ps -p "$PID" > /dev/null 2>&1; then
    echo "后台处理程序启动成功 (PID: $PID)"
    
    # 检查日志文件
    if [ -f "$LOG_FILE" ] && [ -s "$LOG_FILE" ]; then
        echo "日志文件已创建，显示最后几行:"
        tail -5 "$LOG_FILE"
    else
        echo "警告: 日志文件未创建或为空"
    fi
    
    echo ""
    echo "使用以下命令监控日志:"
    echo "tail -f $LOG_FILE"
    echo ""
    echo "使用以下命令停止程序:"
    echo "kill $PID"
    
else
    echo "错误: 后台处理程序启动失败"
    rm -f "$PID_FILE"
    
    if [ -f "$LOG_FILE" ]; then
        echo "日志内容:"
        cat "$LOG_FILE"
    fi
    
    exit 1
fi

echo "启动完成！"
