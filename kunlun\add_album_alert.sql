-- 添加图库预警功能的数据库更新脚本
-- 更新时间: 2025-08-12
-- 说明: 为Kunlun_sms_alert_config表添加预警类型和图库名称字段

-- 检查表结构
DESCRIBE `Kunlun_sms_alert_config`;

-- 添加预警类型字段
ALTER TABLE `Kunlun_sms_alert_config` 
ADD COLUMN `alarm_type` enum('sfz','album') NOT NULL DEFAULT 'sfz' 
COMMENT '预警类型（sfz=单身份证预警，album=图库预警）' 
AFTER `id`;

-- 添加图库名称字段
ALTER TABLE `Kunlun_sms_alert_config` 
ADD COLUMN `album_name` varchar(100) DEFAULT NULL 
COMMENT '图库名称（图库预警时必填）' 
AFTER `alarm_person_name`;

-- 修改身份证号字段为可空（图库预警时不需要）
ALTER TABLE `Kunlun_sms_alert_config` 
MODIFY COLUMN `alarm_person_sfz` varchar(18) DEFAULT NULL 
COMMENT '预警人员身份证号（单身份证预警时必填）';

-- 添加索引
CREATE INDEX `idx_alarm_type` ON `Kunlun_sms_alert_config` (`alarm_type`);
CREATE INDEX `idx_album_name` ON `Kunlun_sms_alert_config` (`album_name`);

-- 更新表注释
ALTER TABLE `Kunlun_sms_alert_config` 
COMMENT = '短信预警配置表（支持单身份证和图库预警）';

-- 添加约束检查
-- 单身份证预警时必须有身份证号
-- 图库预警时必须有图库名称
ALTER TABLE `Kunlun_sms_alert_config` 
ADD CONSTRAINT `chk_alarm_config` CHECK (
    (alarm_type = 'sfz' AND alarm_person_sfz IS NOT NULL) OR
    (alarm_type = 'album' AND album_name IS NOT NULL)
);

-- 验证表结构
DESCRIBE `Kunlun_sms_alert_config`;

-- 显示表的存储引擎和字符集信息
SHOW TABLE STATUS LIKE 'Kunlun_sms_alert_config';

-- 检查相关索引
SHOW INDEX FROM `Kunlun_sms_alert_config`;

-- 查询现有数据（应该都是sfz类型）
SELECT alarm_type, COUNT(*) as count 
FROM `Kunlun_sms_alert_config` 
GROUP BY alarm_type;
