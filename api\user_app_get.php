<?php
header('Content-Type: application/json');
require_once '../conn_waf.php';

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'status' => 0,
        'message' => '仅支持POST请求',
        'data' => []
    ]);
    exit;
}

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'status' => 0,
        'message' => '用户未登录',
        'data' => []
    ]);
    exit;
}

// 获取当前用户ID
$userId = $_SESSION['user_id'];

/**
 * 根据角色ID数组获取角色名称数组
 * @param array $roleIds 角色ID数组
 * @param mysqli $conn 数据库连接
 * @return array 角色名称数组
 */
function getRoleNames($roleIds, $conn) {
    if (empty($roleIds) || !is_array($roleIds)) {
        return [];
    }

    $roleNames = [];
    foreach ($roleIds as $roleId) {
        $roleSql = "SELECT roleName FROM 4_Role_List WHERE id = ?";
        $roleStmt = $conn->prepare($roleSql);

        if ($roleStmt !== false) {
            $roleStmt->bind_param('i', $roleId);
            $roleStmt->execute();
            $roleResult = $roleStmt->get_result();

            if ($roleResult->num_rows > 0) {
                $roleRow = $roleResult->fetch_assoc();
                $roleNames[] = $roleRow['roleName'];
            }
            $roleStmt->close();
        }
    }

    return $roleNames;
}

try {
    // 查询用户角色信息
    $userRoleSql = "SELECT roleId, unitId, appId FROM 3_user_Role WHERE userId = ?";
    $userRoleStmt = $conn->prepare($userRoleSql);

    if ($userRoleStmt === false) {
        return json_encode([
            'status' => 0, 
            'message' => '用户角色查询准备失败: ' . $conn->error,
            'data' => []
        ]);
    }

    $userRoleStmt->bind_param('i', $userId);
    $userRoleStmt->execute();
    $userRoleResult = $userRoleStmt->get_result();

    if ($userRoleResult->num_rows === 0) {
        echo json_encode([
            'status' => 0,
            'message' => '用户角色信息不存在',
            'data' => []
        ]);
        exit;
    }

    // 获取用户所有角色信息
    $userRoles = [];
    $hasAdminRole = false;
    $hasAppAdminRole = false;
    $appIds = [];
    
    while ($row = $userRoleResult->fetch_assoc()) {
        $userRoles[] = $row;
        if ($row['roleId'] == 1) {
            $hasAdminRole = true;
        }
        if ($row['roleId'] == 2) {
            $hasAppAdminRole = true;
            $appIds[] = $row['appId'];
        }
    }
    
    // 如果没有任何角色，返回错误
    if (empty($userRoles)) {
        echo json_encode([
            'status' => 0,
            'message' => '用户角色信息不存在',
            'data' => []
        ]);
        exit;
    }

    // 根据角色决定查询逻辑
    $applicationData = [];

    if ($hasAdminRole) {
        // 系统管理员：返回所有应用记录
        $appSql = "SELECT id, application_name, roleList FROM 5_application";
        $appStmt = $conn->prepare($appSql);

        if ($appStmt === false) {
            return json_encode([
                'status' => 0, 
                'message' => '应用查询准备失败: ' . $conn->error,
                'data' => []
            ]);
        }

        $appStmt->execute();
        $appResult = $appStmt->get_result();

        while ($row = $appResult->fetch_assoc()) {
            // 解析roleList JSON字段
            $roleListJson = $row['roleList'];
            $roleListIds = [];

            if (!empty($roleListJson)) {
                $decoded = json_decode($roleListJson, true);
                if (is_array($decoded)) {
                    $roleListIds = $decoded;
                }
            }

            // 获取角色名称
            $roleListNames = getRoleNames($roleListIds, $conn);

            $applicationData[] = [
                'id' => $row['id'],
                'application_name' => $row['application_name'],
                'rolelist_id' => $roleListIds,
                'rolelist_name' => $roleListNames
            ];
        }

    } elseif ($hasAppAdminRole) {
        // 应用管理员：返回匹配appIds的应用记录
        $placeholders = implode(',', array_fill(0, count($appIds), '?'));
        $appSql = "SELECT id, application_name, roleList FROM 5_application WHERE id IN ($placeholders)";
        $appStmt = $conn->prepare($appSql);

        if ($appStmt === false) {
            return json_encode([
                'status' => 0, 
                'message' => '应用查询准备失败: ' . $conn->error,
                'data' => []
            ]);
        }

        // 绑定参数
        $types = str_repeat('i', count($appIds));
        $appStmt->bind_param($types, ...$appIds);
        $appStmt->execute();
        $appResult = $appStmt->get_result();

        while ($row = $appResult->fetch_assoc()) {
            // 解析roleList JSON字段
            $roleListJson = $row['roleList'];
            $roleListIds = [];

            if (!empty($roleListJson)) {
                $decoded = json_decode($roleListJson, true);
                if (is_array($decoded)) {
                    $roleListIds = $decoded;
                }
            }

            // 获取角色名称
            $roleListNames = getRoleNames($roleListIds, $conn);

            $applicationData[] = [
                'id' => $row['id'],
                'application_name' => $row['application_name'],
                'rolelist_id' => $roleListIds,
                'rolelist_name' => $roleListNames
            ];
        }

    } else {
        // 其他角色：根据业务需求可以扩展
        echo json_encode([
            'status' => 0,
            'message' => '当前用户无权限访问应用信息',
            'data' => []
        ]);
        exit;
    }

    // 返回成功结果
    echo json_encode([
        'status' => 1,
        'message' => '查询成功',
        'data' => $applicationData
    ]);

} catch (Exception $e) {
    // 错误处理
    echo json_encode([
        'status' => 0,
        'message' => '查询失败: ' . $e->getMessage(),
        'data' => []
    ]);
} finally {
    // 关闭数据库连接
    if (isset($userRoleStmt)) {
        $userRoleStmt->close();
    }
    if (isset($appStmt)) {
        $appStmt->close();
    }
    if (isset($conn)) {
        $conn->close();
    }
}
?>