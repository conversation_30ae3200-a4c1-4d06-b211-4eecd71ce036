<?php
/**
 * 数据库连接（使用PDO，不依赖mysqli扩展）
 * 创建时间: 2025-08-12
 */

// 数据库配置
$servername = "localhost";
$username = "root";
$password = "YC@yc110";
$dbname = "application";

try {
    // 使用PDO连接数据库
    $dsn = "mysql:host=$servername;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);
    
    // 为了兼容现有代码，创建一个mysqli兼容的包装类
    class PDOMysqliWrapper {
        private $pdo;
        public $connect_error = null;
        
        public function __construct($pdo) {
            $this->pdo = $pdo;
        }
        
        public function query($sql) {
            try {
                $stmt = $this->pdo->query($sql);
                return new PDOResultWrapper($stmt);
            } catch (PDOException $e) {
                error_log("Database query error: " . $e->getMessage());
                return false;
            }
        }
        
        public function prepare($sql) {
            try {
                $stmt = $this->pdo->prepare($sql);
                return new PDOStatementWrapper($stmt);
            } catch (PDOException $e) {
                error_log("Database prepare error: " . $e->getMessage());
                return false;
            }
        }
        
        public function set_charset($charset) {
            // PDO已在连接时设置字符集
            return true;
        }
        
        public function real_escape_string($string) {
            // PDO使用参数绑定，不需要手动转义
            return addslashes($string);
        }
        
        public function close() {
            $this->pdo = null;
            return true;
        }
        
        public function __get($name) {
            if ($name === 'insert_id') {
                return $this->pdo->lastInsertId();
            }
            if ($name === 'affected_rows') {
                return $this->pdo->rowCount();
            }
            if ($name === 'error') {
                $errorInfo = $this->pdo->errorInfo();
                return $errorInfo[2] ?? '';
            }
            return null;
        }
    }
    
    class PDOResultWrapper {
        private $stmt;
        public $num_rows;
        
        public function __construct($stmt) {
            $this->stmt = $stmt;
            $this->num_rows = $stmt->rowCount();
        }
        
        public function fetch_assoc() {
            return $this->stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        public function fetch_all($mode = MYSQLI_ASSOC) {
            return $this->stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        public function free() {
            $this->stmt = null;
        }
    }
    
    class PDOStatementWrapper {
        private $stmt;
        private $params = [];
        
        public function __construct($stmt) {
            $this->stmt = $stmt;
        }
        
        public function bind_param($types, ...$params) {
            $this->params = $params;
            return true;
        }
        
        public function execute() {
            try {
                return $this->stmt->execute($this->params);
            } catch (PDOException $e) {
                error_log("Database execute error: " . $e->getMessage());
                return false;
            }
        }
        
        public function get_result() {
            return new PDOResultWrapper($this->stmt);
        }
        
        public function close() {
            $this->stmt = null;
        }
        
        public function __get($name) {
            if ($name === 'insert_id') {
                return $this->stmt->lastInsertId();
            }
            if ($name === 'affected_rows') {
                return $this->stmt->rowCount();
            }
            return null;
        }
    }
    
    // 创建兼容的连接对象
    $conn = new PDOMysqliWrapper($pdo);
    
} catch (PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}

// 兼容函数
if (!function_exists('mysqli_connect')) {
    function mysqli_connect($host, $username, $password, $database) {
        global $conn;
        return $conn;
    }
}

if (!function_exists('mysqli_query')) {
    function mysqli_query($conn, $sql) {
        return $conn->query($sql);
    }
}

if (!function_exists('mysqli_prepare')) {
    function mysqli_prepare($conn, $sql) {
        return $conn->prepare($sql);
    }
}

if (!function_exists('mysqli_real_escape_string')) {
    function mysqli_real_escape_string($conn, $string) {
        return $conn->real_escape_string($string);
    }
}

if (!function_exists('mysqli_close')) {
    function mysqli_close($conn) {
        return $conn->close();
    }
}

// 定义常量
if (!defined('MYSQLI_ASSOC')) {
    define('MYSQLI_ASSOC', PDO::FETCH_ASSOC);
}
?>
