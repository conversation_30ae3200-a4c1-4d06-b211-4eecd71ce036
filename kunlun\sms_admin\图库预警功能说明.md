# 昆仑人脸预警系统 - 图库预警功能说明

## 功能概述

在原有单身份证预警功能的基础上，新增了图库预警功能。现在系统支持两种预警方式：

1. **单身份证预警**：针对特定身份证号的人员进行预警
2. **图库预警**：针对整个图库内的所有人员进行预警

## 功能特点

### 预警方式选择
- 新增预警配置时，可以选择预警类型：
  - **单身份证预警**：精确匹配特定人员的身份证号
  - **图库预警**：匹配指定图库内的任何人员

### 智能字段切换
- 根据选择的预警类型，界面会自动显示相应的输入字段
- 单身份证预警：显示身份证号输入框
- 图库预警：显示图库名称输入框

### 数据验证
- 单身份证预警时，身份证号为必填项并进行格式验证
- 图库预警时，图库名称为必填项
- 两种方式互斥，确保数据一致性

## 数据库结构变更

### 表结构更新
```sql
-- 新增字段
ALTER TABLE `Kunlun_sms_alert_config` 
ADD COLUMN `alarm_type` enum('sfz','album') NOT NULL DEFAULT 'sfz' 
COMMENT '预警类型（sfz=单身份证预警，album=图库预警）';

ALTER TABLE `Kunlun_sms_alert_config` 
ADD COLUMN `album_name` varchar(100) DEFAULT NULL 
COMMENT '图库名称（图库预警时必填）';

-- 修改现有字段
ALTER TABLE `Kunlun_sms_alert_config` 
MODIFY COLUMN `alarm_person_sfz` varchar(18) DEFAULT NULL 
COMMENT '预警人员身份证号（单身份证预警时必填）';
```

### 索引优化
```sql
-- 新增索引
CREATE INDEX `idx_alarm_type` ON `Kunlun_sms_alert_config` (`alarm_type`);
CREATE INDEX `idx_album_name` ON `Kunlun_sms_alert_config` (`album_name`);
```

### 约束检查
```sql
-- 添加约束确保数据一致性
ALTER TABLE `Kunlun_sms_alert_config` 
ADD CONSTRAINT `chk_alarm_config` CHECK (
    (alarm_type = 'sfz' AND alarm_person_sfz IS NOT NULL) OR
    (alarm_type = 'album' AND album_name IS NOT NULL)
);
```

## 前端界面更新

### 新增配置界面
- 添加预警类型选择下拉框
- 根据选择的类型动态显示相应字段
- 实时验证必填字段

### 配置列表界面
- 新增"预警类型"列，显示预警方式
- "身份证号/图库名称"列根据类型显示相应内容
- 预警类型用不同颜色的标签区分

### JavaScript功能
```javascript
// 切换预警类型字段显示
function toggleAlarmFields() {
    const alarmType = document.getElementById('alarmType').value;
    if (alarmType === 'sfz') {
        // 显示身份证字段，隐藏图库字段
        document.getElementById('sfzFields').style.display = 'block';
        document.getElementById('albumFields').style.display = 'none';
    } else {
        // 显示图库字段，隐藏身份证字段
        document.getElementById('sfzFields').style.display = 'none';
        document.getElementById('albumFields').style.display = 'block';
    }
}
```

## 后端逻辑更新

### API接口增强
- 支持接收预警类型参数
- 根据预警类型进行不同的数据验证
- 批量创建配置时支持两种预警方式

### 预警匹配逻辑
```sql
-- 支持两种预警方式的匹配
WHERE (
    (sac.alarm_type = 'sfz' AND sac.alarm_person_sfz = ?) OR
    (sac.alarm_type = 'album' AND sac.album_name = ?)
)
```

### 短信内容优化
- 根据预警类型生成不同的短信内容
- 图库预警时在短信中显示图库信息
- 保持短信内容的清晰和准确

## 使用场景

### 单身份证预警
**适用场景**：
- 针对特定重点人员的精确监控
- 已知具体身份信息的目标人员
- 需要高精度匹配的场景

**配置方法**：
1. 选择"单身份证预警"
2. 输入目标人员的18位身份证号
3. 选择接收短信的人员
4. 设置有效期和启用状态

### 图库预警
**适用场景**：
- 对整个图库内人员的批量监控
- 黑名单、重点关注人员库的监控
- 不确定具体身份但属于某个群体的监控

**配置方法**：
1. 选择"图库预警"
2. 输入目标图库的名称
3. 选择接收短信的人员
4. 设置有效期和启用状态

## 数据流程

### 预警触发流程
1. **人脸识别系统**检测到人员
2. **预警记录**包含身份证号和图库名称信息
3. **短信配置匹配**：
   - 检查是否有匹配的单身份证预警配置
   - 检查是否有匹配的图库预警配置
4. **短信发送**：根据匹配的配置发送相应短信

### 配置管理流程
1. **选择预警类型**：单身份证或图库
2. **填写相应信息**：身份证号或图库名称
3. **选择接收人员**：支持多选
4. **设置有效期**：可选的时间范围限制
5. **保存配置**：系统验证并保存

## 兼容性保证

### 向后兼容
- 现有的单身份证预警配置自动标记为'sfz'类型
- 原有功能完全保持不变
- API接口保持向后兼容

### 数据迁移
- 现有配置数据无需手动迁移
- 新增字段有合理的默认值
- 约束检查确保数据完整性

## 部署指南

### 数据库更新
1. 备份现有数据库
2. 执行`add_album_alert.sql`脚本
3. 验证表结构和数据完整性

### 代码部署
1. 更新前端界面文件
2. 更新后端API接口
3. 更新后台处理程序
4. 重启相关服务

### 功能验证
1. 测试单身份证预警功能
2. 测试图库预警功能
3. 验证短信发送逻辑
4. 检查配置管理界面

## 监控建议

### 性能监控
- 监控图库预警的匹配性能
- 观察数据库查询效率
- 关注短信发送频率

### 功能监控
- 验证预警匹配准确性
- 检查短信内容正确性
- 监控配置创建和编辑功能

## 总结

图库预警功能的添加显著增强了系统的灵活性和实用性：

**主要优势**：
- ✅ 支持两种预警方式，满足不同监控需求
- ✅ 智能界面切换，用户体验友好
- ✅ 完善的数据验证和约束检查
- ✅ 向后兼容，不影响现有功能
- ✅ 灵活的配置管理，支持批量操作

**技术特点**：
- 🔧 数据库结构合理设计
- 🔧 前端界面动态交互
- 🔧 后端逻辑完善验证
- 🔧 短信内容智能生成
- 🔧 性能优化的查询逻辑

这一功能使得预警系统能够更好地适应各种监控场景，提高了系统的实用价值和用户满意度。
