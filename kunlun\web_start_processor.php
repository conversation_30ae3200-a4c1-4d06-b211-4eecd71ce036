<?php
/**
 * Web版本的后台处理程序启动器
 * 由于CLI环境缺少mysqli扩展，使用Web环境启动
 */

header('Content-Type: application/json; charset=utf-8');

// 引入数据库连接（Web环境应该有mysqli扩展）
require_once '../conn_waf.php';

try {
    $results = [];
    
    // 1. 检查Web环境mysqli扩展
    $results['environment_check'] = [
        'mysqli_extension' => extension_loaded('mysqli'),
        'mysqli_functions' => function_exists('mysqli_connect'),
        'database_connection' => isset($conn) && $conn !== false
    ];
    
    if (!$results['environment_check']['mysqli_extension']) {
        throw new Exception('Web环境也缺少mysqli扩展');
    }
    
    if (!$results['environment_check']['database_connection']) {
        throw new Exception('数据库连接失败');
    }
    
    // 2. 检查后台处理程序文件
    $processorPath = __DIR__ . '/background_processor.php';
    if (!file_exists($processorPath)) {
        throw new Exception('后台处理程序文件不存在: ' . $processorPath);
    }
    
    // 3. 创建日志目录
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        if (!mkdir($logDir, 0755, true)) {
            throw new Exception('无法创建日志目录: ' . $logDir);
        }
    }
    
    $results['log_directory'] = $logDir;
    
    // 4. 检查是否已经在运行
    $pidFile = $logDir . '/processor.pid';
    $logFile = $logDir . '/background_processor.log';
    
    if (file_exists($pidFile)) {
        $pid = trim(file_get_contents($pidFile));
        $results['already_running'] = [
            'pid_file_exists' => true,
            'pid' => $pid,
            'message' => '检测到PID文件，处理程序可能已在运行'
        ];
        
        // 检查进程是否真的在运行（Linux）
        if (function_exists('posix_kill')) {
            $running = posix_kill($pid, 0);
            $results['already_running']['process_running'] = $running;
            if (!$running) {
                // 进程不存在，删除PID文件
                unlink($pidFile);
                $results['already_running']['pid_file_removed'] = true;
            }
        }
    }
    
    // 5. 启动方式选择
    $action = $_GET['action'] ?? 'check';
    
    if ($action === 'start') {
        // 强制启动
        if (file_exists($pidFile)) {
            unlink($pidFile);
        }
        
        // 使用Web方式启动后台处理
        $results['start_attempt'] = [
            'method' => 'web_background',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // 方法1：使用ignore_user_abort和set_time_limit
        ignore_user_abort(true);
        set_time_limit(0);
        
        // 记录启动
        file_put_contents($pidFile, getmypid());
        file_put_contents($logFile, "[" . date('Y-m-d H:i:s') . "] Web启动后台处理程序\n", FILE_APPEND);
        
        // 启动后台处理逻辑
        $results['processor_started'] = true;
        
        // 可以在这里包含后台处理逻辑，或者使用其他方式
        // 由于这是Web请求，我们需要快速响应，所以使用异步方式
        
        // 方法2：使用shell_exec异步启动（如果可能）
        $command = "nohup php " . escapeshellarg($processorPath) . " > " . escapeshellarg($logFile) . " 2>&1 & echo $!";
        $pid = shell_exec($command);
        
        if ($pid) {
            file_put_contents($pidFile, trim($pid));
            $results['async_start'] = [
                'success' => true,
                'pid' => trim($pid),
                'command' => $command
            ];
        } else {
            $results['async_start'] = [
                'success' => false,
                'error' => 'shell_exec启动失败'
            ];
        }
        
    } elseif ($action === 'stop') {
        // 停止处理程序
        if (file_exists($pidFile)) {
            $pid = trim(file_get_contents($pidFile));
            
            // 尝试终止进程
            if (function_exists('posix_kill')) {
                $killed = posix_kill($pid, SIGTERM);
                $results['stop_attempt'] = [
                    'pid' => $pid,
                    'killed' => $killed
                ];
            } else {
                // 使用shell命令
                $killResult = shell_exec("kill $pid 2>&1");
                $results['stop_attempt'] = [
                    'pid' => $pid,
                    'kill_output' => $killResult
                ];
            }
            
            // 删除PID文件
            unlink($pidFile);
            $results['pid_file_removed'] = true;
            
            // 记录停止
            file_put_contents($logFile, "[" . date('Y-m-d H:i:s') . "] Web停止后台处理程序\n", FILE_APPEND);
        } else {
            $results['stop_attempt'] = [
                'error' => 'PID文件不存在，程序可能未运行'
            ];
        }
        
    } elseif ($action === 'status') {
        // 检查状态
        $results['status_check'] = [
            'pid_file_exists' => file_exists($pidFile),
            'log_file_exists' => file_exists($logFile)
        ];
        
        if (file_exists($pidFile)) {
            $pid = trim(file_get_contents($pidFile));
            $results['status_check']['pid'] = $pid;
            
            // 检查进程是否运行
            if (function_exists('posix_kill')) {
                $running = posix_kill($pid, 0);
                $results['status_check']['process_running'] = $running;
            }
        }
        
        if (file_exists($logFile)) {
            $logContent = file_get_contents($logFile);
            $results['status_check']['log_size'] = strlen($logContent);
            $results['status_check']['last_log_lines'] = array_slice(explode("\n", trim($logContent)), -10);
        }
        
    } else {
        // 默认检查
        $results['available_actions'] = [
            'start' => '启动后台处理程序',
            'stop' => '停止后台处理程序', 
            'status' => '检查运行状态'
        ];
        
        $results['usage'] = [
            'start' => '?action=start',
            'stop' => '?action=stop',
            'status' => '?action=status'
        ];
    }
    
    echo json_encode([
        'status' => 1,
        'message' => '操作完成',
        'action' => $action,
        'data' => $results
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 0,
        'message' => '操作失败: ' . $e->getMessage(),
        'error' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
