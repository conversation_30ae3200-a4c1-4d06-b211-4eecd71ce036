<?php
/**
 * 最终修复测试
 */

header('Content-Type: application/json; charset=utf-8');

try {
    $results = [];
    
    // 1. 检查可用的数据库扩展
    $results['extensions'] = [
        'mysqli' => function_exists('mysqli_connect'),
        'pdo' => class_exists('PDO'),
        'pdo_mysql' => extension_loaded('pdo_mysql'),
        'mysql' => function_exists('mysql_connect')
    ];
    
    // 2. 测试数据库连接
    $results['database_test'] = [
        'connection_attempt' => false,
        'connection_success' => false,
        'connection_type' => 'none',
        'error' => null
    ];
    
    try {
        $results['database_test']['connection_attempt'] = true;
        
        // 包含数据库连接文件
        include_once '../conn_waf.php';
        
        if (isset($conn) && $conn) {
            $results['database_test']['connection_success'] = true;
            
            // 检测连接类型
            $conn_class = get_class($conn);
            if ($conn_class === 'mysqli') {
                $results['database_test']['connection_type'] = 'mysqli';
            } elseif (strpos($conn_class, 'PDO') !== false) {
                $results['database_test']['connection_type'] = 'pdo';
            } elseif (strpos($conn_class, 'MySQL') !== false) {
                $results['database_test']['connection_type'] = 'mysql';
            } elseif (strpos($conn_class, 'Mock') !== false) {
                $results['database_test']['connection_type'] = 'mock';
            } else {
                $results['database_test']['connection_type'] = $conn_class;
            }
            
            // 测试简单查询
            try {
                $testQuery = $conn->query("SELECT 1 as test");
                if ($testQuery) {
                    $results['database_test']['query_test'] = true;
                    if (method_exists($testQuery, 'fetch_assoc')) {
                        $result = $testQuery->fetch_assoc();
                        $results['database_test']['query_result'] = $result;
                    }
                } else {
                    $results['database_test']['query_test'] = false;
                }
            } catch (Exception $e) {
                $results['database_test']['query_error'] = $e->getMessage();
            }
        } else {
            $results['database_test']['error'] = '数据库连接对象未创建';
        }
    } catch (Exception $e) {
        $results['database_test']['error'] = $e->getMessage();
    }
    
    // 3. 测试脚本执行
    $results['script_test'] = [
        'test_available' => false,
        'output' => null,
        'error' => null
    ];
    
    try {
        $scriptPath = __DIR__ . '/background_processor.php';
        if (file_exists($scriptPath)) {
            $testCommand = "php $scriptPath --test 2>&1";
            $testOutput = shell_exec($testCommand);
            
            $results['script_test']['command'] = $testCommand;
            $results['script_test']['output'] = $testOutput;
            $results['script_test']['test_available'] = strpos($testOutput, 'test mode started') !== false;
            $results['script_test']['has_fatal_error'] = strpos($testOutput, 'Fatal error') !== false;
            $results['script_test']['has_mysqli_error'] = strpos($testOutput, 'mysqli_connect') !== false;
        } else {
            $results['script_test']['error'] = 'background_processor.php文件不存在';
        }
    } catch (Exception $e) {
        $results['script_test']['error'] = $e->getMessage();
    }
    
    // 4. 总体状态
    $overall_status = [
        'database_connected' => $results['database_test']['connection_success'] ?? false,
        'script_working' => $results['script_test']['test_available'] ?? false,
        'no_mysqli_errors' => !($results['script_test']['has_mysqli_error'] ?? true),
        'no_fatal_errors' => !($results['script_test']['has_fatal_error'] ?? true)
    ];
    
    $overall_status['ready'] = $overall_status['database_connected'] && 
                               $overall_status['no_fatal_errors'] && 
                               $overall_status['no_mysqli_errors'];
    
    $results['overall_status'] = $overall_status;
    
    // 5. 建议
    $recommendations = [];
    
    if (!$overall_status['database_connected']) {
        $recommendations[] = '数据库连接失败，请检查数据库配置和服务状态';
    }
    
    if (!$overall_status['no_mysqli_errors']) {
        $recommendations[] = '仍有mysqli相关错误，请检查代码修复';
    }
    
    if (!$overall_status['no_fatal_errors']) {
        $recommendations[] = '存在致命错误，请查看详细输出';
    }
    
    if ($overall_status['ready']) {
        $recommendations[] = '✓ 所有检查通过，可以启动后台处理程序';
        $recommendations[] = '使用的数据库连接类型: ' . ($results['database_test']['connection_type'] ?? 'unknown');
    }
    
    $results['recommendations'] = $recommendations;
    
    echo json_encode([
        'status' => $overall_status['ready'] ? 1 : 0,
        'message' => $overall_status['ready'] ? 
            '修复成功，系统可以正常运行' : 
            '仍有问题需要解决',
        'data' => $results
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 0,
        'message' => '测试失败: ' . $e->getMessage(),
        'error' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
