<?php
ini_set("display_errors",1);
error_reporting(E_ALL);
// 检查是否为表单提交请求

// 检查是否为表单提交请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 获取表单数据
    $systemId = "ed47f8d43acec3ee48d7409146bef5c1";
    $extensionNo = "039";
    $smId = "1060232342343242";

    try {
        // 创建SoapClient实例
        $client = new SoapClient('http://dxpt.sc/services/IMsInterfaceNewService?wsdl');

        // 发送SOAP请求
        $response = $client->__call(
            'report', // 替换为实际的SOAP方法名
            [
                $systemId,
                $extensionNo,
                $smId
            ]
        );

        // 处理响应
        if ($response === false) {
            throw new Exception('SOAP 请求失败');
        }

        // 检查 $response 是否为对象
        if (is_object($response)) {
            // 将对象转换为字符串
            $response = (string)$response;
        }

        // 尝试解析XML
        $xml = simplexml_load_string($response);
        if ($xml === false) {
            echo '<div class="error">
                    <strong>解析响应失败！</strong><br>
                    错误信息：' . htmlspecialchars(simplexml_last_error_string()) . '<br>
                    响应内容：<pre>' . htmlspecialchars($response) . '</pre>
                </div>';
        } else {
            $result = [
                'returncode' => (string)$xml->sminfo_return->returncode,
                'error_code' => (string)$xml->sminfo_return->error_code,
                'message' => (string)$xml->sminfo_return->message,
                'reports' => []
            ];

            if (!empty($xml->sminfo_return->reports)) {
                foreach ($xml->sminfo_return->reports->report as $report) {
                    $result['reports'][] = [
                        'sm_id' => (string)$report->sm_id,
                        'tousermobile' => (string)$report->tousermobile,
                        'state' => (string)$report->state
                    ];
                }
            }

            if ($result['returncode'] === '1') {
                echo '<div class="success">
                        <strong>查询成功！</strong><br>
                        查询结果如下：<br><br>
                        <table border="1" cellpadding="5">
                            <tr>
                                <th>短信ID</th>
                                <th>接收手机号</th>
                                <th>状态</th>
                            </tr>';
                foreach ($result['reports'] as $report) {
                    echo '<tr>
                            <td>' . $report['sm_id'] . '</td>
                            <td>' . $report['tousermobile'] . '</td>
                            <td>' . $report['state'] . '</td>
                        </tr>';
                }
                echo '</table>
                    </div>';
            } else {
                echo '<div class="error">
                        <strong>查询失败！</strong><br>
                        错误码：' . $result['error_code'] . '<br>
                        提示信息：' . $result['message'] . '
                    </div>';
            }
        }
    } catch (Exception $e) {
        echo '<div class="error">
                <strong>发生错误！</strong><br>
                错误信息：' . $e->getMessage() . '<br>
                堆栈跟踪：' . $e->getTraceAsString() . '
            </div>';
    }
} else {
    echo '<div class="error">非法请求！</div>';
}
?>