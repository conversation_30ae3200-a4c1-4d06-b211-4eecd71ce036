<?php
/**
 * 统计数据API
 * 创建时间: 2025-08-12
 * 功能: 提供系统统计数据
 */

require_once '../../../conn_waf.php';

$APP_ID = 35; // 预警系统应用ID

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 检查用户权限
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['status' => 0, 'message' => '未登录或登录信息已过期']);
    exit;
}

// 检查应用权限
function isHasPerm($APP_ID) {
    global $conn;
    
    if (!isset($_SESSION['user_id'])) {
        return false;
    }
    
    // 系统管理员拥有所有权限
    if (isAdmin()) {
        return true;
    }
    
    // 检查应用管理员权限
    if (isAppAdmin($APP_ID)) {
        return true;
    }
    
    return false;
}

if (!isHasPerm($APP_ID)) {
    echo json_encode(['status' => 0, 'message' => '权限不足']);
    exit;
}

try {
    // 获取统计数据
    $stats = getSystemStats();
    
    echo json_encode([
        'status' => 1,
        'message' => '获取成功',
        'data' => $stats
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 0,
        'message' => '获取统计数据失败: ' . $e->getMessage()
    ]);
}

/**
 * 获取系统统计数据
 */
function getSystemStats() {
    global $conn;
    
    $stats = [];
    
    // 1. 总配置数
    $sql = "SELECT COUNT(*) as total FROM Kunlun_sms_alert_config";
    $result = $conn->query($sql);
    $stats['totalConfigs'] = $result->fetch_assoc()['total'];
    
    // 2. 启用配置数
    $sql = "SELECT COUNT(*) as total FROM Kunlun_sms_alert_config WHERE is_active = 1";
    $result = $conn->query($sql);
    $stats['activeConfigs'] = $result->fetch_assoc()['total'];
    
    // 3. 今日发送短信数
    $sql = "SELECT COUNT(*) as total FROM Kunlun_sms_send_log 
            WHERE DATE(created_time) = CURDATE()";
    $result = $conn->query($sql);
    $stats['todaySms'] = $result->fetch_assoc()['total'];
    
    // 4. 今日成功率
    $sql = "SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN send_status = 'success' THEN 1 ELSE 0 END) as success
            FROM Kunlun_sms_send_log 
            WHERE DATE(created_time) = CURDATE()";
    $result = $conn->query($sql);
    $row = $result->fetch_assoc();
    $stats['successRate'] = $row['total'] > 0 ? round(($row['success'] / $row['total']) * 100, 1) : 0;
    
    // 5. 最近7天发送统计
    $sql = "SELECT 
                DATE(created_time) as date,
                COUNT(*) as total,
                SUM(CASE WHEN send_status = 'success' THEN 1 ELSE 0 END) as success,
                SUM(CASE WHEN send_status = 'failed' THEN 1 ELSE 0 END) as failed
            FROM Kunlun_sms_send_log 
            WHERE created_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            GROUP BY DATE(created_time)
            ORDER BY date DESC";
    $result = $conn->query($sql);
    $weeklyStats = [];
    while ($row = $result->fetch_assoc()) {
        $weeklyStats[] = $row;
    }
    $stats['weeklyStats'] = $weeklyStats;
    
    // 6. 预警级别分布
    $sql = "SELECT 
                alert_level,
                COUNT(*) as count
            FROM Kunlun_sms_alert_config 
            WHERE is_active = 1
            GROUP BY alert_level";
    $result = $conn->query($sql);
    $levelDistribution = [];
    while ($row = $result->fetch_assoc()) {
        $levelDistribution[] = $row;
    }
    $stats['levelDistribution'] = $levelDistribution;
    
    // 7. 最近预警记录
    $sql = "SELECT 
                ar.person_name,
                ar.person_sfz,
                ar.score,
                ar.severity,
                ar.location_name,
                ar.captured_time,
                COUNT(ssl.id) as sms_sent
            FROM Kunlun_alarm_records ar
            LEFT JOIN Kunlun_sms_send_log ssl ON ar.id = ssl.alarm_record_id
            WHERE ar.captured_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            GROUP BY ar.id
            ORDER BY ar.captured_time DESC
            LIMIT 10";
    $result = $conn->query($sql);
    $recentAlarms = [];
    while ($row = $result->fetch_assoc()) {
        $recentAlarms[] = $row;
    }
    $stats['recentAlarms'] = $recentAlarms;
    
    // 8. 发送状态统计
    $sql = "SELECT 
                send_status,
                COUNT(*) as count
            FROM Kunlun_sms_send_log 
            WHERE created_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY send_status";
    $result = $conn->query($sql);
    $statusStats = [];
    while ($row = $result->fetch_assoc()) {
        $statusStats[] = $row;
    }
    $stats['statusStats'] = $statusStats;
    
    // 9. 单位发送统计
    $sql = "SELECT 
                ssl.recipient_unit_name,
                COUNT(*) as total,
                SUM(CASE WHEN ssl.send_status = 'success' THEN 1 ELSE 0 END) as success
            FROM Kunlun_sms_send_log ssl
            WHERE ssl.created_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            AND ssl.recipient_unit_name IS NOT NULL
            GROUP BY ssl.recipient_unit_name
            ORDER BY total DESC
            LIMIT 10";
    $result = $conn->query($sql);
    $unitStats = [];
    while ($row = $result->fetch_assoc()) {
        $unitStats[] = $row;
    }
    $stats['unitStats'] = $unitStats;
    
    // 10. 系统健康状态
    $stats['systemHealth'] = getSystemHealth();
    
    return $stats;
}

/**
 * 获取系统健康状态
 */
function getSystemHealth() {
    global $conn;
    
    $health = [
        'status' => 'healthy',
        'issues' => []
    ];
    
    // 检查是否有失败的短信
    $sql = "SELECT COUNT(*) as failed_count 
            FROM Kunlun_sms_send_log 
            WHERE send_status = 'failed' 
            AND created_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)";
    $result = $conn->query($sql);
    $failedCount = $result->fetch_assoc()['failed_count'];
    
    if ($failedCount > 10) {
        $health['status'] = 'warning';
        $health['issues'][] = "最近1小时内有 {$failedCount} 条短信发送失败";
    }
    
    // 检查是否有过期的配置
    $sql = "SELECT COUNT(*) as expired_count 
            FROM Kunlun_sms_alert_config 
            WHERE is_active = 1 
            AND valid_end_time IS NOT NULL 
            AND valid_end_time < NOW()";
    $result = $conn->query($sql);
    $expiredCount = $result->fetch_assoc()['expired_count'];
    
    if ($expiredCount > 0) {
        $health['issues'][] = "有 {$expiredCount} 个配置已过期但仍处于启用状态";
    }
    
    // 检查是否有待发送的短信
    $sql = "SELECT COUNT(*) as pending_count 
            FROM Kunlun_sms_send_log 
            WHERE send_status = 'pending' 
            AND created_time < DATE_SUB(NOW(), INTERVAL 10 MINUTE)";
    $result = $conn->query($sql);
    $pendingCount = $result->fetch_assoc()['pending_count'];
    
    if ($pendingCount > 0) {
        $health['status'] = 'warning';
        $health['issues'][] = "有 {$pendingCount} 条短信超过10分钟仍未发送";
    }
    
    // 检查最近是否有预警记录
    $sql = "SELECT COUNT(*) as recent_alarms 
            FROM Kunlun_alarm_records 
            WHERE created_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)";
    $result = $conn->query($sql);
    $recentAlarms = $result->fetch_assoc()['recent_alarms'];
    
    if ($recentAlarms == 0) {
        $health['issues'][] = "最近1小时内没有新的预警记录，请检查数据采集是否正常";
    }
    
    return $health;
}
?>
