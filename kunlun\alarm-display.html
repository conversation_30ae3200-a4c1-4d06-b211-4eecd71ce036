<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时报警监控系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .status-bar {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 500;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .status-connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .indicator-connected {
            background: #28a745;
        }

        .indicator-connecting {
            background: #ffc107;
        }

        .indicator-disconnected {
            background: #dc3545;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .stats {
            display: flex;
            gap: 1rem;
            font-size: 0.9rem;
            color: #666;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .alarms-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .alarm-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            animation: slideIn 0.5s ease-out;
        }

        .alarm-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .alarm-header {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
            position: relative;
        }

        .severity-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .severity-high {
            background: #fee;
            color: #c53030;
            border: 1px solid #fed7d7;
        }

        .severity-medium {
            background: #fffbeb;
            color: #d69e2e;
            border: 1px solid #faf089;
        }

        .severity-low {
            background: #f0fff4;
            color: #38a169;
            border: 1px solid #9ae6b4;
        }

        .alarm-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .alarm-time {
            color: #718096;
            font-size: 0.9rem;
        }

        .alarm-body {
            padding: 1.5rem;
        }

        .alarm-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 0.8rem;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.25rem;
        }

        .info-value {
            font-weight: 500;
            color: #2d3748;
        }

        .score-display {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .score-bar {
            flex: 1;
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            overflow: hidden;
        }

        .score-fill {
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .alarm-image {
            width: 100%;
            max-height: 200px;
            object-fit: cover;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .alarm-image:hover {
            transform: scale(1.02);
        }

        .map-container {
            margin-top: 1rem;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e2e8f0;
        }

        .map-header {
            background: #f7fafc;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .map-title {
            font-weight: 500;
            color: #2d3748;
            font-size: 0.9rem;
        }

        .map-toggle {
            background: #4299e1;
            color: white;
            border: none;
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .map-toggle:hover {
            background: #3182ce;
        }

        .map-frame {
            width: 100%;
            height: 250px;
            border: none;
            display: block;
        }

        .coordinates-link {
            color: #4299e1;
            text-decoration: none;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .coordinates-link:hover {
            color: #3182ce;
            text-decoration: underline;
        }

        .no-alarms {
            text-align: center;
            padding: 4rem 2rem;
            color: #718096;
        }

        .no-alarms-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .retry-button {
            background: #4299e1;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s ease;
        }

        .retry-button:hover {
            background: #3182ce;
        }

        .retry-button:disabled {
            background: #a0aec0;
            cursor: not-allowed;
        }

        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .container {
                padding: 1rem;
            }

            .alarms-grid {
                grid-template-columns: 1fr;
            }

            .status-bar {
                flex-direction: column;
                align-items: flex-start;
            }

            .alarm-info {
                grid-template-columns: 1fr;
            }
        }

        .error-message {
            background: #fed7d7;
            color: #c53030;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            border-left: 4px solid #e53e3e;
        }


    </style>
</head>
<body>
    <div class="header">
        <h1>🚨 实时报警监控系统</h1>
        <div class="status-bar">
            <div id="connectionStatus" class="connection-status status-connecting">
                <div class="status-indicator indicator-connecting"></div>
                <span>正在连接...</span>
            </div>
            <div class="stats">
                <span>总报警数: <strong id="totalAlarms">0</strong></span>
                <span>最后更新: <strong id="lastUpdate">--</strong></span>
                <span>重连次数: <strong id="reconnectCount">0</strong></span>
                <button id="retryButton" class="retry-button" style="display: none;">重新连接</button>
            </div>
        </div>
        <div id="errorMessage" class="error-message" style="display: none;"></div>
    </div>

    <div class="container">
        <div id="alarms" class="alarms-grid"></div>
        <div id="noAlarms" class="no-alarms">
            <div class="no-alarms-icon">📡</div>
            <h3>等待报警数据...</h3>
            <p>系统正在监听实时报警信息</p>
        </div>
    </div>

    <!-- 图片模态框 -->
    <div id="imageModal" class="image-modal">
        <div class="image-modal-content">
            <div class="image-modal-header">
                <span class="image-modal-title" id="imageModalTitle">抓拍图片</span>
                <button class="image-modal-close" onclick="closeImageModal()">&times;</button>
            </div>
            <div class="image-modal-body">
                <img id="imageModalImg" src="" alt="抓拍图片">
            </div>
        </div>
    </div>

    <script>
        class SSEManager {
            constructor() {
                this.eventSource = null;
                this.reconnectAttempts = 0;
                this.baseDelay = 1000; // 1秒
                this.maxDelay = 30000; // 30秒
                this.isManuallyDisconnected = false;
                this.totalAlarms = 0;

                this.init();
                this.updateReconnectCount();
            }

            init() {
                this.connect();
                this.setupRetryButton();
            }

            connect() {
                if (this.eventSource) {
                    this.eventSource.close();
                }

                this.updateConnectionStatus('connecting', '正在连接...');
                this.hideError();

                try {
                    this.eventSource = new EventSource('kafka.php');
                    this.setupEventListeners();
                } catch (error) {
                    this.handleError('连接失败: ' + error.message);
                }
            }

            setupEventListeners() {
                this.eventSource.onopen = () => {
                    this.updateConnectionStatus('connected', '已连接');
                    this.hideRetryButton();
                    this.updateReconnectCount();
                    // 连接成功后重置重连计数器，但保留总重连次数用于显示
                    this.reconnectAttempts = 0;
                };

                this.eventSource.onerror = (error) => {
                    console.error('SSE连接错误:', error);
                    this.updateConnectionStatus('disconnected', '连接断开');

                    if (!this.isManuallyDisconnected) {
                        this.scheduleReconnect();
                    }
                };

                this.eventSource.addEventListener('connection', (e) => {
                    const data = JSON.parse(e.data);
                    console.log('连接状态:', data);
                    this.updateLastUpdate(data.server_time);
                });

                this.eventSource.addEventListener('heartbeat', (e) => {
                    const data = JSON.parse(e.data);
                    this.updateLastUpdate(data.server_time);
                });

                this.eventSource.addEventListener('alarm', (e) => {
                    const data = JSON.parse(e.data);
                    this.handleAlarm(data);
                    this.updateLastUpdate(data.time);
                });

                this.eventSource.addEventListener('error', (e) => {
                    const data = JSON.parse(e.data);
                    this.handleError('服务器错误: ' + data.error);
                });
            }

            scheduleReconnect() {
                const delay = Math.min(
                    this.baseDelay * Math.pow(2, this.reconnectAttempts),
                    this.maxDelay
                );

                this.reconnectAttempts++;
                this.updateConnectionStatus('connecting', `重连中... (第${this.reconnectAttempts}次尝试)`);
                this.updateReconnectCount();

                setTimeout(() => {
                    if (!this.isManuallyDisconnected) {
                        this.connect();
                    }
                }, delay);
            }

            handleAlarm(data) {
                this.totalAlarms++;
                document.getElementById('totalAlarms').textContent = this.totalAlarms;
                document.getElementById('noAlarms').style.display = 'none';

                const alarmCard = this.createAlarmCard(data);
                document.getElementById('alarms').prepend(alarmCard);

                // 限制显示的报警数量
                const alarms = document.querySelectorAll('.alarm-card');
                if (alarms.length > 50) {
                    alarms[alarms.length - 1].remove();
                }
            }

            createAlarmCard(data) {
                const card = document.createElement('div');
                card.className = 'alarm-card';

                const severityClass = `severity-${data.severity || 'low'}`;
                const severityText = {
                    'high': '高',
                    'medium': '中',
                    'low': '低'
                }[data.severity || 'low'];

                const scoreColor = data.score >= 90 ? '#e53e3e' :
                                 data.score >= 70 ? '#d69e2e' : '#38a169';

                card.innerHTML = `
                    <div class="alarm-header">
                        <div class="severity-badge ${severityClass}">${severityText}</div>
                        <div class="alarm-title">报警编号: ${data.alarmNo}</div>
                        <div class="alarm-time">📅 ${data.time}</div>
                    </div>
                    <div class="alarm-body">
                        <div class="alarm-info">
                            <div class="info-item">
                                <div class="info-label">姓名</div>
                                <div class="info-value">👤 ${data.name}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">身份证</div>
                                <div class="info-value">🆔 ${data.sfz}</div>
                            </div>
                            <div class="info-item" style="grid-column: 1 / -1;">
                                <div class="info-label">相似度</div>
                                <div class="score-display">
                                    <div class="score-bar">
                                        <div class="score-fill" style="width: ${data.score}%; background: ${scoreColor};"></div>
                                    </div>
                                    <span class="info-value">${data.score}%</span>
                                </div>
                            </div>
                        </div>
                        <div class="map-container">
                            <div class="map-header">
                                <span class="map-title">📍 ${data.location}</span>
                            </div>
                            <iframe id="map-${data.id}" class="map-frame show" src="map/index.php?lat=${data.lat}&lon=${data.lon}&label=${encodeURIComponent(data.location)}"></iframe>
                        </div>
                        ${data.image ? `<img src="${data.image}" alt="抓拍图片" class="alarm-image" onclick="window.open('${data.image}', '_blank')" onerror="this.style.display='none'">` : ''}
                    </div>
                `;

                return card;
            }

            updateConnectionStatus(status, message) {
                const statusElement = document.getElementById('connectionStatus');
                const indicator = statusElement.querySelector('.status-indicator');

                statusElement.className = `connection-status status-${status}`;
                indicator.className = `status-indicator indicator-${status}`;
                statusElement.querySelector('span').textContent = message;
            }

            updateLastUpdate(time) {
                document.getElementById('lastUpdate').textContent = time || new Date().toLocaleTimeString();
            }

            updateReconnectCount() {
                document.getElementById('reconnectCount').textContent = this.reconnectAttempts;
            }

            handleError(message) {
                const errorElement = document.getElementById('errorMessage');
                errorElement.textContent = message;
                errorElement.style.display = 'block';
                console.error(message);
            }

            hideError() {
                document.getElementById('errorMessage').style.display = 'none';
            }

            showRetryButton() {
                document.getElementById('retryButton').style.display = 'inline-block';
            }

            hideRetryButton() {
                document.getElementById('retryButton').style.display = 'none';
            }

            setupRetryButton() {
                document.getElementById('retryButton').addEventListener('click', () => {
                    this.isManuallyDisconnected = false;
                    this.reconnectAttempts = 0;
                    this.connect();
                });
            }

            disconnect() {
                this.isManuallyDisconnected = true;
                if (this.eventSource) {
                    this.eventSource.close();
                }
                this.updateConnectionStatus('disconnected', '已断开连接');
            }
        }

        // 初始化SSE管理器
        const sseManager = new SSEManager();

        // 页面卸载时断开连接
        window.addEventListener('beforeunload', () => {
            sseManager.disconnect();
        });

        // 图片模态框功能
        function openImageModal(imageSrc, alarmNo) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('imageModalImg');
            const modalTitle = document.getElementById('imageModalTitle');

            modalImg.src = imageSrc;
            modalTitle.textContent = `报警图片 - ${alarmNo}`;
            modal.style.display = 'block';

            // 防止背景滚动
            document.body.style.overflow = 'hidden';
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // 点击模态框背景关闭
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImageModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageModal();
            }
        });


    </script>
</body>
</html>