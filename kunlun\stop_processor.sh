#!/bin/bash
# 停止昆仑预警数据处理程序
# 创建时间: 2025-08-12
# 适用系统: Linux

echo "正在停止昆仑预警数据处理程序..."

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 设置文件路径
LOG_DIR="$SCRIPT_DIR/logs"
PID_FILE="$LOG_DIR/processor.pid"

# 检查PID文件是否存在
if [ ! -f "$PID_FILE" ]; then
    echo "未找到PID文件，程序可能未运行"
    exit 1
fi

# 读取PID
PID=$(cat "$PID_FILE")

if [ -z "$PID" ] || ! [[ "$PID" =~ ^[0-9]+$ ]]; then
    echo "无效的PID: $PID"
    rm -f "$PID_FILE"
    exit 1
fi

# 检查进程是否在运行
if ! ps -p "$PID" > /dev/null 2>&1; then
    echo "进程 $PID 未运行，删除PID文件"
    rm -f "$PID_FILE"
    exit 1
fi

echo "找到运行中的进程 (PID: $PID)"

# 尝试优雅停止
echo "发送TERM信号..."
kill -TERM "$PID"

# 等待进程停止
for i in {1..10}; do
    if ! ps -p "$PID" > /dev/null 2>&1; then
        echo "进程已停止"
        rm -f "$PID_FILE"
        echo "停止完成！"
        exit 0
    fi
    echo "等待进程停止... ($i/10)"
    sleep 1
done

# 如果优雅停止失败，强制停止
echo "优雅停止失败，强制停止进程..."
kill -KILL "$PID"

# 再次检查
sleep 2
if ! ps -p "$PID" > /dev/null 2>&1; then
    echo "进程已强制停止"
    rm -f "$PID_FILE"
    echo "停止完成！"
else
    echo "错误: 无法停止进程 $PID"
    exit 1
fi
