<?php
// 初始化一个curl句柄
$ch = curl_init();

// 设置请求的URL
curl_setopt($ch, CURLOPT_URL, 'http://80.164.12.151:9100/openapi/kunlun/v1/device/cameras:search');

// 设置请求方法为POST
curl_setopt($ch, CURLOPT_POST, 1);

// 设置请求头
$headers = [
    'Content-Type: application/json;charset=UTF-8',
    'X-OA-Token: c58d087660b4415faa71496ba2003ae5',
    'Api_Id: device_cameraSearch'
];
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

// 设置请求体数据
$data = '{"types":[1],"pageSize":20,"pageNo":1}';
curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

// 执行请求
$response = curl_exec($ch);

// 检查执行结果
if (curl_errno($ch)) {
    echo 'Error:' . curl_error($ch);
} else {
    // 处理响应
    $result = json_decode($response, true);
    print_r($result);
}

// 释放curl句柄
curl_close($ch);
?>