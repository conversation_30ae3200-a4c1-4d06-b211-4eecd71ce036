﻿var mappoints;
var map;
var markers = [];
var onlineIcon;
var offlineIcon;
var editmarker;
function start()
{
	var mapOptions = {
		minZoom: 5,// 地图最小层级
		maxZoom: 18,// 地图最小层级
		mapType: BMAP_NORMAL_MAP
	}
	map = new BMap.Map("container", mapOptions);      //设置卫星图为底图BMAP_PERSPECTIVE_MAP
	var initPoint = new BMap.Point(106.404, 30.915);    // 创建点坐标
	map.addControl(new BMap.MapTypeControl({mapTypes: [BMAP_NORMAL_MAP,BMAP_SATELLITE_MAP,BMAP_HYBRID_MAP]})); //负责切换地图类型的控件	
	map.centerAndZoom(initPoint,10);                    // 初始化地图,设置中心点坐标和地图级别。
	map.enableScrollWheelZoom();                  // 启用滚轮放大缩小。
	map.enableKeyboard();                         // 启用键盘操作。  
	map.enableContinuousZoom();										//启用连续缩放效果
	
	// ----- control -----
	map.addControl(new BMap.NavigationControl()); //地图平移缩放控件
	map.addControl(new BMap.ScaleControl()); //显示比例尺在右下角
	//map.addControl(new BMap.OverviewMapControl({anchor: BMAP_ANCHOR_TOP_LEFT, isOpen: true})); //缩略图控件
	//map.disableDragging(); //禁止地图拖动
	 map.enableDragging();//启动地图拖动
/*
	//
	
	// ----- maker -----

	//addCabinMarker(initPoint);//设置标注点
	function addCabinMarker(point) { 
		var cabinIcon = new BMap.Icon("images/qouji.png", new BMap.Size(36, 37));    
		var cabinMarkerOptions = {
			icon: cabinIcon,
			//enableDragging: true,
			//draggingCursor: "move",
			title: "标注点",
			rotation: 60
		}
 		var cabinMarker = new BMap.Marker(point, cabinMarkerOptions);  
 		cabinMarker.setAnimation(BMAP_ANIMATION_DROP);
 		
 		//map.addOverlay(cabinMarker);
		cabinMarker.setRotation(120);
 		cabinMarker.addEventListener("dragging", function(e) {
			document.getElementById("position").innerHTML = "坐标像素</br>x :" + e.pixel.x + " y :" + e.pixel.y + "<br>坐标经纬度</br>经度: " + e.point.lng + " 纬度: " + e.point.lat;//获取经纬度
		}); 
	}
	//
	var MAX = 10;

	var pt = null;*/
	onlineIcon = new BMap.Icon("images/qouji_online.png", new BMap.Size(24, 24)); 
	offlineIcon = new BMap.Icon("images/qouji.png", new BMap.Size(24, 24)); 
	var xmlhttp;

	if (window.XMLHttpRequest)
  	{// code for IE7+, Firefox, Chrome, Opera, Safari
  		xmlhttp=new XMLHttpRequest();
  	}
	else
  	{// code for IE6, IE5
  		xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
  	}
	xmlhttp.onreadystatechange=function()
  	{
  		if (xmlhttp.readyState==4 && xmlhttp.status==200)
    		{
    		var mappoints=xmlhttp.responseText.split("|");//返回的数据
			for(t1=0;t1<mappoints.length;t1++)
			{
				mappoints[t1]=mappoints[t1].split(",");
   				var cabinMarkerOptions = 
				{
					icon: onlineIcon,
					
					//enableDragging: true,
					//draggingCursor: "move",
					title:mappoints[t1][3]//,
					//rotation: 60
				}
 				var cabinMarker = new BMap.Marker( new BMap.Point(mappoints[t1][8], mappoints[t1][9]), cabinMarkerOptions);  
				cabinMarker.camerid=mappoints[t1][0],
				//cabinMarker.addEventListener("click",function(e){window.external.OnGoogleEleDbClick(e.target.camerid);});
	  			markers.push(cabinMarker);
				//map.addOverlay(cabinMarker);
		
			}

		//最简单的用法，生成一个marker数组，然后调用markerClusterer类即可。
		var markerClusterer = new BMapLib.MarkerClusterer(map, {markers:markers,maxZoom:17});
		window.external.OnDocumentLoadFinished();
		window.external.OnLoad(); 
			
   		 }
  	}
	xmlhttp.open("GET","mappoint.php",true);
	xmlhttp.send();

}

function clickfun(e)
{
	window.external.OnGoogleEleDbClick(e.target.camerid);
	window.external.OnGoogleEleLButtonUp(e.target.camerid);
}

function FocusToElement(id)
{	//alert("function FocusToElement(id)"+id+markers[0].camerid);
	for(var t=0;t<markers.length;t++)
	{
		if(markers[t].camerid==id)
		{
			//alert(id);
			map.centerAndZoom(new BMap.Point(markers[t].point.lng,markers[t].point.lat),18); 
			editmarker=markers[t];
			var timer=null;
			var i=0;
			timer=setInterval(function(){i++%2?map.addOverlay(editmarker):map.removeOverlay(editmarker);i>7&&(clearInterval(timer))},300);
		}
					  
	}
}

function LoadMap(zoomLevel, longitude, latitude)//window.external.OnDocumentLoadFinished后会调用这个函数
{}
function GetElementScreenPos(elementId)//点击摄像头后需要在这里返回显示打开视频菜单的屏幕坐标
{
	return("<mapxml><mapwnd><x>-400</x><y>-300</y></mapwnd></mapxml>");
	//window.external.GetJsResult("<mapxml><mapwnd><x>100</x><y>300</y></mapwnd></mapxml>");
}
function AddMapElement(id,longitude, latitude, imgpath, title, options)//初始化完成后要添加图元
{
//alert("AddMapElement()"+id+longitude+ latitude+ imgpath+title+options);
}
function RemoveMapElementStatus(id, status)//移除摄像头状态 onload后运行
{
//alert("RemoveMapElementStatus()"+id+status);
}
function GetElementCoord(elementId)//查看摄像头详细信息时需要返回摄像头的经纬度
{	
	//var xml = "<mapxml><mapcoord><longitude>106.0000</longitude><latitude>30.5000</latitude></mapcoord></mapxml>";
	//window.external.GetJsResult(xml);
	//return xml;
}
function SetMapElementStatus(id, status)
{
	for(var t=0;t<markers.length;t++)
	{
		if(markers[t].camerid==id)
		{
			//alert(id);
			if(status=="offline")	
			{
				//alert(id);
				markers[t].setIcon(offlineIcon);
			}
			else if(status=="online")	
			{
				//alert(id);
				markers[t].setIcon(onlineIcon);
			}
		}
					  
	}
}//onload后启动


function UnLockMap(){}//alert("function UnLockMap()");}//菜单解锁地图
function UnloadMap(){}//alert("function UnloadMap()");}
function StopGPSTrackFlash(id){}//alert("function StopGPSTrackFlash(id)");}
function StopElementFlash(id){}//alert("function StopElementFlash(id)");}
function StartRecPlayTimeList(startTime, trackId){}//alert("function StartRecPlayTimeList(startTime, trackId)");}
function StartGPSTrackFlash(id){}//alert("function StartGPSTrackFlash(id)");}
function StartElementFlash(id){}//alert("function StartElementFlash(id)");}
function ShowMarkerInfo(){}//alert("function ShowMarkerInfo()");}//菜单显示元素信息
function ShowGpsTrack(id, bShow){}//alert("function ShowGpsTrack(id, bShow)");}
function ShowGpsPointInfo(pointsTime){}//alert("function ShowGpsPointInfo(pointsTime)");}
function ShowClickPointTime(trackId,pointsCoord, pointsTime){}//alert("function ShowClickPointTime(trackId,pointsCoord, pointsTime)");}
function SetZoomLevel(level){}//alert("function SetZoomLevel(level)");}//onload后启动
function SetTrackStartRecFlag(trackId, longitude, latitude){}//alert("function SetTrackStartRecFlag(trackId, longitude, latitude)");}
function SetTrackEndRecFlag(trackId, longitude, latitude){}//alert("function SetTrackEndRecFlag(trackId, longitude, latitude)");}
function SetTrackColor(id, color){}//alert("function SetTrackColor(id, color)");}
function SetSelectedTrack(id){}//alert("function SetSelectedTrack(id)");}
function SetSearchElesMode(){}//alert("function SetSearchElesMode()");}
function SetOverViewPos(position){}//alert("function SetOverViewPos(position)");}//菜单设置鹰眼
function SetMapviewMode(viewMode){}//alert("function SetMapviewMode(viewMode)");}
function SetImageServerPath(isUseGoogle, path){}//alert("function SetImageServerPath(isUseGoogle, path)");}
function SetGpsTrackTip(id, tip){}//alert("function SetGpsTrackTip(id, tip)");}
function SetGpsTrackProp(id, color, weight,type){}//alert("function SetGpsTrackProp(id, color, weight,type)");}
function SetElementVisible(id, visible){}//alert("function SetElementVisible(id, visible)");}
function SetElementToolTip(id, tip){}//alert("function SetElementToolTip(id, tip)");}
function SetElementTitleVisible(id, visible){}//alert("function SetElementTitleVisible(id, visible)");}
function SetElementTitleProp(id, font, fontsize, fontcolor, isbold, isitalic, isunderline, isstrike){}//alert("function SetElementTitleProp(id, font, fontsize, fontcolor, isbold, isitalic, isunderline, isstrike)");}
function SetElementTitle(id, title){}//alert("function SetElementTitle(id, title)");}
function SetElementImageSize(id, width, height){}//alert("function SetElementImageSize(id, width, height)");}
function SetElementImage(id, imgpath){}//alert("function SetElementImage(id, imgpath)");}
function SetCursorStyle(style){}//alert("function SetCursorStyle(style)");}
function SetCenter(longitude, latitude){}//alert("function SetCenter(longitude, latitude)");}//onload后加载
function SearchElementsByCircle(longitude, latitude, rDistance){}//alert("function SearchElementsByCircle(longitude, latitude, rDistance)");}
function ResetCenterEditPosition(){}//alert("function ResetCenterEditPosition()");}
function RemoveGpsTrack(id){}//alert("function RemoveGpsTrack(id)");}
function RemoveElement(id){}//alert("function RemoveElement(id)");}
function RemoveAllTracks(){}//alert("function RemoveAllTracks()");}
function ReloadMap(){}//alert("function ReloadMap()");}
function RedrawGpsTrack(id, points){}//alert("function RedrawGpsTrack(id, points)");}
function randomNumber(limit)  {}//alert("function randomNumber(limit)  ");}
function randomBgColor()   {}//alert("function randomBgColor()   ");}
function QueryRectCallBack(strPoint){}//alert("function QueryRectCallBack(strPoint)");}
function QueryRect(){}//alert("function QueryRect()");}
function QueryPolygonCallBack(strPoint){}//alert("function QueryPolygonCallBack(strPoint)");}
function QueryPolygon(){}//alert("function QueryPolygon()");}
function QueryPointCallBack(strPoint){}//alert("function QueryPointCallBack(strPoint)");}
function QueryPoint(){}//alert("function QueryPoint()");}
function paramString2obj (serializedParams){}//alert("function paramString2obj (serializedParams)");}
function pagePos(event){}//alert("function pagePos(event)");}
function OnSetCenter(longitude, latitude){}//alert("function OnSetCenter(longitude, latitude)");}
function onmydrop(event){}//alert("function onmydrop(event)");}
function OnMapMouseLeave(evt){}//alert("function OnMapMouseLeave(evt)");}
function OnMapMouseEnter(evt){}//alert("function OnMapMouseEnter(evt)");}
function OnMapKeyDown(evt){}//alert("function OnMapKeyDown(evt)");}
function OnHintCenterPosEdit(){}//alert("function OnHintCenterPosEdit()");}
function MoveElement(id, longitude, latitude){}//alert("function MoveElement(id, longitude, latitude)");}
function ModifyMapElement(id,longitude, latitude, imgpath, title){}//alert("function ModifyMapElement(id,longitude, latitude, imgpath, title)");}
function ModGpsTrackName(id, name){}//alert("function ModGpsTrackName(id, name)");}
function LockMap(){}//alert("function LockMap()");}//onload后启动
function Locate(longitude, latitude) {}//alert("function Locate(longitude, latitude) ");}
function JSONStrToObject(JSONStr){}//alert("function JSONStrToObject(JSONStr)");}
function IsPointInRect(point, bound){}//alert("function IsPointInRect(point, bound)");}
function IsLocationVisiable(longitude, latitude){}//alert("function IsLocationVisiable(longitude, latitude)");}
function IsGpsTrackExists(id){}//alert("function IsGpsTrackExists(id)");}
function HideMarkerInfo(){}//alert("function HideMarkerInfo()");}//onload后启动
function GpsTrackLineTo(id, longitude, latitude){}//alert("function GpsTrackLineTo(id, longitude, latitude)");}
function GpsTrackBatchLineTo(id, points){}//alert("function GpsTrackBatchLineTo(id, points)");}
function GetZoomLevel(){}//alert("function GetZoomLevel()");}//退出时查询并保存
function GetTrackColor(id){}//alert("function GetTrackColor(id)");}
function GetPolygonString(){}//alert("function GetPolygonString()");}
function GetMapElementCount(){}//alert("function GetMapElementCount()");}
function GetMapBounds(){}//alert("function GetMapBounds()");}
function GetImageSourse(){}//alert("function GetImageSourse()");}
function GetElementTitle(id){}//alert("function GetElementTitle(id)");}
function GetCenter(){}//alert("function GetCenter()");}//退出时查询并保存
function getBrowserVersion(){}//alert("function getBrowserVersion()");}
function FocusToTrack(traceId){}//alert("function FocusToTrack(traceId)");}

function DrawSelectEffect(rect,center, size){}//alert("function DrawSelectEffect(rect,center, size)");}
function DrawGPSTrack(id, points, name, weight, type, color){}//alert("function DrawGPSTrack(id, points, name, weight, type, color)");}
function DistanceToLatitude(distance){}//alert("function DistanceToLatitude(distance)");}
function DistanceMapZLevel(distance){}//alert("function DistanceMapZLevel(distance)");}
function decToHex(dec)   {}//alert("function decToHex(dec)   ");}
function CreateGpsTrack(id, name, points, weight, type, color, bNeedStartFlag, bNeedEndFlag){}//alert("function CreateGpsTrack(id, name, points, weight, type, color, bNeedStartFlag, bNeedEndFlag)");}
function ConvertWndCoord2MapCoord(x, y){}//alert("function ConvertWndCoord2MapCoord(x, y)");}
function ConvertCoord2Wnd(x, y){}//alert("function ConvertCoord2Wnd(x, y)");}
function ClearPolygon(){}//alert("function ClearPolygon()");}
function clearDragMode(){}//alert("function clearDragMode()");}
function AddMapElementByWndCoord(id, x, y, imgpath, title, options){}//alert("function AddMapElementByWndCoord(id, x, y, imgpath, title, options)");}
function uaMatch(ua)   {}//alert("function uaMatch(ua)   ");}

