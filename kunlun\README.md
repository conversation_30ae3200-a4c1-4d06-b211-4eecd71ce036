# 预警系统 - 完整开发文档

## 项目概述

基于SSE（服务器发送事件）的实时预警监控系统，集成了人员识别、地图定位、短信通知等功能。系统能够实时监控预警信息，自动存储预警数据，并根据配置规则发送短信通知。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Kafka数据源   │───▶│   SSE服务器     │───▶│   前端监控页面   │
│   (kafka.php)  │    │  (实时数据流)   │    │ (alarm-display) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   后台处理程序   │
                       │(background_     │
                       │ processor.php)  │
                       └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   数据库存储    │───▶│   短信通知系统   │
                       │  (MySQL表)     │    │  (SmsSystem)    │
                       └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   管理后台      │
                       │  (sms_admin)   │
                       └─────────────────┘
```

## 核心功能

### 1. 实时监控系统
- **文件**: `alarm-display.html`
- **功能**: 
  - 实时显示预警信息
  - 现代化卡片布局
  - 自动重连机制（无限重连）
  - 地图集成显示
  - 图片Base64处理

### 2. 数据存储系统
- **文件**: `background_processor.php`
- **功能**:
  - 监听SSE数据流
  - 自动存储预警记录
  - 触发短信通知
  - 错误处理和重试

### 3. 短信通知系统
- **文件**: `sms_admin/`
- **功能**:
  - 预警配置管理
  - 短信发送记录
  - 批量重发功能
  - 统计分析

### 4. 系统管理
- **文件**: `system_manager.php`
- **功能**:
  - 系统状态监控
  - 服务启停控制
  - 数据清理
  - 健康检查

## 数据库设计

### 主要表结构

#### 1. Kunlun_alarm_records (预警记录表)
```sql
- id: 主键
- alarm_id: 报警ID（唯一）
- person_name: 预警人员姓名
- person_sfz: 身份证号
- score: 相似度分数
- location_name: 位置名称
- latitude/longitude: 经纬度
- image_base64: 图片Base64数据
- severity: 严重级别 (low/medium/high)
- captured_time: 抓拍时间
- is_processed: 是否已处理
```

#### 2. Kunlun_sms_alert_config (短信配置表)
```sql
- id: 主键
- alarm_person_sfz: 预警人员身份证
- recipient_user_id: 接收人员ID
- alert_level: 预警级别 (all/high/medium/low)
- is_active: 是否启用
- valid_start_time/valid_end_time: 有效期
```

#### 3. Kunlun_sms_send_log (短信记录表)
```sql
- id: 主键
- alarm_record_id: 关联预警记录
- recipient_phone: 接收手机号
- sms_content: 短信内容
- send_status: 发送状态 (pending/success/failed/expired)
- send_time: 发送时间
- retry_count: 重试次数
```

## 文件结构

```
kunlun/
├── README.md                    # 项目文档
├── database_schema.sql          # 数据库表结构
├── install_database.php        # 数据库安装脚本
├── kafka.php                   # SSE服务器（增强版）
├── alarm-display.html          # 实时监控页面（增强版）
├── background_processor.php    # 后台数据处理程序
├── system_manager.php          # 系统管理器
├── system_test.php             # 系统测试页面
├── test-sse.html              # SSE连接测试
├── test-map.html              # 地图功能测试
├── test-image-download.php    # 图片下载测试
├── logs/                      # 日志目录
├── map/
│   ├── index.php              # 地图页面（PHP版）
│   ├── js/                    # 地图JS文件
│   └── images/                # 地图图标
└── sms_admin/                 # 短信管理后台
    ├── index.php              # 主页面
    └── api/
        ├── config_manage.php  # 配置管理API
        ├── sms_manage.php     # 短信管理API
        └── stats.php          # 统计API
```

## 安装部署

### 1. 数据库安装
```bash
# 访问数据库安装页面
http://your-domain/kunlun/install_database.php

# 或直接执行SQL文件
mysql -u username -p database_name < database_schema.sql
```

### 2. 启动后台处理程序
```bash
# 命令行启动
php background_processor.php

# 或设置为系统服务（推荐）
# 创建systemd服务文件
sudo nano /etc/systemd/system/kunlun-processor.service
```

### 3. 配置Web服务器
确保Web服务器支持：
- PHP 7.4+
- MySQL 5.7+
- SSE长连接
- 文件上传和Base64处理

## 使用指南

### 1. 系统管理
访问 `system_manager.php` 进行：
- 系统状态检查
- 服务启停控制
- 数据清理维护

### 2. 短信配置
访问 `sms_admin/` 进行：
- 添加预警人员配置
- 设置接收人员
- 管理发送记录

### 3. 实时监控
访问 `alarm-display.html` 查看：
- 实时预警信息
- 地图位置显示
- 预警图片查看

### 4. 系统测试
访问 `system_test.php` 进行：
- 功能模块测试
- 完整流程验证
- 性能压力测试

## 技术特性

### 1. 高可用性
- 自动重连机制
- 错误恢复处理
- 服务状态监控
- 数据备份策略

### 2. 性能优化
- 数据库索引优化
- 图片Base64缓存
- 分页查询处理
- 异步消息处理

### 3. 安全性
- WAF安全检查
- SQL注入防护
- 用户权限验证
- 操作日志记录

### 4. 用户体验
- 响应式设计
- 现代化界面
- 实时状态反馈
- 友好错误提示

## 监控维护

### 1. 日志监控
```bash
# 查看后台处理程序日志
tail -f kunlun/logs/background_processor.log

# 查看系统错误日志
tail -f /var/log/apache2/error.log
```

### 2. 性能监控
- 数据库连接数
- SSE连接状态
- 短信发送成功率
- 磁盘空间使用

### 3. 定期维护
- 清理过期数据
- 更新系统依赖
- 备份重要数据
- 检查安全更新

## 故障排除

### 1. SSE连接问题
- 检查kafka.php是否正常运行
- 验证网络连接状态
- 查看浏览器控制台错误

### 2. 短信发送失败
- 检查SmsSystem.php配置
- 验证手机号格式
- 查看短信服务商状态

### 3. 数据库问题
- 检查数据库连接
- 验证表结构完整性
- 查看MySQL错误日志

### 4. 后台程序异常
- 检查PHP错误日志
- 验证文件权限
- 重启后台处理程序

## 版本信息

- **版本**: 1.0.0
- **开发时间**: 2025-08-12
- **PHP版本**: 7.4+
- **MySQL版本**: 5.7+
- **浏览器支持**: Chrome 60+, Firefox 55+, Safari 12+

## 联系支持

如有问题或建议，请联系系统管理员或查看：
- 系统管理器: `system_manager.php`
- 测试页面: `system_test.php`
- 错误日志: `logs/` 目录

---

**注意**: 本系统为生产环境设计，请在部署前进行充分测试，确保所有功能正常运行。
