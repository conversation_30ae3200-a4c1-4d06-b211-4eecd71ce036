# 预警系统数据库设计与开发文档

## 项目概述

基于现有的SSE实时报警监控系统，设计并开发预警人员存储系统和短信预警通知管理系统。

## 一、数据库设计

### 1.1 预警人员存储数据库

#### 表名：`alarm_records`
存储所有预警人员的详细信息，基于kafka.php中的数据结构设计。

```sql
CREATE TABLE `alarm_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `alarm_id` varchar(100) NOT NULL COMMENT '报警ID（来自Kafka数据）',
  `alarm_no` varchar(100) DEFAULT NULL COMMENT '报警编号',
  `person_name` varchar(100) DEFAULT NULL COMMENT '预警人员姓名',
  `person_sfz` varchar(18) DEFAULT NULL COMMENT '预警人员身份证号',
  `score` decimal(5,2) DEFAULT NULL COMMENT '相似度分数',
  `location_name` varchar(200) DEFAULT NULL COMMENT '位置名称',
  `latitude` decimal(10,6) DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(10,6) DEFAULT NULL COMMENT '经度',
  `image_base64` longtext COMMENT '抓拍图片Base64数据',
  `image_url` varchar(500) DEFAULT NULL COMMENT '原始图片URL',
  `album_name` varchar(100) DEFAULT NULL COMMENT '图库名称',
  `severity` enum('low','medium','high') DEFAULT 'low' COMMENT '严重级别',
  `captured_time` datetime DEFAULT NULL COMMENT '抓拍时间',
  `created_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `is_processed` tinyint(1) DEFAULT 0 COMMENT '是否已处理（0=未处理，1=已处理）',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `process_user_id` int(11) DEFAULT NULL COMMENT '处理人员ID',
  `remarks` text COMMENT '备注信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_alarm_id` (`alarm_id`),
  KEY `idx_person_sfz` (`person_sfz`),
  KEY `idx_captured_time` (`captured_time`),
  KEY `idx_severity` (`severity`),
  KEY `idx_is_processed` (`is_processed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预警人员记录表';
```

### 1.2 短信预警通知数据库

#### 表名：`sms_alert_config`
短信预警配置表，管理预警人员与短信接收人员的关系。

```sql
CREATE TABLE `sms_alert_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `alarm_person_sfz` varchar(18) NOT NULL COMMENT '预警人员身份证号',
  `alarm_person_name` varchar(100) DEFAULT NULL COMMENT '预警人员姓名',
  `recipient_user_id` int(11) NOT NULL COMMENT '短信接收人员ID（关联3_user.id）',
  `alert_level` enum('low','medium','high','all') DEFAULT 'all' COMMENT '预警级别（只有达到此级别才发送短信）',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用（0=禁用，1=启用）',
  `valid_start_time` datetime DEFAULT NULL COMMENT '有效开始时间',
  `valid_end_time` datetime DEFAULT NULL COMMENT '有效结束时间',
  `created_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` int(11) DEFAULT NULL COMMENT '创建人ID',
  `updated_by` int(11) DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`),
  KEY `idx_alarm_person_sfz` (`alarm_person_sfz`),
  KEY `idx_recipient_user_id` (`recipient_user_id`),
  KEY `idx_alert_level` (`alert_level`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_valid_time` (`valid_start_time`,`valid_end_time`),
  CONSTRAINT `fk_sms_alert_recipient` FOREIGN KEY (`recipient_user_id`) REFERENCES `3_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信预警配置表';
```

#### 表名：`sms_send_log`
短信发送记录表，记录所有短信发送的详细信息。

```sql
CREATE TABLE `sms_send_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `alarm_record_id` int(11) NOT NULL COMMENT '关联预警记录ID',
  `config_id` int(11) NOT NULL COMMENT '关联短信配置ID',
  `recipient_user_id` int(11) NOT NULL COMMENT '接收人员ID',
  `recipient_name` varchar(100) DEFAULT NULL COMMENT '接收人员姓名',
  `recipient_phone` varchar(20) DEFAULT NULL COMMENT '接收人员电话',
  `recipient_unit_id` int(11) DEFAULT NULL COMMENT '接收人员单位ID',
  `recipient_unit_name` varchar(200) DEFAULT NULL COMMENT '接收人员单位名称',
  `sms_content` text NOT NULL COMMENT '短信内容',
  `send_status` enum('pending','success','failed','expired') DEFAULT 'pending' COMMENT '发送状态',
  `send_time` datetime DEFAULT NULL COMMENT '发送时间',
  `response_message` text COMMENT '发送响应信息',
  `retry_count` int(11) DEFAULT 0 COMMENT '重试次数',
  `created_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_alarm_record_id` (`alarm_record_id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_recipient_user_id` (`recipient_user_id`),
  KEY `idx_send_status` (`send_status`),
  KEY `idx_send_time` (`send_time`),
  CONSTRAINT `fk_sms_log_alarm` FOREIGN KEY (`alarm_record_id`) REFERENCES `alarm_records` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sms_log_config` FOREIGN KEY (`config_id`) REFERENCES `sms_alert_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信发送记录表';
```

## 二、系统架构设计

### 2.1 自动存储后台程序

#### 文件：`kunlun/background_processor.php`
- **功能**：监听SSE数据流，自动将预警信息存储到数据库
- **运行方式**：后台常驻进程或定时任务
- **数据来源**：从kafka.php获取实时预警数据
- **处理逻辑**：
  1. 接收SSE事件数据
  2. 解析预警人员信息
  3. 存储到alarm_records表
  4. 触发短信预警检查

### 2.2 短信预警管理后台

#### 文件结构：
```
kunlun/sms_admin/
├── index.php              # 主页面
├── api/
│   ├── config_manage.php  # 短信配置管理API
│   ├── log_query.php      # 发送记录查询API
│   └── send_test.php      # 测试发送API
├── css/
│   └── admin.css          # 样式文件
└── js/
    └── admin.js           # 前端交互脚本
```

### 2.3 数据库关系图

```
alarm_records (预警记录)
    ↓ (一对多)
sms_send_log (发送记录)
    ↑ (多对一)
sms_alert_config (短信配置)
    ↓ (外键关联)
3_user (用户表) ← → 2_unit (单位表)
```

## 三、功能模块设计

### 3.1 预警数据自动存储模块

**核心功能：**
- 实时监听kafka.php的SSE数据流
- 自动解析预警人员信息
- 去重处理（基于alarm_id）
- 数据验证和清洗
- 异常处理和日志记录

**技术实现：**
- 使用EventSource监听SSE
- 数据库事务处理
- 错误重试机制
- 性能监控

### 3.2 短信预警配置管理模块

**核心功能：**
- 预警人员与接收人员关系配置
- 预警级别设置
- 有效期管理
- 批量导入/导出
- 配置模板

**页面功能：**
- 增加配置：选择预警人员、接收人员、级别、有效期
- 删除配置：支持单个删除和批量删除
- 修改配置：在线编辑配置信息
- 查询配置：多条件搜索和筛选

### 3.3 短信发送记录管理模块

**核心功能：**
- 发送记录查询
- 发送状态统计
- 失败重发
- 发送报表
- 数据导出

## 四、技术规范

### 4.1 开发环境
- **后端**：PHP 7.4+
- **数据库**：MySQL 5.7+
- **前端**：HTML5 + CSS3 + JavaScript (ES6)
- **依赖**：conn_waf.php（数据库连接和安全检查）

### 4.2 安全规范
- 使用conn_waf.php的WAF安全检查
- SQL预处理语句防注入
- 用户权限验证
- 数据加密存储（敏感信息）
- 操作日志记录

### 4.3 性能规范
- 数据库索引优化
- 分页查询
- 缓存机制
- 异步处理
- 资源限制

## 五、部署方案

### 5.1 数据库部署
1. 执行SQL脚本创建表结构
2. 创建必要的索引
3. 设置数据库权限
4. 配置备份策略

### 5.2 应用部署
1. 上传PHP文件到服务器
2. 配置Web服务器
3. 设置文件权限
4. 配置定时任务（后台处理程序）
5. 测试系统功能

### 5.3 监控方案
- 系统运行状态监控
- 数据库性能监控
- 短信发送成功率监控
- 错误日志监控
- 资源使用监控

## 六、测试计划

### 6.1 单元测试
- 数据库操作函数测试
- 数据验证函数测试
- 短信发送接口测试

### 6.2 集成测试
- SSE数据流处理测试
- 短信预警流程测试
- 管理后台功能测试

### 6.3 性能测试
- 高并发预警数据处理
- 大量短信发送性能
- 数据库查询性能

## 七、维护方案

### 7.1 日常维护
- 数据库清理（定期清理过期数据）
- 日志文件管理
- 系统性能优化
- 安全更新

### 7.2 故障处理
- 错误监控和报警
- 故障恢复流程
- 数据备份恢复
- 应急处理预案

---

**开发优先级：**
1. 数据库表结构创建
2. 预警数据自动存储程序
3. 短信配置管理后台
4. 短信发送功能
5. 监控和优化

**预计开发周期：** 5-7个工作日

请确认此开发文档是否符合您的需求，如有需要修改的地方请告知，确认无误后我将开始编写代码。
